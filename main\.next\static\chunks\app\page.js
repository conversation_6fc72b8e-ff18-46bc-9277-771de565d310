/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./app/components/DashboardStats.tsx":
/*!*******************************************!*\
  !*** ./app/components/DashboardStats.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction DashboardStats(param) {\n    let { totalIncome, totalExpenses, totalInvestments, totalInvestmentProfitLoss, currentBalance } = param;\n    const stats = [\n        {\n            title: 'Tổng thu nhập',\n            value: totalIncome,\n            icon: 'ri-arrow-up-circle-fill',\n            color: 'text-green-600',\n            bgColor: 'bg-green-50'\n        },\n        {\n            title: 'Tổng chi tiêu',\n            value: totalExpenses,\n            icon: 'ri-arrow-down-circle-fill',\n            color: 'text-red-600',\n            bgColor: 'bg-red-50'\n        },\n        {\n            title: 'Tổng đầu tư',\n            value: totalInvestments,\n            icon: 'ri-line-chart-fill',\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-50'\n        },\n        {\n            title: 'Tổng lãi/lỗ đầu tư',\n            value: totalInvestmentProfitLoss,\n            icon: totalInvestmentProfitLoss >= 0 ? 'ri-trend-up-fill' : 'ri-trend-down-fill',\n            color: totalInvestmentProfitLoss >= 0 ? 'text-green-600' : 'text-red-600',\n            bgColor: totalInvestmentProfitLoss >= 0 ? 'bg-green-50' : 'bg-red-50'\n        },\n        {\n            title: 'Số dư hiện tại',\n            value: currentBalance,\n            icon: 'ri-wallet-3-fill',\n            color: currentBalance >= 0 ? 'text-green-600' : 'text-red-600',\n            bgColor: currentBalance >= 0 ? 'bg-green-50' : 'bg-red-50'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-600\",\n                                    children: stat.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold \".concat(stat.color),\n                                    children: [\n                                        stat.value >= 0 && stat.title === 'Tổng lãi/lỗ đầu tư' ? '+' : '',\n                                        stat.value.toLocaleString(),\n                                        \"₫\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 flex items-center justify-center rounded-lg \".concat(stat.bgColor),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"\".concat(stat.icon, \" \").concat(stat.color, \" text-xl\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this)\n            }, index, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_c = DashboardStats;\nvar _c;\n$RefreshReg$(_c, \"DashboardStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/DashboardStats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/SavingsBooks.tsx":
/*!*****************************************!*\
  !*** ./app/components/SavingsBooks.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SavingsBooks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction SavingsBooks(param) {\n    let { savingsBooks, onAdd, onUpdate, onDelete, onAddExpense, isPreview = false } = param;\n    _s();\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExpenseForm, setShowExpenseForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showExpenseList, setShowExpenseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newBook, setNewBook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        initialAmount: '',\n        description: ''\n    });\n    const [newExpense, setNewExpense] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: '',\n        description: ''\n    });\n    const handleAddBook = (e)=>{\n        e.preventDefault();\n        if (!newBook.name || !newBook.initialAmount) return;\n        const amount = parseFloat(newBook.initialAmount);\n        onAdd({\n            name: newBook.name,\n            initialAmount: amount,\n            currentAmount: amount,\n            description: newBook.description,\n            createdDate: new Date().toISOString().split('T')[0]\n        });\n        setNewBook({\n            name: '',\n            initialAmount: '',\n            description: ''\n        });\n        setShowAddForm(false);\n    };\n    const handleAddExpense = (e, bookId)=>{\n        e.preventDefault();\n        if (!newExpense.amount || !newExpense.description) return;\n        const amount = parseFloat(newExpense.amount);\n        const book = savingsBooks.find((b)=>b.id === bookId);\n        if (!book || book.currentAmount < amount) return;\n        onAddExpense(bookId, {\n            amount,\n            description: newExpense.description,\n            date: new Date().toISOString().split('T')[0]\n        });\n        // Update current amount\n        onUpdate(bookId, {\n            currentAmount: book.currentAmount - amount\n        });\n        setNewExpense({\n            amount: '',\n            description: ''\n        });\n        setShowExpenseForm(null);\n    };\n    const formatNumber = (num)=>{\n        return num.replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');\n    };\n    const parseNumber = (str)=>{\n        return str.replace(/\\./g, '');\n    };\n    const handleInitialAmountChange = (value)=>{\n        // Remove all non-numeric characters except dots\n        const numericValue = value.replace(/[^0-9]/g, '');\n        setNewBook({\n            ...newBook,\n            initialAmount: numericValue\n        });\n    };\n    const handleExpenseAmountChange = (value)=>{\n        // Remove all non-numeric characters except dots\n        const numericValue = value.replace(/[^0-9]/g, '');\n        setNewExpense({\n            ...newExpense,\n            amount: numericValue\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Sổ tiết kiệm\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddForm(true),\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-add-line mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            \"Th\\xeam sổ mới\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this),\n            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Th\\xeam sổ tiết kiệm mới\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleAddBook,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"T\\xean sổ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newBook.name,\n                                                onChange: (e)=>setNewBook({\n                                                        ...newBook,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                                placeholder: \"V\\xed dụ: Sổ du lịch\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Số tiền ban đầu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newBook.initialAmount ? formatNumber(newBook.initialAmount) : '',\n                                                        onChange: (e)=>handleInitialAmountChange(e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                                        placeholder: \"0\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                                        children: \"₫\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"M\\xf4 tả\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: newBook.description,\n                                        onChange: (e)=>setNewBook({\n                                                ...newBook,\n                                                description: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                        placeholder: \"M\\xf4 tả mục đ\\xedch sử dụng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                        children: \"Th\\xeam sổ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddForm(false),\n                                        className: \"bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                        children: \"Hủy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 \".concat(isPreview ? 'md:grid-cols-1' : 'md:grid-cols-2 lg:grid-cols-3', \" gap-6\"),\n                children: savingsBooks.map((book)=>{\n                    const totalExpenses = book.expenses.reduce((sum, expense)=>sum + expense.amount, 0);\n                    const isEmptyBook = book.currentAmount <= 0;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6 \".concat(isEmptyBook ? 'border-2 border-red-200' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: book.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onDelete(book.id),\n                                        className: \"w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-delete-bin-line text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this),\n                            isEmptyBook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-alert-line text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-700 font-medium text-sm\",\n                                            children: \"Sổ đ\\xe3 tất to\\xe1n\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Số tiền ban đầu:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: [\n                                                    book.initialAmount.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Đ\\xe3 chi ti\\xeau:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-red-600\",\n                                                children: [\n                                                    totalExpenses.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"C\\xf2n lại:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold \".concat(isEmptyBook ? 'text-red-600' : 'text-green-600'),\n                                                children: [\n                                                    book.currentAmount.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this),\n                            book.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mt-3 italic\",\n                                children: book.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        !isEmptyBook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowExpenseForm(showExpenseForm === book.id ? null : book.id),\n                                            className: \"flex-1 bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-subtract-line mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"R\\xfat tiền\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 21\n                                        }, this),\n                                        book.expenses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowExpenseList(showExpenseList === book.id ? null : book.id),\n                                            className: \"flex-1 bg-gray-600 text-white px-3 py-2 rounded-md hover:bg-gray-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-list-check mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Lịch sử\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this),\n                            showExpenseForm === book.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"R\\xfat tiền từ sổ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: (e)=>handleAddExpense(e, book.id),\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Số tiền r\\xfat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: newExpense.amount ? formatNumber(newExpense.amount) : '',\n                                                                onChange: (e)=>handleExpenseAmountChange(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                                                placeholder: \"0\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                                                children: \"₫\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            \"Tối đa: \",\n                                                            book.currentAmount.toLocaleString(),\n                                                            \"₫\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Mục đ\\xedch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newExpense.description,\n                                                        onChange: (e)=>setNewExpense({\n                                                                ...newExpense,\n                                                                description: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                                        placeholder: \"Để l\\xe0m g\\xec?\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        className: \"bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                                        children: \"R\\xfat tiền\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowExpenseForm(null),\n                                                        className: \"bg-gray-300 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-400 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                                        children: \"Hủy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 17\n                            }, this),\n                            showExpenseList === book.id && book.expenses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Lịch sử chi ti\\xeau\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-48 overflow-y-auto\",\n                                        children: book.expenses.map((expense)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: expense.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(expense.date).toLocaleDateString('vi-VN')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-red-600\",\n                                                        children: [\n                                                            \"-\",\n                                                            expense.amount.toLocaleString(),\n                                                            \"₫\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, expense.id, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, book.id, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            savingsBooks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-book-line text-4xl mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mb-2\",\n                        children: \"Chưa c\\xf3 sổ tiết kiệm n\\xe0o\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: \"Tạo sổ tiết kiệm đầu ti\\xean để bắt đầu tiết kiệm\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this),\n                    !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddForm(true),\n                        className: \"mt-4 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-add-line mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, this),\n                            \"Th\\xeam sổ tiết kiệm\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this),\n            isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowAddForm(true),\n                    className: \"w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 shadow-lg cursor-pointer flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-add-line text-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(SavingsBooks, \"PsRbnExu9LXB8uRWQBpl3tjzb54=\");\n_c = SavingsBooks;\nvar _c;\n$RefreshReg$(_c, \"SavingsBooks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/SavingsBooks.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/TransactionForm.tsx":
/*!********************************************!*\
  !*** ./app/components/TransactionForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransactionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TransactionForm(param) {\n    let { onSubmit } = param;\n    _s();\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('income');\n    const [amount, setAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [displayAmount, setDisplayAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [date, setDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().toISOString().split('T')[0]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Investment profit/loss fields\n    const [profitLoss, setProfitLoss] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [displayProfitLoss, setDisplayProfitLoss] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [period, setPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('month');\n    const [showProfitLossSuggestions, setShowProfitLossSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const formatNumber = (num)=>{\n        return num.replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');\n    };\n    const parseNumber = (str)=>{\n        return str.replace(/\\./g, '');\n    };\n    const generateSuggestions = (input)=>{\n        const baseNum = parseNumber(input);\n        if (!baseNum || isNaN(Number(baseNum))) return [];\n        const suggestions = [];\n        const base = Number(baseNum);\n        if (base < 10) {\n            suggestions.push(base * 1000);\n            suggestions.push(base * 10000);\n            suggestions.push(base * 100000);\n            suggestions.push(base * 500000);\n            suggestions.push(base * 1000000);\n        } else if (base < 100) {\n            suggestions.push(base * 100);\n            suggestions.push(base * 1000);\n            suggestions.push(base * 10000);\n            suggestions.push(base * 50000);\n        } else if (base < 1000) {\n            suggestions.push(base * 10);\n            suggestions.push(base * 100);\n            suggestions.push(base * 1000);\n        } else {\n            suggestions.push(base);\n            suggestions.push(base * 10);\n            suggestions.push(base * 100);\n        }\n        return suggestions.filter((s)=>s > 0).slice(0, 5);\n    };\n    const handleAmountChange = (e)=>{\n        const value = e.target.value;\n        const numericValue = parseNumber(value);\n        if (numericValue === '' || /^\\d+$/.test(numericValue)) {\n            setDisplayAmount(numericValue ? formatNumber(numericValue) : '');\n            setAmount(numericValue);\n            setShowSuggestions(numericValue.length > 0);\n        }\n    };\n    const handleProfitLossChange = (e)=>{\n        const value = e.target.value;\n        let numericValue = parseNumber(value);\n        // Allow negative numbers for losses\n        if (value.startsWith('-')) {\n            numericValue = '-' + numericValue;\n        }\n        if (numericValue === '' || /^-?\\d+$/.test(numericValue)) {\n            setDisplayProfitLoss(numericValue ? numericValue.startsWith('-') ? '-' + formatNumber(numericValue.slice(1)) : formatNumber(numericValue) : '');\n            setProfitLoss(numericValue);\n            setShowProfitLossSuggestions(Math.abs(parseFloat(numericValue || '0')) > 0);\n        }\n    };\n    const selectSuggestion = (suggestion)=>{\n        setAmount(suggestion.toString());\n        setDisplayAmount(formatNumber(suggestion.toString()));\n        setShowSuggestions(false);\n    };\n    const selectProfitLossSuggestion = (suggestion)=>{\n        setProfitLoss(suggestion.toString());\n        setDisplayProfitLoss(suggestion < 0 ? '-' + formatNumber(Math.abs(suggestion).toString()) : formatNumber(suggestion.toString()));\n        setShowProfitLossSuggestions(false);\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!amount || !category || !description) return;\n        const transaction = {\n            type,\n            amount: parseFloat(amount),\n            category,\n            description,\n            date\n        };\n        // Add investment data if it's an investment transaction\n        if (type === 'investment' && profitLoss) {\n            transaction.investmentData = {\n                profitLoss: parseFloat(profitLoss),\n                period,\n                lastUpdated: new Date().toISOString()\n            };\n        }\n        onSubmit(transaction);\n        // Reset form\n        setAmount('');\n        setDisplayAmount('');\n        setCategory('');\n        setDescription('');\n        setDate(new Date().toISOString().split('T')[0]);\n        setProfitLoss('');\n        setDisplayProfitLoss('');\n        setPeriod('month');\n        setShowSuggestions(false);\n        setShowProfitLossSuggestions(false);\n    };\n    const categories = {\n        income: [\n            'Lương',\n            'Freelance',\n            'Kinh doanh',\n            'Lợi nhuận đầu tư',\n            'Khác'\n        ],\n        expense: [\n            'Tiền nhà',\n            'Thực phẩm',\n            'Giao thông',\n            'Giải trí',\n            'Y tế',\n            'Khác'\n        ],\n        investment: [\n            'Cổ phiếu',\n            'Trái phiếu',\n            'Bất động sản',\n            'Tiền điện tử',\n            'Quỹ tương hỗ',\n            'Khác'\n        ]\n    };\n    const typeLabels = {\n        income: 'Thu nhập',\n        expense: 'Chi tiêu',\n        investment: 'Đầu tư'\n    };\n    const periodLabels = {\n        week: 'Tuần',\n        month: 'Tháng',\n        year: 'Năm'\n    };\n    const suggestions = generateSuggestions(displayAmount);\n    const profitLossSuggestions = generateSuggestions(displayProfitLoss.replace('-', ''));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Loại\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    'income',\n                                    'expense',\n                                    'investment'\n                                ].map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setType(t),\n                                        className: \"px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap cursor-pointer \".concat(type === t ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'),\n                                        children: typeLabels[t]\n                                    }, t, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Số tiền\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: displayAmount,\n                                        onChange: handleAmountChange,\n                                        onFocus: ()=>setShowSuggestions(displayAmount.length > 0),\n                                        onBlur: ()=>setTimeout(()=>setShowSuggestions(false), 200),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                        placeholder: \"0\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                        children: \"₫\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            showSuggestions && suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto\",\n                                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>selectSuggestion(suggestion),\n                                        className: \"w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                        children: [\n                                            formatNumber(suggestion.toString()),\n                                            \"₫\"\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            type === 'investment' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"L\\xe3i/Lỗ\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: displayProfitLoss,\n                                        onChange: handleProfitLossChange,\n                                        onFocus: ()=>setShowProfitLossSuggestions(Math.abs(parseFloat(profitLoss || '0')) > 0),\n                                        onBlur: ()=>setTimeout(()=>setShowProfitLossSuggestions(false), 200),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                        placeholder: \"0 (nhập số \\xe2m nếu lỗ)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                        children: \"₫\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            showProfitLossSuggestions && profitLossSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto\",\n                                children: profitLossSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>selectProfitLossSuggestion(suggestion),\n                                                className: \"w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 text-green-600\",\n                                                children: [\n                                                    \"+\",\n                                                    formatNumber(suggestion.toString()),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>selectProfitLossSuggestion(-suggestion),\n                                                className: \"w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 last:border-b-0 text-red-600\",\n                                                children: [\n                                                    \"-\",\n                                                    formatNumber(suggestion.toString()),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Chu kỳ\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    'week',\n                                    'month',\n                                    'year'\n                                ].map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setPeriod(p),\n                                        className: \"px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap cursor-pointer \".concat(period === p ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'),\n                                        children: periodLabels[p]\n                                    }, p, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Danh mục\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: category,\n                                onChange: (e)=>setCategory(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8\",\n                                required: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Chọn danh mục\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this),\n                                    categories[type].map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: cat,\n                                            children: cat\n                                        }, cat, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Ng\\xe0y\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: date,\n                                onChange: (e)=>setDate(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"M\\xf4 tả\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: description,\n                        onChange: (e)=>setDescription(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                        placeholder: \"Nhập m\\xf4 tả\",\n                        required: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"submit\",\n                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium whitespace-nowrap cursor-pointer\",\n                children: \"Th\\xeam giao dịch\"\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionForm, \"8oYWrqN97XwEhASPwCW/CRLh2HU=\");\n_c = TransactionForm;\nvar _c;\n$RefreshReg$(_c, \"TransactionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/TransactionForm.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/TransactionList.tsx":
/*!********************************************!*\
  !*** ./app/components/TransactionList.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransactionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction TransactionList(param) {\n    let { transactions, onDelete } = param;\n    const getTypeColor = (type)=>{\n        switch(type){\n            case 'income':\n                return 'text-green-600 bg-green-50';\n            case 'expense':\n                return 'text-red-600 bg-red-50';\n            case 'investment':\n                return 'text-blue-600 bg-blue-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case 'income':\n                return 'ri-arrow-up-circle-fill';\n            case 'expense':\n                return 'ri-arrow-down-circle-fill';\n            case 'investment':\n                return 'ri-line-chart-fill';\n            default:\n                return 'ri-money-dollar-circle-fill';\n        }\n    };\n    const getTypeLabel = (type)=>{\n        switch(type){\n            case 'income':\n                return 'Thu nhập';\n            case 'expense':\n                return 'Chi tiêu';\n            case 'investment':\n                return 'Đầu tư';\n            default:\n                return type;\n        }\n    };\n    const getPeriodLabel = (period)=>{\n        switch(period){\n            case 'week':\n                return 'Tuần';\n            case 'month':\n                return 'Tháng';\n            case 'year':\n                return 'Năm';\n            default:\n                return period;\n        }\n    };\n    if (transactions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"ri-inbox-line text-4xl mb-2\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Chưa c\\xf3 giao dịch n\\xe0o\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Loại\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Số tiền\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"L\\xe3i/Lỗ\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Danh mục\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"M\\xf4 tả\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Ng\\xe0y\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Thao t\\xe1c\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"border-b border-gray-100 hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center rounded-full \".concat(getTypeColor(transaction.type)),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"\".concat(getTypeIcon(transaction.type), \" text-sm\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: getTypeLabel(transaction.type)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium \".concat(transaction.type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                        children: [\n                                            transaction.type === 'income' ? '+' : '-',\n                                            transaction.amount.toLocaleString(),\n                                            \"₫\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: transaction.investmentData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-sm \".concat(transaction.investmentData.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                children: [\n                                                    transaction.investmentData.profitLoss >= 0 ? '+' : '',\n                                                    transaction.investmentData.profitLoss.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"/\",\n                                                    getPeriodLabel(transaction.investmentData.period)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4 text-sm text-gray-900\",\n                                    children: transaction.category\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4 text-sm text-gray-600\",\n                                    children: transaction.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4 text-sm text-gray-600\",\n                                    children: new Date(transaction.date).toLocaleDateString('vi-VN')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onDelete(transaction.id),\n                                        className: \"w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-delete-bin-line text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, transaction.id, true, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_c = TransactionList;\nvar _c;\n$RefreshReg$(_c, \"TransactionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/TransactionList.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardStats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/DashboardStats */ \"(app-pages-browser)/./app/components/DashboardStats.tsx\");\n/* harmony import */ var _components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/TransactionForm */ \"(app-pages-browser)/./app/components/TransactionForm.tsx\");\n/* harmony import */ var _components_TransactionList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/TransactionList */ \"(app-pages-browser)/./app/components/TransactionList.tsx\");\n/* harmony import */ var _components_SavingsBooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/SavingsBooks */ \"(app-pages-browser)/./app/components/SavingsBooks.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [savingsBooks, setSavingsBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    // Load data from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const savedTransactions = localStorage.getItem('finance-transactions');\n            const savedSavingsBooks = localStorage.getItem('finance-savings-books');\n            if (savedTransactions) {\n                setTransactions(JSON.parse(savedTransactions));\n            } else {\n                // Default transactions if no saved data\n                const defaultTransactions = [\n                    {\n                        id: '1',\n                        type: 'income',\n                        amount: 5000,\n                        category: 'Lương',\n                        description: 'Lương tháng',\n                        date: '2024-01-15'\n                    },\n                    {\n                        id: '2',\n                        type: 'expense',\n                        amount: 1200,\n                        category: 'Tiền nhà',\n                        description: 'Tiền thuê nhà hàng tháng',\n                        date: '2024-01-01'\n                    },\n                    {\n                        id: '3',\n                        type: 'expense',\n                        amount: 300,\n                        category: 'Thực phẩm',\n                        description: 'Mua sắm hàng tuần',\n                        date: '2024-01-08'\n                    },\n                    {\n                        id: '4',\n                        type: 'investment',\n                        amount: 2000,\n                        category: 'Cổ phiếu',\n                        description: 'Mua cổ phiếu công nghệ',\n                        date: '2024-01-10',\n                        investmentData: {\n                            profitLoss: 150,\n                            period: 'month',\n                            lastUpdated: '2024-01-20T10:00:00Z'\n                        }\n                    },\n                    {\n                        id: '5',\n                        type: 'income',\n                        amount: 500,\n                        category: 'Freelance',\n                        description: 'Hoàn thành dự án',\n                        date: '2024-01-12'\n                    },\n                    {\n                        id: '6',\n                        type: 'investment',\n                        amount: 1500,\n                        category: 'Tiền điện tử',\n                        description: 'Mua Bitcoin',\n                        date: '2024-01-18',\n                        investmentData: {\n                            profitLoss: -200,\n                            period: 'week',\n                            lastUpdated: '2024-01-25T15:30:00Z'\n                        }\n                    }\n                ];\n                setTransactions(defaultTransactions);\n            }\n            if (savedSavingsBooks) {\n                setSavingsBooks(JSON.parse(savedSavingsBooks));\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    // Save data to localStorage whenever transactions or savings books change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            localStorage.setItem('finance-transactions', JSON.stringify(transactions));\n        }\n    }[\"Home.useEffect\"], [\n        transactions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            localStorage.setItem('finance-savings-books', JSON.stringify(savingsBooks));\n        }\n    }[\"Home.useEffect\"], [\n        savingsBooks\n    ]);\n    const addTransaction = (transaction)=>{\n        const newTransaction = {\n            ...transaction,\n            id: Date.now().toString()\n        };\n        setTransactions([\n            newTransaction,\n            ...transactions\n        ]);\n    };\n    const deleteTransaction = (id)=>{\n        setTransactions(transactions.filter((t)=>t.id !== id));\n    };\n    const addSavingsBook = (book)=>{\n        const newBook = {\n            ...book,\n            id: Date.now().toString(),\n            expenses: []\n        };\n        setSavingsBooks([\n            ...savingsBooks,\n            newBook\n        ]);\n    };\n    const updateSavingsBook = (id, updates)=>{\n        setSavingsBooks(savingsBooks.map((book)=>book.id === id ? {\n                ...book,\n                ...updates\n            } : book));\n    };\n    const deleteSavingsBook = (id)=>{\n        setSavingsBooks(savingsBooks.filter((book)=>book.id !== id));\n    };\n    const addExpenseToSavingsBook = (bookId, expense)=>{\n        setSavingsBooks(savingsBooks.map((book)=>{\n            if (book.id === bookId) {\n                const newExpense = {\n                    ...expense,\n                    id: Date.now().toString()\n                };\n                return {\n                    ...book,\n                    expenses: [\n                        ...book.expenses,\n                        newExpense\n                    ]\n                };\n            }\n            return book;\n        }));\n    };\n    const totalIncome = transactions.filter((t)=>t.type === 'income').reduce((sum, t)=>sum + t.amount, 0);\n    const totalExpenses = transactions.filter((t)=>t.type === 'expense').reduce((sum, t)=>sum + t.amount, 0);\n    const totalInvestments = transactions.filter((t)=>t.type === 'investment').reduce((sum, t)=>sum + t.amount, 0);\n    // Calculate total profit/loss from investments\n    const totalInvestmentProfitLoss = transactions.filter((t)=>t.type === 'investment' && t.investmentData).reduce((sum, t)=>{\n        var _t_investmentData;\n        return sum + (((_t_investmentData = t.investmentData) === null || _t_investmentData === void 0 ? void 0 : _t_investmentData.profitLoss) || 0);\n    }, 0);\n    // Calculate current balance including investment profit/loss\n    const currentBalance = totalIncome - totalExpenses - totalInvestments + totalInvestmentProfitLoss;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 flex items-center justify-center bg-blue-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-wallet-3-line text-white text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Quản l\\xfd t\\xe0i ch\\xednh c\\xe1 nh\\xe2n\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Số dư hiện tại\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold \".concat(currentBalance >= 0 ? 'text-green-600' : 'text-red-600'),\n                                        children: [\n                                            currentBalance.toLocaleString(),\n                                            \"₫\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('dashboard'),\n                                className: \"py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer \".concat(activeTab === 'dashboard' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                                children: \"Tổng quan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('transactions'),\n                                className: \"py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer \".concat(activeTab === 'transactions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                                children: \"Giao dịch\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('savings'),\n                                className: \"py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer \".concat(activeTab === 'savings' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                                children: \"Sổ tiết kiệm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardStats__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                totalIncome: totalIncome,\n                                totalExpenses: totalExpenses,\n                                totalInvestments: totalInvestments,\n                                totalInvestmentProfitLoss: totalInvestmentProfitLoss,\n                                currentBalance: currentBalance\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Th\\xeam giao dịch mới\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                onSubmit: addTransaction\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Sổ tiết kiệm của bạn\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SavingsBooks__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                savingsBooks: savingsBooks.slice(0, 3),\n                                                onAdd: addSavingsBook,\n                                                onUpdate: updateSavingsBook,\n                                                onDelete: deleteSavingsBook,\n                                                onAddExpense: addExpenseToSavingsBook,\n                                                isPreview: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            savingsBooks.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('savings'),\n                                                className: \"mt-4 text-blue-600 hover:text-blue-800 text-sm cursor-pointer\",\n                                                children: \"Xem tất cả sổ tiết kiệm →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Giao dịch gần đ\\xe2y\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            transactions: transactions.slice(0, 5),\n                                            onDelete: deleteTransaction\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'transactions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Th\\xeam giao dịch mới\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onSubmit: addTransaction\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Tất cả giao dịch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            transactions: transactions,\n                                            onDelete: deleteTransaction\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'savings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Quản l\\xfd sổ tiết kiệm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SavingsBooks__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    savingsBooks: savingsBooks,\n                                    onAdd: addSavingsBook,\n                                    onUpdate: updateSavingsBook,\n                                    onDelete: deleteSavingsBook,\n                                    onAddExpense: addExpenseToSavingsBook,\n                                    isPreview: false\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"tBaCn5YjJa6dmdhG9V6qfBGw8eM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(app-pages-browser)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0FwcCUyMHF1JUUxJUJBJUEzbiUyMGwlQzMlQkQlMjBjaGklMjB0aSVDMyVBQXUlNUMlNUNhcHAlMjBjJUUxJUJCJUE3YSUyMG0lRTElQkElQjklNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLHNKQUF3RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQXBwIHF14bqjbiBsw70gY2hpIHRpw6p1XFxcXGFwcCBj4bunYSBt4bq5XFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxBcHAgcXXhuqNuIGzDvSBjaGkgdGnDqnVcXGFwcCBj4bunYSBt4bq5XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);