{"name": "@react-google-maps/api", "sideEffects": false, "version": "2.20.7", "description": "React.js Google Maps API integration", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/JustFly1984"}, "contributors": ["<PERSON><PERSON> <<EMAIL>> (https://github.com/uriklar)", "<PERSON><PERSON>t <PERSON>wwit <<EMAIL>> (https://github.com/awwit)", "<PERSON> <ivan<PERSON><PERSON>@gmail.com> (https://github.com/ivankonev)", "<PERSON> <<EMAIL>> (https://github.com/fredyc)", "<PERSON> <<EMAIL>> (https://github.com/thekevinbrown)"], "repository": {"type": "git", "url": "https://github.com/JustFly1984/react-google-maps-api.git", "directory": "packages/react-google-maps-api"}, "bugs": {"url": "https://github.com/JustFly1984/react-google-maps-api/issues"}, "homepage": "https://react-google-maps-api-docs.netlify.app", "publishConfig": {"access": "public"}, "main": "dist/cjs.js", "types": "dist/index.d.ts", "unpkg": "dist/umd.min.js", "module": "dist/esm.js", "files": ["src/", "dist/"], "keywords": ["React", "Google", "Google Maps", "google maps", "google-maps", "@google-maps", "google-maps-api", "@google-maps-api", "Map", "Maps", "API", "GoogleMap", "react-component", "addons/MarkerClusterer", "directions/DirectionsR<PERSON>er", "directions/DirectionsService", "drawing/DrawingManager", "places/SearchBox", "InfoWindow", "KmlLayer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OverlayView", "Circle", "Polygon", "Polyline", "Rectangle", "StreetViewPanorama", "TrafficLayer", "visualization/HeatmapLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Typescript"], "resolutions": {"@types/node": "22.7.5", "@types/react": "18.3.11", "@types/react-dom": "18.3.0"}, "dependencies": {"@googlemaps/js-api-loader": "1.16.8", "@googlemaps/markerclusterer": "2.5.3", "@react-google-maps/infobox": "2.20.0", "@react-google-maps/marker-clusterer": "2.20.0", "@types/google.maps": "3.58.1", "invariant": "2.2.4"}, "peerDependencies": {"react": "^16.8 || ^17 || ^18 || ^19", "react-dom": "^16.8 || ^17 || ^18 || ^19"}, "devDependencies": {"@babel/core": "7.25.7", "@babel/plugin-proposal-decorators": "7.25.7", "@babel/plugin-proposal-export-default-from": "7.25.7", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-jsx": "7.25.7", "@babel/plugin-syntax-typescript": "7.25.7", "@babel/plugin-transform-arrow-functions": "7.25.7", "@babel/plugin-transform-class-properties": "7.25.7", "@babel/plugin-transform-classes": "7.25.7", "@babel/plugin-transform-destructuring": "7.25.7", "@babel/plugin-transform-for-of": "7.25.7", "@babel/plugin-transform-parameters": "7.25.7", "@babel/plugin-transform-react-jsx": "7.25.7", "@babel/plugin-transform-runtime": "7.25.7", "@babel/plugin-transform-spread": "7.25.7", "@babel/plugin-transform-typescript": "7.25.7", "@babel/preset-env": "7.25.7", "@babel/preset-react": "7.25.7", "@babel/preset-typescript": "7.25.7", "@babel/runtime": "7.25.7", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "28.0.0", "@rollup/plugin-node-resolve": "15.3.0", "@rollup/plugin-terser": "0.4.4", "@rollup/plugin-typescript": "12.1.0", "@testing-library/react": "16.0.1", "babel-loader": "9.2.1", "css-loader": "^7.1.2", "jest": "29.7.0", "jest-cli": "29.7.0", "process": "^0.11.10", "react-docgen-typescript": "^2.2.2", "react-styleguidist": "^13.1.4", "rimraf": "6.0.1", "rollup": "4.24.0", "rollup-plugin-dts": "6.1.1", "style-loader": "^4.0.0", "ts-loader": "9.5.1", "tslib": "2.7.0", "webpack": "5.95.0", "webpack-cli": "^6.0.1"}, "gitHead": "80167ddcc3d8e356dbf0b0c3a6292c6a3a989f83", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup --config", "clean": "rimraf ./package-lock.json ./pnpm-lock.yml ./node_modules/ && pnpm install", "lint": "pnpx eslint ./src/**/*.{ts,tsx} --fix", "docs:dev": "pnpm exec styleguidist server --config ./styleguide.config.js --verbose", "docs:build": "pnpm exec styleguidist build --config ./styleguide.config.js --verbose", "tc": "tsc -p ./tsconfig.json --noEmit --traceResolution", "test": "jest", "pub": "pnpm publish .", "pub:next": "pnpm publish . --tag next"}}