{"version": 3, "file": "esm.min.js", "sources": ["../src/ClusterIcon.tsx", "../src/Cluster.tsx", "../src/Clusterer.tsx"], "sourcesContent": ["/* global google */\n/* eslint-disable filenames/match-regex */\nimport type { Cluster } from './Cluster'\n\nimport type { ClusterIconStyle, ClusterIconInfo } from './types'\n\nexport class ClusterIcon {\n  cluster: Cluster\n  className: string\n  clusterClassName: string\n  styles: ClusterIconStyle[]\n  center: google.maps.LatLng | undefined\n  div: HTMLDivElement | null\n  sums: ClusterIconInfo | null\n  visible: boolean\n  url: string\n  height: number\n  width: number\n  anchorText: [number, number]\n  anchorIcon: [number, number]\n  textColor: string\n  textSize: number\n  textDecoration: string\n  fontWeight: string\n  fontStyle: string\n  fontFamily: string\n  backgroundPosition: string\n  cMouseDownInCluster: boolean | null\n  cDraggingMapByCluster: boolean | null\n  timeOut: number | null\n\n  boundsChangedListener: google.maps.MapsEventListener | null\n\n  constructor(cluster: Cluster, styles: ClusterIconStyle[]) {\n    cluster.getClusterer().extend(ClusterIcon, google.maps.OverlayView)\n\n    this.cluster = cluster\n\n    this.clusterClassName = this.cluster.getClusterer().getClusterClass()\n\n    this.className = this.clusterClassName\n\n    this.styles = styles\n\n    this.center = undefined\n\n    this.div = null\n\n    this.sums = null\n\n    this.visible = false\n\n    this.boundsChangedListener = null\n\n    this.url = ''\n\n    this.height = 0\n    this.width = 0\n\n    this.anchorText = [0, 0]\n    this.anchorIcon = [0, 0]\n\n    this.textColor = 'black'\n    this.textSize = 11\n    this.textDecoration = 'none'\n    this.fontWeight = 'bold'\n    this.fontStyle = 'normal'\n    this.fontFamily = 'Arial,sans-serif'\n\n    this.backgroundPosition = '0 0'\n\n    this.cMouseDownInCluster = null\n    this.cDraggingMapByCluster = null\n    this.timeOut = null;\n\n    (this as unknown as google.maps.OverlayView).setMap(cluster.getMap()) // Note: this causes onAdd to be called\n\n    this.onBoundsChanged = this.onBoundsChanged.bind(this)\n    this.onMouseDown = this.onMouseDown.bind(this)\n    this.onClick = this.onClick.bind(this)\n    this.onMouseOver = this.onMouseOver.bind(this)\n    this.onMouseOut = this.onMouseOut.bind(this)\n    this.onAdd = this.onAdd.bind(this)\n    this.onRemove = this.onRemove.bind(this)\n    this.draw = this.draw.bind(this)\n    this.hide = this.hide.bind(this)\n    this.show = this.show.bind(this)\n    this.useStyle = this.useStyle.bind(this)\n    this.setCenter = this.setCenter.bind(this)\n    this.getPosFromLatLng = this.getPosFromLatLng.bind(this)\n  }\n\n  onBoundsChanged() {\n    this.cDraggingMapByCluster = this.cMouseDownInCluster\n  }\n\n  onMouseDown() {\n    this.cMouseDownInCluster = true\n\n    this.cDraggingMapByCluster = false\n  }\n\n  onClick(event: Event) {\n    this.cMouseDownInCluster = false\n\n    if (!this.cDraggingMapByCluster) {\n      const markerClusterer = this.cluster.getClusterer()\n\n      /**\n       * This event is fired when a cluster marker is clicked.\n       * @name MarkerClusterer#click\n       * @param {Cluster} c The cluster that was clicked.\n       * @event\n       */\n      google.maps.event.trigger(markerClusterer, 'click', this.cluster)\n      google.maps.event.trigger(markerClusterer, 'clusterclick', this.cluster) // deprecated name\n\n      // The default click handler follows. Disable it by setting\n      // the zoomOnClick property to false.\n      if (markerClusterer.getZoomOnClick()) {\n        // Zoom into the cluster.\n        const maxZoom = markerClusterer.getMaxZoom()\n\n        const bounds = this.cluster.getBounds()\n\n        const map = (markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n        if (map !== null && 'fitBounds' in map) {\n          map.fitBounds(bounds)\n        }\n\n\n        // There is a fix for Issue 170 here:\n        this.timeOut = window.setTimeout(() => {\n          const map = (markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n          if (map !== null) {\n            if ('fitBounds' in map) {\n              map.fitBounds(bounds)\n            }\n\n            const zoom = map.getZoom() || 0\n\n            // Don't zoom beyond the max zoom level\n            if (\n              maxZoom !== null &&\n              zoom > maxZoom\n            ) {\n              map.setZoom(maxZoom + 1)\n            }\n          }\n        }, 100)\n      }\n\n      // Prevent event propagation to the map:\n      event.cancelBubble = true\n\n      if (event.stopPropagation) {\n        event.stopPropagation()\n      }\n    }\n  }\n\n  onMouseOver() {\n    /**\n     * This event is fired when the mouse moves over a cluster marker.\n     * @name MarkerClusterer#mouseover\n     * @param {Cluster} c The cluster that the mouse moved over.\n     * @event\n     */\n    google.maps.event.trigger(\n      this.cluster.getClusterer(),\n      'mouseover',\n      this.cluster\n    )\n  }\n\n  onMouseOut() {\n    /**\n     * This event is fired when the mouse moves out of a cluster marker.\n     * @name MarkerClusterer#mouseout\n     * @param {Cluster} c The cluster that the mouse moved out of.\n     * @event\n     */\n    google.maps.event.trigger(\n      this.cluster.getClusterer(),\n      'mouseout',\n      this.cluster\n    )\n  }\n\n  onAdd() {\n    this.div = document.createElement('div')\n\n    this.div.className = this.className\n\n    if (this.visible) {\n      this.show()\n    }\n\n    ;(this as unknown as google.maps.OverlayView).getPanes()?.overlayMouseTarget.appendChild(this.div)\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    if (map !== null) {\n      // Fix for Issue 157\n      this.boundsChangedListener = google.maps.event.addListener(\n        map,\n        'bounds_changed',\n        this.onBoundsChanged\n      )\n\n      this.div.addEventListener('mousedown', this.onMouseDown)\n\n      this.div.addEventListener('click', this.onClick)\n\n      this.div.addEventListener('mouseover', this.onMouseOver)\n\n      this.div.addEventListener('mouseout', this.onMouseOut)\n    }\n  }\n\n  onRemove() {\n    if (this.div && this.div.parentNode) {\n      this.hide()\n\n      if (this.boundsChangedListener !== null) {\n        google.maps.event.removeListener(this.boundsChangedListener)\n      }\n\n      this.div.removeEventListener('mousedown', this.onMouseDown)\n\n      this.div.removeEventListener('click', this.onClick)\n\n      this.div.removeEventListener('mouseover', this.onMouseOver)\n\n      this.div.removeEventListener('mouseout', this.onMouseOut)\n\n      this.div.parentNode.removeChild(this.div)\n\n      if (this.timeOut !== null) {\n        window.clearTimeout(this.timeOut)\n\n        this.timeOut = null\n      }\n\n      this.div = null\n    }\n  }\n\n  draw() {\n    if (this.visible && this.div !== null && this.center) {\n      const pos = this.getPosFromLatLng(this.center)\n\n      this.div.style.top = pos !== null ? `${pos.y}px` : '0'\n      this.div.style.left = pos !== null ? `${pos.x}px` : '0'\n    }\n  }\n\n  hide() {\n    if (this.div) {\n      this.div.style.display = 'none'\n    }\n\n    this.visible = false\n  }\n\n  show() {\n    if (this.div && this.center) {\n      const divTitle = this.sums === null ||\n      typeof this.sums.title === 'undefined' ||\n      this.sums.title === '' ? this.cluster.getClusterer().getTitle() :  this.sums.title\n\n      // NOTE: values must be specified in px units\n      const bp = this.backgroundPosition.split(' ')\n\n      const spriteH = parseInt(bp[0]?.replace(/^\\s+|\\s+$/g, '') || '0', 10)\n      const spriteV = parseInt(bp[1]?.replace(/^\\s+|\\s+$/g, '') || '0', 10)\n\n      const pos = this.getPosFromLatLng(this.center)\n\n      this.div.className = this.className\n      this.div .setAttribute('style', `cursor: pointer; position: absolute; top: ${pos !== null ? `${pos.y}px` : '0'}; left: ${pos !== null ? `${pos.x}px` : '0'}; width: ${this.width}px; height: ${this.height}px; `)\n\n      const img = document.createElement('img')\n\n      img.alt = divTitle\n      img.src = this.url\n      img.width = this.width\n      img.height = this.height\n      img.setAttribute('style', `position: absolute; top: ${spriteV}px; left: ${spriteH}px`)\n\n      if (!this.cluster.getClusterer().enableRetinaIcons) {\n        img.style.clip = `rect(-${spriteV}px, -${spriteH + this.width}px, -${\n          spriteV + this.height\n        }, -${spriteH})`\n      }\n\n      const textElm = document.createElement('div')\n\n      textElm .setAttribute('style', `position: absolute; top: ${this.anchorText[0]}px; left: ${this.anchorText[1]}px; color: ${this.textColor}; font-size: ${this.textSize}px; font-family: ${this.fontFamily}; font-weight: ${this.fontWeight}; fontStyle: ${this.fontStyle}; text-decoration: ${this.textDecoration}; text-align: center; width: ${this.width}px; line-height: ${this.height}px`)\n\n      if (this.sums?.text) textElm.innerText = `${this.sums?.text}`\n      if (this.sums?.html) textElm.innerHTML = `${this.sums?.html}`\n\n      this.div.innerHTML = ''\n\n      this.div.appendChild(img)\n      this.div.appendChild(textElm)\n\n      this.div.title = divTitle\n\n      this.div.style.display = ''\n    }\n\n    this.visible = true\n  }\n\n  useStyle(sums: ClusterIconInfo) {\n    this.sums = sums\n\n    const styles = this.cluster.getClusterer().getStyles()\n\n    const style =\n      styles[Math.min(styles.length - 1, Math.max(0, sums.index - 1))]\n\n    if (style) {\n      this.url = style.url\n      this.height = style.height\n      this.width = style.width\n\n      if (style.className) {\n        this.className = `${this.clusterClassName} ${style.className}`\n      }\n\n      this.anchorText = style.anchorText || [0, 0]\n      this.anchorIcon = style.anchorIcon || [this.height / 2, this.width / 2]\n\n      this.textColor = style.textColor || 'black'\n\n      this.textSize = style.textSize || 11\n\n      this.textDecoration = style.textDecoration || 'none'\n\n      this.fontWeight = style.fontWeight || 'bold'\n\n      this.fontStyle = style.fontStyle || 'normal'\n\n      this.fontFamily = style.fontFamily || 'Arial,sans-serif'\n\n      this.backgroundPosition = style.backgroundPosition || '0 0'\n    }\n  }\n\n  setCenter(center: google.maps.LatLng) {\n    this.center = center\n  }\n\n  getPosFromLatLng(latlng: google.maps.LatLng): google.maps.Point | null {\n    const pos = (this as unknown as google.maps.OverlayView).getProjection().fromLatLngToDivPixel(latlng)\n\n    if (pos !== null) {\n      pos.x -= this.anchorIcon[1]\n\n      pos.y -= this.anchorIcon[0]\n    }\n\n    return pos\n  }\n}\n", "/* global google */\n\nimport type { <PERSON>luster<PERSON> } from './Clusterer'\n\nimport { ClusterIcon } from './ClusterIcon'\n\nimport type { MarkerExtended } from './types'\n\nexport class Cluster {\n  markerClusterer: Clusterer\n  map: google.maps.Map | google.maps.StreetViewPanorama | null\n  gridSize: number\n  minClusterSize: number\n  averageCenter: boolean\n  markers: MarkerExtended[]\n  center: google.maps.LatLng | undefined\n  bounds: google.maps.LatLngBounds | null\n  clusterIcon: ClusterIcon\n\n  constructor(markerClusterer: Clusterer) {\n    this.markerClusterer = markerClusterer\n\n    this.map = (this.markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n    this.gridSize = this.markerClusterer.getGridSize()\n\n    this.minClusterSize = this.markerClusterer.getMinimumClusterSize()\n\n    this.averageCenter = this.markerClusterer.getAverageCenter()\n\n    this.markers = []\n\n    this.center = undefined\n\n    this.bounds = null\n\n    this.clusterIcon = new ClusterIcon(this, this.markerClusterer.getStyles())\n\n    this.getSize = this.getSize.bind(this)\n    this.getMarkers = this.getMarkers.bind(this)\n    this.getCenter = this.getCenter.bind(this)\n    this.getMap = this.getMap.bind(this)\n    this.getClusterer = this.getClusterer.bind(this)\n    this.getBounds = this.getBounds.bind(this)\n    this.remove = this.remove.bind(this)\n    this.addMarker = this.addMarker.bind(this)\n    this.isMarkerInClusterBounds = this.isMarkerInClusterBounds.bind(this)\n    this.calculateBounds = this.calculateBounds.bind(this)\n    this.updateIcon = this.updateIcon.bind(this)\n    this.isMarkerAlreadyAdded = this.isMarkerAlreadyAdded.bind(this)\n  }\n\n  getSize(): number {\n    return this.markers.length\n  }\n\n  getMarkers(): MarkerExtended[] {\n    return this.markers\n  }\n\n  getCenter(): google.maps.LatLng | undefined {\n    return this.center\n  }\n\n  getMap(): google.maps.Map | google.maps.StreetViewPanorama | null {\n    return this.map\n  }\n\n  getClusterer(): Clusterer {\n    return this.markerClusterer\n  }\n\n  getBounds(): google.maps.LatLngBounds {\n    const bounds = new google.maps.LatLngBounds(this.center, this.center)\n\n    const markers = this.getMarkers()\n\n    for (const marker of markers) {\n      const position = marker.getPosition()\n\n      if (position) {\n        bounds.extend(position)\n      }\n    }\n\n    return bounds\n  }\n\n  remove() {\n    (this.clusterIcon as unknown as google.maps.OverlayView).setMap(null)\n\n    this.markers = []\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    delete this.markers\n  }\n\n  addMarker(marker: MarkerExtended): boolean {\n    if (this.isMarkerAlreadyAdded(marker)) {\n      return false\n    }\n\n    if (!this.center) {\n      const position = marker.getPosition()\n\n      if (position) {\n        this.center = position\n\n        this.calculateBounds()\n      }\n    } else {\n      if (this.averageCenter) {\n        const position = marker.getPosition()\n\n        if (position) {\n          const length = this.markers.length + 1\n\n          this.center = new google.maps.LatLng(\n            (this.center.lat() * (length - 1) + position.lat()) / length,\n            (this.center.lng() * (length - 1) + position.lng()) / length\n          )\n\n          this.calculateBounds()\n        }\n      }\n    }\n\n    marker.isAdded = true\n\n    this.markers.push(marker)\n\n    const mCount = this.markers.length\n\n    const maxZoom = this.markerClusterer.getMaxZoom()\n\n    const zoom = this.map?.getZoom()\n\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      // Zoomed in past max zoom, so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map)\n      }\n    } else if (mCount < this.minClusterSize) {\n      // Min cluster size not reached so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map)\n      }\n    } else if (mCount === this.minClusterSize) {\n      // Hide the markers that were showing.\n      for (const markerElement of this.markers) {\n        markerElement.setMap(null)\n      }\n    } else {\n      marker.setMap(null)\n    }\n\n    return true\n  }\n\n  isMarkerInClusterBounds(marker: MarkerExtended): boolean {\n    if (this.bounds !== null) {\n      const position = marker.getPosition()\n\n      if (position) {\n        return this.bounds.contains(position)\n      }\n    }\n\n    return false\n  }\n\n  calculateBounds() {\n    this.bounds = this.markerClusterer.getExtendedBounds(\n      new google.maps.LatLngBounds(this.center, this.center)\n    )\n  }\n\n  updateIcon() {\n    const mCount = this.markers.length\n\n    const maxZoom = this.markerClusterer.getMaxZoom()\n\n    const zoom = this.map?.getZoom()\n\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      this.clusterIcon.hide()\n\n      return\n    }\n\n    if (mCount < this.minClusterSize) {\n      // Min cluster size not yet reached.\n      this.clusterIcon.hide()\n\n      return\n    }\n\n    if (this.center) {\n      this.clusterIcon.setCenter(this.center)\n    }\n\n    this.clusterIcon.useStyle(\n      this.markerClusterer.getCalculator()(this.markers, this.markerClusterer.getStyles().length)\n    )\n\n    this.clusterIcon.show()\n  }\n\n  isMarkerAlreadyAdded(marker: MarkerExtended): boolean {\n    if (this.markers.includes) {\n      return this.markers.includes(marker)\n    }\n\n    for (let i = 0; i < this.markers.length; i++) {\n      if (marker === this.markers[i]) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n", "/* global google */\n/* eslint-disable filenames/match-regex */\nimport { Cluster } from './Cluster'\nimport type { ClusterIcon } from './ClusterIcon'\n\nimport type {\n  MarkerExtended,\n  ClustererOptions,\n  ClusterIconStyle,\n  TCalculator,\n  ClusterIconInfo,\n} from './types'\n\n/**\n * Supports up to 9007199254740991 (Number.MAX_SAFE_INTEGER) markers\n * which is not a problem as max array length is 4294967296 (2**32)\n */\nfunction CALCULATOR(\n  markers: MarkerExtended[],\n  numStyles: number\n): ClusterIconInfo {\n  const count = markers.length\n\n  const numberOfDigits = count.toString().length\n\n  const index = Math.min(numberOfDigits, numStyles)\n\n  return {\n    text: count.toString(),\n    index,\n    title: '',\n  }\n}\n\nconst BATCH_SIZE = 2000\n\nconst BATCH_SIZE_IE = 500\n\nconst IMAGE_PATH =\n  'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'\n\nconst IMAGE_EXTENSION = 'png'\n\nconst IMAGE_SIZES = [53, 56, 66, 78, 90]\n\nconst CLUSTERER_CLASS = 'cluster'\n\nexport class Clusterer implements google.maps.OverlayView {\n  markers: MarkerExtended[]\n  clusters: Cluster[]\n  listeners: google.maps.MapsEventListener[]\n  activeMap: google.maps.Map | google.maps.StreetViewPanorama | null\n  ready: boolean\n  gridSize: number\n  minClusterSize: number\n  maxZoom: number | null\n  styles: ClusterIconStyle[]\n  title: string\n  zoomOnClick: boolean\n  averageCenter: boolean\n  ignoreHidden: boolean\n  enableRetinaIcons: boolean\n  imagePath: string\n  imageExtension: string\n  imageSizes: number[]\n  calculator: TCalculator\n  batchSize: number\n  batchSizeIE: number\n  clusterClass: string\n  timerRefStatic: number | null\n\n  constructor(\n    map: google.maps.Map,\n    optMarkers: MarkerExtended[] = [],\n    optOptions: ClustererOptions = {}\n  ) {\n    this.getMinimumClusterSize = this.getMinimumClusterSize.bind(this)\n    this.setMinimumClusterSize = this.setMinimumClusterSize.bind(this)\n    this.getEnableRetinaIcons = this.getEnableRetinaIcons.bind(this)\n    this.setEnableRetinaIcons = this.setEnableRetinaIcons.bind(this)\n    this.addToClosestCluster = this.addToClosestCluster.bind(this)\n    this.getImageExtension = this.getImageExtension.bind(this)\n    this.setImageExtension = this.setImageExtension.bind(this)\n    this.getExtendedBounds = this.getExtendedBounds.bind(this)\n    this.getAverageCenter = this.getAverageCenter.bind(this)\n    this.setAverageCenter = this.setAverageCenter.bind(this)\n    this.getTotalClusters = this.getTotalClusters.bind(this)\n    this.fitMapToMarkers = this.fitMapToMarkers.bind(this)\n    this.getIgnoreHidden = this.getIgnoreHidden.bind(this)\n    this.setIgnoreHidden = this.setIgnoreHidden.bind(this)\n    this.getClusterClass = this.getClusterClass.bind(this)\n    this.setClusterClass = this.setClusterClass.bind(this)\n    this.getTotalMarkers = this.getTotalMarkers.bind(this)\n    this.getZoomOnClick = this.getZoomOnClick.bind(this)\n    this.setZoomOnClick = this.setZoomOnClick.bind(this)\n    this.getBatchSizeIE = this.getBatchSizeIE.bind(this)\n    this.setBatchSizeIE = this.setBatchSizeIE.bind(this)\n    this.createClusters = this.createClusters.bind(this)\n    this.onZoomChanged = this.onZoomChanged.bind(this)\n    this.getImageSizes = this.getImageSizes.bind(this)\n    this.setImageSizes = this.setImageSizes.bind(this)\n    this.getCalculator = this.getCalculator.bind(this)\n    this.setCalculator = this.setCalculator.bind(this)\n    this.removeMarkers = this.removeMarkers.bind(this)\n    this.resetViewport = this.resetViewport.bind(this)\n    this.getImagePath = this.getImagePath.bind(this)\n    this.setImagePath = this.setImagePath.bind(this)\n    this.pushMarkerTo = this.pushMarkerTo.bind(this)\n    this.removeMarker = this.removeMarker.bind(this)\n    this.clearMarkers = this.clearMarkers.bind(this)\n    this.setupStyles = this.setupStyles.bind(this)\n    this.getGridSize = this.getGridSize.bind(this)\n    this.setGridSize = this.setGridSize.bind(this)\n    this.getClusters = this.getClusters.bind(this)\n    this.getMaxZoom = this.getMaxZoom.bind(this)\n    this.setMaxZoom = this.setMaxZoom.bind(this)\n    this.getMarkers = this.getMarkers.bind(this)\n    this.addMarkers = this.addMarkers.bind(this)\n    this.getStyles = this.getStyles.bind(this)\n    this.setStyles = this.setStyles.bind(this)\n    this.addMarker = this.addMarker.bind(this)\n    this.onRemove = this.onRemove.bind(this)\n    this.getTitle = this.getTitle.bind(this)\n    this.setTitle = this.setTitle.bind(this)\n    this.repaint = this.repaint.bind(this)\n    this.onIdle = this.onIdle.bind(this)\n    this.redraw = this.redraw.bind(this)\n    this.onAdd = this.onAdd.bind(this)\n    this.draw = this.draw.bind(this)\n\n    this.extend = this.extend.bind(this)\n    this.extend(Clusterer, google.maps.OverlayView)\n\n    this.markers = []\n    this.clusters = []\n    this.listeners = []\n    this.activeMap = null\n    this.ready = false\n    this.gridSize = optOptions.gridSize || 60\n    this.minClusterSize = optOptions.minimumClusterSize || 2\n    this.maxZoom = optOptions.maxZoom || null\n    this.styles = optOptions.styles || []\n\n    this.title = optOptions.title || ''\n\n    this.zoomOnClick = true\n\n    if (optOptions.zoomOnClick !== undefined) {\n      this.zoomOnClick = optOptions.zoomOnClick\n    }\n\n    this.averageCenter = false\n\n    if (optOptions.averageCenter !== undefined) {\n      this.averageCenter = optOptions.averageCenter\n    }\n\n    this.ignoreHidden = false\n\n    if (optOptions.ignoreHidden !== undefined) {\n      this.ignoreHidden = optOptions.ignoreHidden\n    }\n\n    this.enableRetinaIcons = false\n\n    if (optOptions.enableRetinaIcons !== undefined) {\n      this.enableRetinaIcons = optOptions.enableRetinaIcons\n    }\n    this.imagePath = optOptions.imagePath || IMAGE_PATH\n\n    this.imageExtension = optOptions.imageExtension || IMAGE_EXTENSION\n\n    this.imageSizes = optOptions.imageSizes || IMAGE_SIZES\n\n    this.calculator = optOptions.calculator || CALCULATOR\n\n    this.batchSize = optOptions.batchSize || BATCH_SIZE\n\n    this.batchSizeIE = optOptions.batchSizeIE || BATCH_SIZE_IE\n\n    this.clusterClass = optOptions.clusterClass || CLUSTERER_CLASS\n\n    if (navigator.userAgent.toLowerCase().indexOf('msie') !== -1) {\n      // Try to avoid IE timeout when processing a huge number of markers:\n      this.batchSize = this.batchSizeIE\n    }\n\n    this.timerRefStatic = null\n\n    this.setupStyles()\n\n    this.addMarkers(optMarkers, true);\n\n    (this as unknown as google.maps.OverlayView).setMap(map) // Note: this causes onAdd to be called\n  }\n\n  onZoomChanged(): void {\n    this.resetViewport(false)\n\n    // Workaround for this Google bug: when map is at level 0 and \"-\" of\n    // zoom slider is clicked, a \"zoom_changed\" event is fired even though\n    // the map doesn't zoom out any further. In this situation, no \"idle\"\n    // event is triggered so the cluster markers that have been removed\n    // do not get redrawn. Same goes for a zoom in at maxZoom.\n    if (\n      (this as unknown as google.maps.OverlayView).getMap()?.getZoom() === ((this as unknown as google.maps.OverlayView).get('minZoom') || 0) ||\n      (this as unknown as google.maps.OverlayView).getMap()?.getZoom() === (this as unknown as google.maps.OverlayView).get('maxZoom')\n    ) {\n      google.maps.event.trigger(this, 'idle')\n    }\n  }\n\n  onIdle(): void {\n    this.redraw()\n  }\n\n  onAdd(): void {\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    this.activeMap = map\n\n    this.ready = true\n\n    this.repaint()\n\n    if (map !== null) {\n      // Add the map event listeners\n      this.listeners = [\n        google.maps.event.addListener(\n          map,\n          'zoom_changed',\n          this.onZoomChanged\n        ),\n        google.maps.event.addListener(\n          map,\n          'idle',\n          this.onIdle\n        ),\n      ]\n    }\n  }\n\n  onRemove(): void {\n    // Put all the managed markers back on the map:\n    for (const marker of this.markers) {\n      if (marker.getMap() !== this.activeMap) {\n        marker.setMap(this.activeMap)\n      }\n    }\n\n    // Remove all clusters:\n    for (const cluster of this.clusters) {\n      cluster.remove()\n    }\n\n    this.clusters = []\n\n    // Remove map event listeners:\n    for (const listener of this.listeners) {\n      google.maps.event.removeListener(listener)\n    }\n\n    this.listeners = []\n\n    this.activeMap = null\n\n    this.ready = false\n  }\n\n  draw(): void { return }\n\n  getMap(): null { return null }\n\n  getPanes(): null { return null }\n\n  getProjection()  {\n    return {\n      fromContainerPixelToLatLng(): null { return null },\n      fromDivPixelToLatLng(): null { return null},\n      fromLatLngToContainerPixel(): null { return null},\n      fromLatLngToDivPixel(): null { return null},\n      getVisibleRegion(): null { return null },\n      getWorldWidth(): number { return 0 }\n    }\n  }\n\n  setMap(): void { return }\n\n  addListener() {\n    return {\n      remove() { return }\n    }\n  }\n\n  bindTo(): void { return }\n\n  get(): void { return }\n\n  notify(): void { return }\n\n  set(): void { return }\n  setValues(): void { return }\n  unbind(): void { return }\n  unbindAll(): void { return }\n\n  setupStyles(): void {\n    if (this.styles.length > 0) {\n      return\n    }\n\n    for (let i = 0; i < this.imageSizes.length; i++) {\n      this.styles.push({\n        url: `${this.imagePath + (i + 1)}.${this.imageExtension}`,\n        height: this.imageSizes[i] || 0,\n        width: this.imageSizes[i] || 0,\n      })\n    }\n  }\n\n  fitMapToMarkers(): void {\n    const markers = this.getMarkers()\n\n    const bounds = new google.maps.LatLngBounds()\n\n    for (const marker of markers) {\n      const position = marker.getPosition()\n\n      if (position) {\n        bounds.extend(position)\n      }\n    }\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    if (map !== null && 'fitBounds' in map) {\n      map.fitBounds(bounds)\n    }\n\n  }\n\n  getGridSize(): number {\n    return this.gridSize\n  }\n\n  setGridSize(gridSize: number) {\n    this.gridSize = gridSize\n  }\n\n  getMinimumClusterSize(): number {\n    return this.minClusterSize\n  }\n\n  setMinimumClusterSize(minimumClusterSize: number) {\n    this.minClusterSize = minimumClusterSize\n  }\n\n  getMaxZoom(): number | null {\n    return this.maxZoom\n  }\n\n  setMaxZoom(maxZoom: number) {\n    this.maxZoom = maxZoom\n  }\n\n  getStyles(): ClusterIconStyle[] {\n    return this.styles\n  }\n\n  setStyles(styles: ClusterIconStyle[]) {\n    this.styles = styles\n  }\n\n  getTitle(): string {\n    return this.title\n  }\n\n  setTitle(title: string) {\n    this.title = title\n  }\n\n  getZoomOnClick(): boolean {\n    return this.zoomOnClick\n  }\n\n  setZoomOnClick(zoomOnClick: boolean) {\n    this.zoomOnClick = zoomOnClick\n  }\n\n  getAverageCenter(): boolean {\n    return this.averageCenter\n  }\n\n  setAverageCenter(averageCenter: boolean) {\n    this.averageCenter = averageCenter\n  }\n\n  getIgnoreHidden(): boolean {\n    return this.ignoreHidden\n  }\n\n  setIgnoreHidden(ignoreHidden: boolean) {\n    this.ignoreHidden = ignoreHidden\n  }\n\n  getEnableRetinaIcons(): boolean {\n    return this.enableRetinaIcons\n  }\n\n  setEnableRetinaIcons(enableRetinaIcons: boolean) {\n    this.enableRetinaIcons = enableRetinaIcons\n  }\n\n  getImageExtension(): string {\n    return this.imageExtension\n  }\n\n  setImageExtension(imageExtension: string) {\n    this.imageExtension = imageExtension\n  }\n\n  getImagePath(): string {\n    return this.imagePath\n  }\n\n  setImagePath(imagePath: string) {\n    this.imagePath = imagePath\n  }\n\n  getImageSizes(): number[] {\n    return this.imageSizes\n  }\n\n  setImageSizes(imageSizes: number[]) {\n    this.imageSizes = imageSizes\n  }\n\n  getCalculator(): TCalculator {\n    return this.calculator\n  }\n\n  setCalculator(calculator: TCalculator) {\n    this.calculator = calculator\n  }\n\n  getBatchSizeIE(): number {\n    return this.batchSizeIE\n  }\n\n  setBatchSizeIE(batchSizeIE: number) {\n    this.batchSizeIE = batchSizeIE\n  }\n\n  getClusterClass(): string {\n    return this.clusterClass\n  }\n\n  setClusterClass(clusterClass: string) {\n    this.clusterClass = clusterClass\n  }\n\n  getMarkers(): MarkerExtended[] {\n    return this.markers\n  }\n\n  getTotalMarkers(): number {\n    return this.markers.length\n  }\n\n  getClusters(): Cluster[] {\n    return this.clusters\n  }\n\n  getTotalClusters(): number {\n    return this.clusters.length\n  }\n\n  addMarker(marker: MarkerExtended, optNoDraw: boolean) {\n    this.pushMarkerTo(marker)\n\n    if (!optNoDraw) {\n      this.redraw()\n    }\n  }\n\n  addMarkers(markers: MarkerExtended[], optNoDraw: boolean) {\n    for (const key in markers) {\n      if (Object.prototype.hasOwnProperty.call(markers, key)) {\n        const marker = markers[key]\n\n        if (marker) {\n          this.pushMarkerTo(marker)\n        }\n      }\n    }\n\n    if (!optNoDraw) {\n      this.redraw()\n    }\n  }\n\n  pushMarkerTo(marker: MarkerExtended) {\n    // If the marker is draggable add a listener so we can update the clusters on the dragend:\n    if (marker.getDraggable()) {\n      google.maps.event.addListener(marker, 'dragend', () => {\n        if (this.ready) {\n          marker.isAdded = false\n\n          this.repaint()\n        }\n      })\n    }\n\n    marker.isAdded = false\n\n    this.markers.push(marker)\n  }\n\n  removeMarker_(marker: MarkerExtended): boolean {\n    let index = -1\n\n    if (this.markers.indexOf) {\n      index = this.markers.indexOf(marker)\n    } else {\n      for (let i = 0; i < this.markers.length; i++) {\n        if (marker === this.markers[i]) {\n          index = i\n\n          break\n        }\n      }\n    }\n\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false\n    }\n\n    marker.setMap(null)\n\n    this.markers.splice(index, 1) // Remove the marker from the list of managed markers\n\n    return true\n  }\n\n  removeMarker(marker: MarkerExtended, optNoDraw: boolean): boolean {\n    const removed = this.removeMarker_(marker)\n\n    if (!optNoDraw && removed) {\n      this.repaint()\n    }\n\n    return removed\n  }\n\n  removeMarkers(markers: MarkerExtended[], optNoDraw: boolean): boolean {\n    let removed = false\n\n    for (const marker of markers) {\n      removed = removed || this.removeMarker_(marker)\n    }\n\n    if (!optNoDraw && removed) {\n      this.repaint()\n    }\n\n    return removed\n  }\n\n  clearMarkers() {\n    this.resetViewport(true)\n\n    this.markers = []\n  }\n\n  repaint() {\n    const oldClusters = this.clusters.slice()\n\n    this.clusters = []\n\n    this.resetViewport(false)\n\n    this.redraw()\n\n    // Remove the old clusters.\n    // Do it in a timeout to prevent blinking effect.\n    setTimeout(function timeout() {\n      for (const oldCluster of oldClusters) {\n        oldCluster.remove()\n      }\n    }, 0)\n  }\n\n  getExtendedBounds(bounds: google.maps.LatLngBounds): google.maps.LatLngBounds {\n    const projection = (this as unknown as google.maps.OverlayView).getProjection()\n\n    // Convert the points to pixels and the extend out by the grid size.\n    const trPix = projection.fromLatLngToDivPixel(\n      // Turn the bounds into latlng.\n      new google.maps.LatLng(bounds.getNorthEast().lat(), bounds.getNorthEast().lng())\n    )\n\n    if (trPix !== null) {\n      trPix.x += this.gridSize\n      trPix.y -= this.gridSize\n    }\n\n    const blPix = projection.fromLatLngToDivPixel(\n      // Turn the bounds into latlng.\n      new google.maps.LatLng(bounds.getSouthWest().lat(), bounds.getSouthWest().lng())\n    )\n\n    if (blPix !== null) {\n      blPix.x -= this.gridSize\n      blPix.y += this.gridSize\n    }\n\n\n    // Extend the bounds to contain the new bounds.\n    if (trPix !== null) {\n      // Convert the pixel points back to LatLng nw\n      const point1 = projection.fromDivPixelToLatLng(trPix)\n\n      if (point1 !== null) {\n        bounds.extend(point1)\n      }\n    }\n\n    if (blPix !== null) {\n      // Convert the pixel points back to LatLng sw\n      const point2 =  projection.fromDivPixelToLatLng(blPix)\n\n      if (point2 !== null) {\n        bounds.extend(\n          point2\n        )\n      }\n    }\n\n\n    return bounds\n  }\n\n  redraw() {\n    // Redraws all the clusters.\n    this.createClusters(0)\n  }\n\n  resetViewport(optHide: boolean) {\n    // Remove all the clusters\n    for (const cluster of this.clusters) {\n      cluster.remove()\n    }\n\n    this.clusters = []\n\n    // Reset the markers to not be added and to be removed from the map.\n    for (const marker of this.markers) {\n      marker.isAdded = false\n\n      if (optHide) {\n        marker.setMap(null)\n      }\n    }\n  }\n\n  distanceBetweenPoints(p1: google.maps.LatLng, p2: google.maps.LatLng): number {\n    const R = 6371 // Radius of the Earth in km\n\n    const dLat = ((p2.lat() - p1.lat()) * Math.PI) / 180\n    const dLon = ((p2.lng() - p1.lng()) * Math.PI) / 180\n\n    const a =\n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n      Math.cos((p1.lat() * Math.PI) / 180) *\n        Math.cos((p2.lat() * Math.PI) / 180) *\n        Math.sin(dLon / 2) *\n        Math.sin(dLon / 2)\n\n    return R * (2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)))\n  }\n\n  isMarkerInBounds(marker: MarkerExtended, bounds: google.maps.LatLngBounds): boolean {\n    const position = marker.getPosition()\n\n    if (position) {\n      return bounds.contains(position)\n    }\n\n    return false\n  }\n\n  addToClosestCluster(marker: MarkerExtended) {\n    let cluster\n\n    let distance = 40000 // Some large number\n\n    let clusterToAddTo = null\n\n    for (const clusterElement of this.clusters) {\n      cluster = clusterElement\n\n      const center = cluster.getCenter()\n\n      const position = marker.getPosition()\n\n      if (center && position) {\n        const d = this.distanceBetweenPoints(center, position)\n\n        if (d < distance) {\n          distance = d\n\n          clusterToAddTo = cluster\n        }\n      }\n    }\n\n    if (clusterToAddTo && clusterToAddTo.isMarkerInClusterBounds(marker)) {\n      clusterToAddTo.addMarker(marker)\n    } else {\n      cluster = new Cluster(this)\n\n      cluster.addMarker(marker)\n\n      this.clusters.push(cluster)\n    }\n  }\n\n  createClusters(iFirst: number) {\n    if (!this.ready) {\n      return\n    }\n\n    // Cancel previous batch processing if we're working on the first batch:\n    if (iFirst === 0) {\n      /**\n       * This event is fired when the <code>Clusterer</code> begins\n       *  clustering markers.\n       * @name Clusterer#clusteringbegin\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringbegin', this)\n\n      if (this.timerRefStatic !== null) {\n        window.clearTimeout(this.timerRefStatic)\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        delete this.timerRefStatic\n      }\n    }\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    const bounds = map !== null && 'getBounds' in map ? map.getBounds() : null\n\n    const zoom =  map?.getZoom() || 0\n    // Get our current map view bounds.\n    // Create a new bounds object so we don't affect the map.\n    //\n    // See Comments 9 & 11 on Issue 3651 relating to this workaround for a Google Maps bug:\n    const mapBounds = zoom > 3\n        ? new google.maps.LatLngBounds(\n            bounds?.getSouthWest(),\n            bounds?.getNorthEast()\n          )\n        : new google.maps.LatLngBounds(\n            new google.maps.LatLng(85.02070771743472, -178.48388434375),\n            new google.maps.LatLng(-85.08136444384544, 178.00048865625)\n          )\n\n    const extendedMapBounds = this.getExtendedBounds(mapBounds)\n\n    const iLast = Math.min(iFirst + this.batchSize, this.markers.length)\n\n    for (let i = iFirst; i < iLast; i++) {\n      const marker = this.markers[i]\n\n      if (marker && !marker.isAdded && this.isMarkerInBounds(marker, extendedMapBounds) && (!this.ignoreHidden || (this.ignoreHidden && marker.getVisible()))) {\n        this.addToClosestCluster(marker)\n      }\n    }\n\n    if (iLast < this.markers.length) {\n      this.timerRefStatic = window.setTimeout(\n        () => {\n          this.createClusters(iLast)\n        },\n        0\n      )\n    } else {\n      this.timerRefStatic = null\n\n      /**\n       * This event is fired when the <code>Clusterer</code> stops\n       *  clustering markers.\n       * @name Clusterer#clusteringend\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringend', this)\n\n      for (const cluster of this.clusters) {\n        cluster.updateIcon()\n      }\n    }\n  }\n\n  extend<A extends typeof Clusterer | typeof ClusterIcon>(obj1: A, obj2: typeof google.maps.OverlayView): A {\n    return function applyExtend(this: A, object: typeof google.maps.OverlayView): A {\n      for (const property in object.prototype) {\n\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const prop = property as keyof google.maps.OverlayView & (string & {})\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        this.prototype[prop] = object.prototype[prop]\n      }\n\n      return this\n    }.apply<A, [typeof google.maps.OverlayView], A>(obj1, [obj2])\n  }\n}\n"], "names": ["ClusterIcon", "cluster", "styles", "getClusterer", "extend", "google", "maps", "OverlayView", "this", "clusterClassName", "getClusterClass", "className", "center", "undefined", "div", "sums", "visible", "boundsChangedListener", "url", "height", "width", "anchorText", "anchorIcon", "textColor", "textSize", "textDecoration", "fontWeight", "fontStyle", "fontFamily", "backgroundPosition", "cMouseDownInCluster", "cDraggingMapByCluster", "timeOut", "setMap", "getMap", "onBoundsChanged", "bind", "onMouseDown", "onClick", "onMouseOver", "onMouseOut", "onAdd", "onRemove", "draw", "hide", "show", "useStyle", "setCenter", "getPosFromLatLng", "prototype", "event", "markerClusterer_1", "trigger", "getZoomOnClick", "maxZoom_1", "getMaxZoom", "bounds_1", "getBounds", "map", "fitBounds", "window", "setTimeout", "zoom", "getZoom", "setZoom", "cancelBubble", "stopPropagation", "document", "createElement", "_a", "getPanes", "overlayMouseTarget", "append<PERSON><PERSON><PERSON>", "addListener", "addEventListener", "parentNode", "removeListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "pos", "style", "top", "concat", "y", "left", "x", "display", "divTitle", "title", "getTitle", "bp", "split", "spriteH", "parseInt", "replace", "spriteV", "_b", "setAttribute", "img", "alt", "src", "enableRetinaIcons", "clip", "textElm", "_c", "text", "innerText", "_d", "_e", "html", "innerHTML", "_f", "getStyles", "Math", "min", "length", "max", "index", "latlng", "getProjection", "fromLatLngToDivPixel", "Cluster", "markerClusterer", "gridSize", "getGridSize", "minClusterSize", "getMinimumClusterSize", "averageCenter", "getAverageCenter", "markers", "bounds", "clusterIcon", "getSize", "getMarkers", "getCenter", "remove", "add<PERSON><PERSON><PERSON>", "isMarkerInClusterBounds", "calculateBounds", "updateIcon", "isMarkerAlreadyAdded", "LatLngBounds", "_i", "markers_1", "position", "getPosition", "marker", "length_1", "LatLng", "lat", "lng", "isAdded", "push", "mCount", "max<PERSON><PERSON>", "contains", "getExtendedBounds", "getCalculator", "includes", "i", "CALCULATOR", "numStyles", "count", "numberOfDigits", "toString", "IMAGE_SIZES", "Clusterer", "optMarkers", "optOptions", "setMinimumClusterSize", "getEnableRetinaIcons", "setEnableRetinaIcons", "addToClosestCluster", "getImageExtension", "setImageExtension", "setAverageCenter", "getTotalClusters", "fitMapToMarkers", "getIgnoreHidden", "setIgnoreHidden", "setClusterClass", "getTotalMarkers", "setZoomOnClick", "getBatchSizeIE", "setBatchSizeIE", "createClusters", "onZoomChanged", "getImageSizes", "setImageSizes", "setCalculator", "removeMarkers", "resetViewport", "getImagePath", "setImagePath", "pushMarkerTo", "<PERSON><PERSON><PERSON><PERSON>", "clearMarkers", "setupStyles", "setGridSize", "getClusters", "setMaxZoom", "addMarkers", "setStyles", "setTitle", "repaint", "onIdle", "redraw", "clusters", "listeners", "activeMap", "ready", "minimumClusterSize", "zoomOnClick", "ignoreHidden", "imagePath", "imageExtension", "imageSizes", "calculator", "batchSize", "batchSizeIE", "clusterClass", "navigator", "userAgent", "toLowerCase", "indexOf", "timer<PERSON>ef<PERSON>tat<PERSON>", "get", "listener", "fromContainerPixelToLatLng", "fromDivPixelToLatLng", "fromLatLngToContainerPixel", "getVisibleRegion", "getWorldWidth", "bindTo", "notify", "set", "set<PERSON><PERSON><PERSON>", "unbind", "unbindAll", "optNoDraw", "key", "Object", "hasOwnProperty", "call", "_this", "getDraggable", "removeMarker_", "splice", "removed", "markers_2", "oldClusters", "slice", "oldClusters_1", "projection", "trPix", "getNorthEast", "blPix", "getSouthWest", "point1", "point2", "optHide", "distanceBetweenPoints", "p1", "p2", "dLat", "PI", "dLon", "a", "sin", "cos", "atan2", "sqrt", "isMarkerInBounds", "distance", "clusterToAddTo", "d", "i<PERSON><PERSON><PERSON>", "mapBounds", "extendedMapBounds", "iLast", "getVisible", "obj1", "obj2", "object", "property", "prop", "apply"], "mappings": "AAMA,IAAAA,EAAA,WA2BE,SAAYA,EAAAC,EAAkBC,GAC5BD,EAAQE,eAAeC,OAAOJ,EAAaK,OAAOC,KAAKC,aAEvDC,KAAKP,QAAUA,EAEfO,KAAKC,iBAAmBD,KAAKP,QAAQE,eAAeO,kBAEpDF,KAAKG,UAAYH,KAAKC,iBAEtBD,KAAKN,OAASA,EAEdM,KAAKI,YAASC,EAEdL,KAAKM,IAAM,KAEXN,KAAKO,KAAO,KAEZP,KAAKQ,SAAU,EAEfR,KAAKS,sBAAwB,KAE7BT,KAAKU,IAAM,GAEXV,KAAKW,OAAS,EACdX,KAAKY,MAAQ,EAEbZ,KAAKa,WAAa,CAAC,EAAG,GACtBb,KAAKc,WAAa,CAAC,EAAG,GAEtBd,KAAKe,UAAY,QACjBf,KAAKgB,SAAW,GAChBhB,KAAKiB,eAAiB,OACtBjB,KAAKkB,WAAa,OAClBlB,KAAKmB,UAAY,SACjBnB,KAAKoB,WAAa,mBAElBpB,KAAKqB,mBAAqB,MAE1BrB,KAAKsB,oBAAsB,KAC3BtB,KAAKuB,sBAAwB,KAC7BvB,KAAKwB,QAAU,KAEdxB,KAA4CyB,OAAOhC,EAAQiC,UAE5D1B,KAAK2B,gBAAkB3B,KAAK2B,gBAAgBC,KAAK5B,MACjDA,KAAK6B,YAAc7B,KAAK6B,YAAYD,KAAK5B,MACzCA,KAAK8B,QAAU9B,KAAK8B,QAAQF,KAAK5B,MACjCA,KAAK+B,YAAc/B,KAAK+B,YAAYH,KAAK5B,MACzCA,KAAKgC,WAAahC,KAAKgC,WAAWJ,KAAK5B,MACvCA,KAAKiC,MAAQjC,KAAKiC,MAAML,KAAK5B,MAC7BA,KAAKkC,SAAWlC,KAAKkC,SAASN,KAAK5B,MACnCA,KAAKmC,KAAOnC,KAAKmC,KAAKP,KAAK5B,MAC3BA,KAAKoC,KAAOpC,KAAKoC,KAAKR,KAAK5B,MAC3BA,KAAKqC,KAAOrC,KAAKqC,KAAKT,KAAK5B,MAC3BA,KAAKsC,SAAWtC,KAAKsC,SAASV,KAAK5B,MACnCA,KAAKuC,UAAYvC,KAAKuC,UAAUX,KAAK5B,MACrCA,KAAKwC,iBAAmBxC,KAAKwC,iBAAiBZ,KAAK5B,KACpD,CAuRH,OArRER,EAAAiD,UAAAd,gBAAA,WACE3B,KAAKuB,sBAAwBvB,KAAKsB,qBAGpC9B,EAAAiD,UAAAZ,YAAA,WACE7B,KAAKsB,qBAAsB,EAE3BtB,KAAKuB,uBAAwB,GAG/B/B,EAAOiD,UAAAX,QAAP,SAAQY,GAGN,GAFA1C,KAAKsB,qBAAsB,GAEtBtB,KAAKuB,sBAAuB,CAC/B,IAAMoB,EAAkB3C,KAAKP,QAAQE,eAarC,GALAE,OAAOC,KAAK4C,MAAME,QAAQD,EAAiB,QAAS3C,KAAKP,SACzDI,OAAOC,KAAK4C,MAAME,QAAQD,EAAiB,eAAgB3C,KAAKP,SAI5DkD,EAAgBE,iBAAkB,CAEpC,IAAMC,EAAUH,EAAgBI,aAE1BC,EAAShD,KAAKP,QAAQwD,YAEtBC,EAAOP,EAAuDjB,SAExD,OAARwB,GAAgB,cAAeA,GACjCA,EAAIC,UAAUH,GAKhBhD,KAAKwB,QAAU4B,OAAOC,YAAW,WAC/B,IAAMH,EAAOP,EAAuDjB,SAEpE,GAAY,OAARwB,EAAc,CACZ,cAAeA,GACjBA,EAAIC,UAAUH,GAGhB,IAAMM,EAAOJ,EAAIK,WAAa,EAIhB,OAAZT,GACAQ,EAAOR,GAEPI,EAAIM,QAAQV,EAAU,EAEzB,CACF,GAAE,IACJ,CAGDJ,EAAMe,cAAe,EAEjBf,EAAMgB,iBACRhB,EAAMgB,iBAET,GAGHlE,EAAAiD,UAAAV,YAAA,WAOElC,OAAOC,KAAK4C,MAAME,QAChB5C,KAAKP,QAAQE,eACb,YACAK,KAAKP,UAITD,EAAAiD,UAAAT,WAAA,WAOEnC,OAAOC,KAAK4C,MAAME,QAChB5C,KAAKP,QAAQE,eACb,WACAK,KAAKP,UAITD,EAAAiD,UAAAR,MAAA,iBACEjC,KAAKM,IAAMqD,SAASC,cAAc,OAElC5D,KAAKM,IAAIH,UAAYH,KAAKG,UAEtBH,KAAKQ,SACPR,KAAKqC,OAGmD,QAAzDwB,EAAC7D,KAA4C8D,kBAAY,IAAAD,GAAAA,EAAAE,mBAAmBC,YAAYhE,KAAKM,KAE9F,IAAM4C,EAAOlD,KAA4C0B,SAE7C,OAARwB,IAEFlD,KAAKS,sBAAwBZ,OAAOC,KAAK4C,MAAMuB,YAC7Cf,EACA,iBACAlD,KAAK2B,iBAGP3B,KAAKM,IAAI4D,iBAAiB,YAAalE,KAAK6B,aAE5C7B,KAAKM,IAAI4D,iBAAiB,QAASlE,KAAK8B,SAExC9B,KAAKM,IAAI4D,iBAAiB,YAAalE,KAAK+B,aAE5C/B,KAAKM,IAAI4D,iBAAiB,WAAYlE,KAAKgC,cAI/CxC,EAAAiD,UAAAP,SAAA,WACMlC,KAAKM,KAAON,KAAKM,IAAI6D,aACvBnE,KAAKoC,OAE8B,OAA/BpC,KAAKS,uBACPZ,OAAOC,KAAK4C,MAAM0B,eAAepE,KAAKS,uBAGxCT,KAAKM,IAAI+D,oBAAoB,YAAarE,KAAK6B,aAE/C7B,KAAKM,IAAI+D,oBAAoB,QAASrE,KAAK8B,SAE3C9B,KAAKM,IAAI+D,oBAAoB,YAAarE,KAAK+B,aAE/C/B,KAAKM,IAAI+D,oBAAoB,WAAYrE,KAAKgC,YAE9ChC,KAAKM,IAAI6D,WAAWG,YAAYtE,KAAKM,KAEhB,OAAjBN,KAAKwB,UACP4B,OAAOmB,aAAavE,KAAKwB,SAEzBxB,KAAKwB,QAAU,MAGjBxB,KAAKM,IAAM,OAIfd,EAAAiD,UAAAN,KAAA,WACE,GAAInC,KAAKQ,SAAwB,OAAbR,KAAKM,KAAgBN,KAAKI,OAAQ,CACpD,IAAMoE,EAAMxE,KAAKwC,iBAAiBxC,KAAKI,QAEvCJ,KAAKM,IAAImE,MAAMC,IAAc,OAARF,EAAe,GAAAG,OAAGH,EAAII,QAAQ,IACnD5E,KAAKM,IAAImE,MAAMI,KAAe,OAARL,EAAe,GAAAG,OAAGH,EAAIM,QAAQ,GACrD,GAGHtF,EAAAiD,UAAAL,KAAA,WACMpC,KAAKM,MACPN,KAAKM,IAAImE,MAAMM,QAAU,QAG3B/E,KAAKQ,SAAU,GAGjBhB,EAAAiD,UAAAJ,KAAA,2BACE,GAAIrC,KAAKM,KAAON,KAAKI,OAAQ,CAC3B,IAAM4E,EAAyB,OAAdhF,KAAKO,WACK,IAApBP,KAAKO,KAAK0E,OACG,KAApBjF,KAAKO,KAAK0E,MAAejF,KAAKP,QAAQE,eAAeuF,WAAclF,KAAKO,KAAK0E,MAGvEE,EAAKnF,KAAKqB,mBAAmB+D,MAAM,KAEnCC,EAAUC,UAAc,QAALzB,EAAAsB,EAAG,UAAE,IAAAtB,OAAA,EAAAA,EAAE0B,QAAQ,aAAc,MAAO,IAAK,IAC5DC,EAAUF,UAAc,QAALG,EAAAN,EAAG,UAAE,IAAAM,OAAA,EAAAA,EAAEF,QAAQ,aAAc,MAAO,IAAK,IAE5Df,EAAMxE,KAAKwC,iBAAiBxC,KAAKI,QAEvCJ,KAAKM,IAAIH,UAAYH,KAAKG,UAC1BH,KAAKM,IAAKoF,aAAa,QAAS,6CAA6Cf,OAAQ,OAARH,EAAe,UAAGA,EAAII,EAAK,MAAG,IAAG,YAAAD,OAAmB,OAARH,EAAe,UAAGA,EAAIM,EAAC,MAAO,IAAG,aAAAH,OAAY3E,KAAKY,MAAK,gBAAA+D,OAAe3E,KAAKW,OAAY,SAEhN,IAAMgF,EAAMhC,SAASC,cAAc,OAEnC+B,EAAIC,IAAMZ,EACVW,EAAIE,IAAM7F,KAAKU,IACfiF,EAAI/E,MAAQZ,KAAKY,MACjB+E,EAAIhF,OAASX,KAAKW,OAClBgF,EAAID,aAAa,QAAS,4BAA4Bf,OAAAa,EAAoB,cAAAb,OAAAU,EAAW,OAEhFrF,KAAKP,QAAQE,eAAemG,oBAC/BH,EAAIlB,MAAMsB,KAAO,SAASpB,OAAAa,EAAe,SAAAb,OAAAU,EAAUrF,KAAKY,MAAK,SAAA+D,OAC3Da,EAAUxF,KAAKW,OAAM,OAAAgE,OACjBU,EAAO,MAGf,IAAMW,EAAUrC,SAASC,cAAc,OAEvCoC,EAASN,aAAa,QAAS,mCAA4B1F,KAAKa,WAAW,wBAAeb,KAAKa,WAAW,yBAAgBb,KAAKe,UAAS,iBAAA4D,OAAgB3E,KAAKgB,SAA4B,qBAAA2D,OAAA3E,KAAKoB,WAA4B,mBAAAuD,OAAA3E,KAAKkB,WAAU,iBAAAyD,OAAgB3E,KAAKmB,UAAS,uBAAAwD,OAAsB3E,KAAKiB,eAA8C,iCAAA0D,OAAA3E,KAAKY,MAAyB,qBAAA+D,OAAA3E,KAAKW,OAAU,gBAEzXsF,EAAAjG,KAAKO,2BAAM2F,QAAMF,EAAQG,UAAY,GAAGxB,OAAS,QAATyB,EAAApG,KAAKO,YAAI,IAAA6F,OAAA,EAAAA,EAAEF,gBACnDG,EAAArG,KAAKO,2BAAM+F,QAAMN,EAAQO,UAAY,GAAG5B,OAAS,QAAT6B,EAAAxG,KAAKO,YAAI,IAAAiG,OAAA,EAAAA,EAAEF,OAEvDtG,KAAKM,IAAIiG,UAAY,GAErBvG,KAAKM,IAAI0D,YAAY2B,GACrB3F,KAAKM,IAAI0D,YAAYgC,GAErBhG,KAAKM,IAAI2E,MAAQD,EAEjBhF,KAAKM,IAAImE,MAAMM,QAAU,EAC1B,CAED/E,KAAKQ,SAAU,GAGjBhB,EAAQiD,UAAAH,SAAR,SAAS/B,GACPP,KAAKO,KAAOA,EAEZ,IAAMb,EAASM,KAAKP,QAAQE,eAAe8G,YAErChC,EACJ/E,EAAOgH,KAAKC,IAAIjH,EAAOkH,OAAS,EAAGF,KAAKG,IAAI,EAAGtG,EAAKuG,MAAQ,KAE1DrC,IACFzE,KAAKU,IAAM+D,EAAM/D,IACjBV,KAAKW,OAAS8D,EAAM9D,OACpBX,KAAKY,MAAQ6D,EAAM7D,MAEf6D,EAAMtE,YACRH,KAAKG,UAAY,GAAAwE,OAAG3E,KAAKC,iBAAgB,KAAA0E,OAAIF,EAAMtE,YAGrDH,KAAKa,WAAa4D,EAAM5D,YAAc,CAAC,EAAG,GAC1Cb,KAAKc,WAAa2D,EAAM3D,YAAc,CAACd,KAAKW,OAAS,EAAGX,KAAKY,MAAQ,GAErEZ,KAAKe,UAAY0D,EAAM1D,WAAa,QAEpCf,KAAKgB,SAAWyD,EAAMzD,UAAY,GAElChB,KAAKiB,eAAiBwD,EAAMxD,gBAAkB,OAE9CjB,KAAKkB,WAAauD,EAAMvD,YAAc,OAEtClB,KAAKmB,UAAYsD,EAAMtD,WAAa,SAEpCnB,KAAKoB,WAAaqD,EAAMrD,YAAc,mBAEtCpB,KAAKqB,mBAAqBoD,EAAMpD,oBAAsB,QAI1D7B,EAASiD,UAAAF,UAAT,SAAUnC,GACRJ,KAAKI,OAASA,GAGhBZ,EAAgBiD,UAAAD,iBAAhB,SAAiBuE,GACf,IAAMvC,EAAOxE,KAA4CgH,gBAAgBC,qBAAqBF,GAQ9F,OANY,OAARvC,IACFA,EAAIM,GAAK9E,KAAKc,WAAW,GAEzB0D,EAAII,GAAK5E,KAAKc,WAAW,IAGpB0D,GAEVhF,CAAD,ICzWA0H,EAAA,WAWE,SAAAA,EAAYC,GACVnH,KAAKmH,gBAAkBA,EAEvBnH,KAAKkD,IAAOlD,KAAKmH,gBAAuDzF,SAExE1B,KAAKoH,SAAWpH,KAAKmH,gBAAgBE,cAErCrH,KAAKsH,eAAiBtH,KAAKmH,gBAAgBI,wBAE3CvH,KAAKwH,cAAgBxH,KAAKmH,gBAAgBM,mBAE1CzH,KAAK0H,QAAU,GAEf1H,KAAKI,YAASC,EAEdL,KAAK2H,OAAS,KAEd3H,KAAK4H,YAAc,IAAIpI,EAAYQ,KAAMA,KAAKmH,gBAAgBV,aAE9DzG,KAAK6H,QAAU7H,KAAK6H,QAAQjG,KAAK5B,MACjCA,KAAK8H,WAAa9H,KAAK8H,WAAWlG,KAAK5B,MACvCA,KAAK+H,UAAY/H,KAAK+H,UAAUnG,KAAK5B,MACrCA,KAAK0B,OAAS1B,KAAK0B,OAAOE,KAAK5B,MAC/BA,KAAKL,aAAeK,KAAKL,aAAaiC,KAAK5B,MAC3CA,KAAKiD,UAAYjD,KAAKiD,UAAUrB,KAAK5B,MACrCA,KAAKgI,OAAShI,KAAKgI,OAAOpG,KAAK5B,MAC/BA,KAAKiI,UAAYjI,KAAKiI,UAAUrG,KAAK5B,MACrCA,KAAKkI,wBAA0BlI,KAAKkI,wBAAwBtG,KAAK5B,MACjEA,KAAKmI,gBAAkBnI,KAAKmI,gBAAgBvG,KAAK5B,MACjDA,KAAKoI,WAAapI,KAAKoI,WAAWxG,KAAK5B,MACvCA,KAAKqI,qBAAuBrI,KAAKqI,qBAAqBzG,KAAK5B,KAC5D,CA4KH,OA1KEkH,EAAAzE,UAAAoF,QAAA,WACE,OAAO7H,KAAK0H,QAAQd,QAGtBM,EAAAzE,UAAAqF,WAAA,WACE,OAAO9H,KAAK0H,SAGdR,EAAAzE,UAAAsF,UAAA,WACE,OAAO/H,KAAKI,QAGd8G,EAAAzE,UAAAf,OAAA,WACE,OAAO1B,KAAKkD,KAGdgE,EAAAzE,UAAA9C,aAAA,WACE,OAAOK,KAAKmH,iBAGdD,EAAAzE,UAAAQ,UAAA,WAKE,IAJA,IAAM0E,EAAS,IAAI9H,OAAOC,KAAKwI,aAAatI,KAAKI,OAAQJ,KAAKI,QAIzCmI,EAAA,EAAAC,EAFLxI,KAAK8H,aAEAS,WAAAA,IAAS,CAAzB,IACGE,EADSD,EAAAD,GACSG,cAEpBD,GACFd,EAAO/H,OAAO6I,EAEjB,CAED,OAAOd,GAGTT,EAAAzE,UAAAuF,OAAA,WACGhI,KAAK4H,YAAmDnG,OAAO,MAEhEzB,KAAK0H,QAAU,UAIR1H,KAAK0H,SAGdR,EAASzE,UAAAwF,UAAT,SAAUU,SAMAF,EALR,GAAIzI,KAAKqI,qBAAqBM,GAC5B,OAAO,EAGT,GAAK3I,KAAKI,QASR,GAAIJ,KAAKwH,gBACDiB,EAAWE,EAAOD,eAEV,CACZ,IAAME,EAAS5I,KAAK0H,QAAQd,OAAS,EAErC5G,KAAKI,OAAS,IAAIP,OAAOC,KAAK+I,QAC3B7I,KAAKI,OAAO0I,OAASF,EAAS,GAAKH,EAASK,OAASF,GACrD5I,KAAKI,OAAO2I,OAASH,EAAS,GAAKH,EAASM,OAASH,GAGxD5I,KAAKmI,iBACN,OApBGM,EAAWE,EAAOD,iBAGtB1I,KAAKI,OAASqI,EAEdzI,KAAKmI,mBAmBTQ,EAAOK,SAAU,EAEjBhJ,KAAK0H,QAAQuB,KAAKN,GAElB,IAAMO,EAASlJ,KAAK0H,QAAQd,OAEtBuC,EAAUnJ,KAAKmH,gBAAgBpE,aAE/BO,EAAe,QAARO,EAAA7D,KAAKkD,WAAG,IAAAW,OAAA,EAAAA,EAAEN,UAEvB,GAAgB,OAAZ4F,QAAoC,IAAT7F,GAAwBA,EAAO6F,EAExDR,EAAOjH,WAAa1B,KAAKkD,KAC3ByF,EAAOlH,OAAOzB,KAAKkD,UAEhB,GAAIgG,EAASlJ,KAAKsH,eAEnBqB,EAAOjH,WAAa1B,KAAKkD,KAC3ByF,EAAOlH,OAAOzB,KAAKkD,UAEhB,GAAIgG,IAAWlJ,KAAKsH,eAEzB,IAA4B,IAAAiB,EAAA,EAAA9C,EAAAzF,KAAK0H,QAALa,EAAY9C,EAAAmB,OAAZ2B,IAAc,CAAlB9C,EAAA8C,GACR9G,OAAO,KACtB,MAEDkH,EAAOlH,OAAO,MAGhB,OAAO,GAGTyF,EAAuBzE,UAAAyF,wBAAvB,SAAwBS,GACtB,GAAoB,OAAhB3I,KAAK2H,OAAiB,CACxB,IAAMc,EAAWE,EAAOD,cAExB,GAAID,EACF,OAAOzI,KAAK2H,OAAOyB,SAASX,EAE/B,CAED,OAAO,GAGTvB,EAAAzE,UAAA0F,gBAAA,WACEnI,KAAK2H,OAAS3H,KAAKmH,gBAAgBkC,kBACjC,IAAIxJ,OAAOC,KAAKwI,aAAatI,KAAKI,OAAQJ,KAAKI,UAInD8G,EAAAzE,UAAA2F,WAAA,iBACQc,EAASlJ,KAAK0H,QAAQd,OAEtBuC,EAAUnJ,KAAKmH,gBAAgBpE,aAE/BO,EAAe,QAARO,EAAA7D,KAAKkD,WAAG,IAAAW,OAAA,EAAAA,EAAEN,UAEP,OAAZ4F,QAAoC,IAAT7F,GAAwBA,EAAO6F,GAM1DD,EAASlJ,KAAKsH,eALhBtH,KAAK4H,YAAYxF,QAYfpC,KAAKI,QACPJ,KAAK4H,YAAYrF,UAAUvC,KAAKI,QAGlCJ,KAAK4H,YAAYtF,SACftC,KAAKmH,gBAAgBmC,eAArBtJ,CAAqCA,KAAK0H,QAAS1H,KAAKmH,gBAAgBV,YAAYG,SAGtF5G,KAAK4H,YAAYvF,SAGnB6E,EAAoBzE,UAAA4F,qBAApB,SAAqBM,GACnB,GAAI3I,KAAK0H,QAAQ6B,SACf,OAAOvJ,KAAK0H,QAAQ6B,SAASZ,GAG/B,IAAK,IAAIa,EAAI,EAAGA,EAAIxJ,KAAK0H,QAAQd,OAAQ4C,IACvC,GAAIb,IAAW3I,KAAK0H,QAAQ8B,GAC1B,OAAO,EAIX,OAAO,GAEVtC,CAAD,IC7MA,SAASuC,EACP/B,EACAgC,GAEA,IAAMC,EAAQjC,EAAQd,OAEhBgD,EAAiBD,EAAME,WAAWjD,OAElCE,EAAQJ,KAAKC,IAAIiD,EAAgBF,GAEvC,MAAO,CACLxD,KAAMyD,EAAME,WACZ/C,MAAKA,EACL7B,MAAO,GAEX,CAEA,IASM6E,EAAc,CAAC,GAAI,GAAI,GAAI,GAAI,IAIrCC,EAAA,WAwBE,SAAAA,EACE7G,EACA8G,EACAC,QADA,IAAAD,IAAAA,EAAiC,SACjC,IAAAC,IAAAA,EAAiC,CAAA,GAEjCjK,KAAKuH,sBAAwBvH,KAAKuH,sBAAsB3F,KAAK5B,MAC7DA,KAAKkK,sBAAwBlK,KAAKkK,sBAAsBtI,KAAK5B,MAC7DA,KAAKmK,qBAAuBnK,KAAKmK,qBAAqBvI,KAAK5B,MAC3DA,KAAKoK,qBAAuBpK,KAAKoK,qBAAqBxI,KAAK5B,MAC3DA,KAAKqK,oBAAsBrK,KAAKqK,oBAAoBzI,KAAK5B,MACzDA,KAAKsK,kBAAoBtK,KAAKsK,kBAAkB1I,KAAK5B,MACrDA,KAAKuK,kBAAoBvK,KAAKuK,kBAAkB3I,KAAK5B,MACrDA,KAAKqJ,kBAAoBrJ,KAAKqJ,kBAAkBzH,KAAK5B,MACrDA,KAAKyH,iBAAmBzH,KAAKyH,iBAAiB7F,KAAK5B,MACnDA,KAAKwK,iBAAmBxK,KAAKwK,iBAAiB5I,KAAK5B,MACnDA,KAAKyK,iBAAmBzK,KAAKyK,iBAAiB7I,KAAK5B,MACnDA,KAAK0K,gBAAkB1K,KAAK0K,gBAAgB9I,KAAK5B,MACjDA,KAAK2K,gBAAkB3K,KAAK2K,gBAAgB/I,KAAK5B,MACjDA,KAAK4K,gBAAkB5K,KAAK4K,gBAAgBhJ,KAAK5B,MACjDA,KAAKE,gBAAkBF,KAAKE,gBAAgB0B,KAAK5B,MACjDA,KAAK6K,gBAAkB7K,KAAK6K,gBAAgBjJ,KAAK5B,MACjDA,KAAK8K,gBAAkB9K,KAAK8K,gBAAgBlJ,KAAK5B,MACjDA,KAAK6C,eAAiB7C,KAAK6C,eAAejB,KAAK5B,MAC/CA,KAAK+K,eAAiB/K,KAAK+K,eAAenJ,KAAK5B,MAC/CA,KAAKgL,eAAiBhL,KAAKgL,eAAepJ,KAAK5B,MAC/CA,KAAKiL,eAAiBjL,KAAKiL,eAAerJ,KAAK5B,MAC/CA,KAAKkL,eAAiBlL,KAAKkL,eAAetJ,KAAK5B,MAC/CA,KAAKmL,cAAgBnL,KAAKmL,cAAcvJ,KAAK5B,MAC7CA,KAAKoL,cAAgBpL,KAAKoL,cAAcxJ,KAAK5B,MAC7CA,KAAKqL,cAAgBrL,KAAKqL,cAAczJ,KAAK5B,MAC7CA,KAAKsJ,cAAgBtJ,KAAKsJ,cAAc1H,KAAK5B,MAC7CA,KAAKsL,cAAgBtL,KAAKsL,cAAc1J,KAAK5B,MAC7CA,KAAKuL,cAAgBvL,KAAKuL,cAAc3J,KAAK5B,MAC7CA,KAAKwL,cAAgBxL,KAAKwL,cAAc5J,KAAK5B,MAC7CA,KAAKyL,aAAezL,KAAKyL,aAAa7J,KAAK5B,MAC3CA,KAAK0L,aAAe1L,KAAK0L,aAAa9J,KAAK5B,MAC3CA,KAAK2L,aAAe3L,KAAK2L,aAAa/J,KAAK5B,MAC3CA,KAAK4L,aAAe5L,KAAK4L,aAAahK,KAAK5B,MAC3CA,KAAK6L,aAAe7L,KAAK6L,aAAajK,KAAK5B,MAC3CA,KAAK8L,YAAc9L,KAAK8L,YAAYlK,KAAK5B,MACzCA,KAAKqH,YAAcrH,KAAKqH,YAAYzF,KAAK5B,MACzCA,KAAK+L,YAAc/L,KAAK+L,YAAYnK,KAAK5B,MACzCA,KAAKgM,YAAchM,KAAKgM,YAAYpK,KAAK5B,MACzCA,KAAK+C,WAAa/C,KAAK+C,WAAWnB,KAAK5B,MACvCA,KAAKiM,WAAajM,KAAKiM,WAAWrK,KAAK5B,MACvCA,KAAK8H,WAAa9H,KAAK8H,WAAWlG,KAAK5B,MACvCA,KAAKkM,WAAalM,KAAKkM,WAAWtK,KAAK5B,MACvCA,KAAKyG,UAAYzG,KAAKyG,UAAU7E,KAAK5B,MACrCA,KAAKmM,UAAYnM,KAAKmM,UAAUvK,KAAK5B,MACrCA,KAAKiI,UAAYjI,KAAKiI,UAAUrG,KAAK5B,MACrCA,KAAKkC,SAAWlC,KAAKkC,SAASN,KAAK5B,MACnCA,KAAKkF,SAAWlF,KAAKkF,SAAStD,KAAK5B,MACnCA,KAAKoM,SAAWpM,KAAKoM,SAASxK,KAAK5B,MACnCA,KAAKqM,QAAUrM,KAAKqM,QAAQzK,KAAK5B,MACjCA,KAAKsM,OAAStM,KAAKsM,OAAO1K,KAAK5B,MAC/BA,KAAKuM,OAASvM,KAAKuM,OAAO3K,KAAK5B,MAC/BA,KAAKiC,MAAQjC,KAAKiC,MAAML,KAAK5B,MAC7BA,KAAKmC,KAAOnC,KAAKmC,KAAKP,KAAK5B,MAE3BA,KAAKJ,OAASI,KAAKJ,OAAOgC,KAAK5B,MAC/BA,KAAKJ,OAAOmK,EAAWlK,OAAOC,KAAKC,aAEnCC,KAAK0H,QAAU,GACf1H,KAAKwM,SAAW,GAChBxM,KAAKyM,UAAY,GACjBzM,KAAK0M,UAAY,KACjB1M,KAAK2M,OAAQ,EACb3M,KAAKoH,SAAW6C,EAAW7C,UAAY,GACvCpH,KAAKsH,eAAiB2C,EAAW2C,oBAAsB,EACvD5M,KAAKmJ,QAAUc,EAAWd,SAAW,KACrCnJ,KAAKN,OAASuK,EAAWvK,QAAU,GAEnCM,KAAKiF,MAAQgF,EAAWhF,OAAS,GAEjCjF,KAAK6M,aAAc,OAEYxM,IAA3B4J,EAAW4C,cACb7M,KAAK6M,YAAc5C,EAAW4C,aAGhC7M,KAAKwH,eAAgB,OAEYnH,IAA7B4J,EAAWzC,gBACbxH,KAAKwH,cAAgByC,EAAWzC,eAGlCxH,KAAK8M,cAAe,OAEYzM,IAA5B4J,EAAW6C,eACb9M,KAAK8M,aAAe7C,EAAW6C,cAGjC9M,KAAK8F,mBAAoB,OAEYzF,IAAjC4J,EAAWnE,oBACb9F,KAAK8F,kBAAoBmE,EAAWnE,mBAEtC9F,KAAK+M,UAAY9C,EAAW8C,WAjI9B,yFAmIE/M,KAAKgN,eAAiB/C,EAAW+C,gBAjIb,MAmIpBhN,KAAKiN,WAAahD,EAAWgD,YAAcnD,EAE3C9J,KAAKkN,WAAajD,EAAWiD,YAAczD,EAE3CzJ,KAAKmN,UAAYlD,EAAWkD,WA9Ib,IAgJfnN,KAAKoN,YAAcnD,EAAWmD,aA9IZ,IAgJlBpN,KAAKqN,aAAepD,EAAWoD,cAvIX,WAyIuC,IAAvDC,UAAUC,UAAUC,cAAcC,QAAQ,UAE5CzN,KAAKmN,UAAYnN,KAAKoN,aAGxBpN,KAAK0N,eAAiB,KAEtB1N,KAAK8L,cAEL9L,KAAKkM,WAAWlC,GAAY,GAE3BhK,KAA4CyB,OAAOyB,EACrD,CAqnBH,OAnnBE6G,EAAAtH,UAAA0I,cAAA,mBACEnL,KAAKwL,eAAc,YAQhB3H,EAAA7D,KAA4C0B,+BAAU6B,cAAgBvD,KAA4C2N,IAAI,YAAc,eACpI3N,KAA4C0B,+BAAU6B,aAAevD,KAA4C2N,IAAI,YAEtH9N,OAAOC,KAAK4C,MAAME,QAAQ5C,KAAM,SAIpC+J,EAAAtH,UAAA6J,OAAA,WACEtM,KAAKuM,UAGPxC,EAAAtH,UAAAR,MAAA,WACE,IAAMiB,EAAOlD,KAA4C0B,SAEzD1B,KAAK0M,UAAYxJ,EAEjBlD,KAAK2M,OAAQ,EAEb3M,KAAKqM,UAEO,OAARnJ,IAEFlD,KAAKyM,UAAY,CACf5M,OAAOC,KAAK4C,MAAMuB,YAChBf,EACA,eACAlD,KAAKmL,eAEPtL,OAAOC,KAAK4C,MAAMuB,YAChBf,EACA,OACAlD,KAAKsM,WAMbvC,EAAAtH,UAAAP,SAAA,WAEE,IAAqB,IAAAqG,EAAA,EAAA1E,EAAA7D,KAAK0H,QAALa,EAAY1E,EAAA+C,OAAZ2B,IAAc,CAA9B,IAAMI,EAAM9E,EAAA0E,GACXI,EAAOjH,WAAa1B,KAAK0M,WAC3B/D,EAAOlH,OAAOzB,KAAK0M,UAEtB,CAGD,IAAsB,IAAAjH,EAAA,EAAAQ,EAAAjG,KAAKwM,SAAL/G,EAAaQ,EAAAW,OAAbnB,IAAe,CAAnBQ,EAAAR,GACRuC,QACT,CAEDhI,KAAKwM,SAAW,GAGhB,IAAuB,IAAApG,EAAA,EAAAC,EAAArG,KAAKyM,UAALrG,EAAcC,EAAAO,OAAdR,IAAgB,CAAlC,IAAMwH,EAAQvH,EAAAD,GACjBvG,OAAOC,KAAK4C,MAAM0B,eAAewJ,EAClC,CAED5N,KAAKyM,UAAY,GAEjBzM,KAAK0M,UAAY,KAEjB1M,KAAK2M,OAAQ,GAGf5C,EAAAtH,UAAAN,KAAA,WAAqB,EAErB4H,EAAAtH,UAAAf,OAAA,WAAiB,OAAO,IAAI,EAE5BqI,EAAAtH,UAAAqB,SAAA,WAAmB,OAAO,IAAI,EAE9BiG,EAAAtH,UAAAuE,cAAA,WACE,MAAO,CACL6G,2BAAqC,WAAA,OAAO,IAAM,EAClDC,qBAA+B,WAAA,OAAO,IAAK,EAC3CC,2BAAqC,WAAA,OAAO,IAAK,EACjD9G,qBAA+B,WAAA,OAAO,IAAK,EAC3C+G,iBAA2B,WAAA,OAAO,IAAM,EACxCC,cAA0B,WAAA,OAAO,CAAG,IAIxClE,EAAAtH,UAAAhB,OAAA,WAAuB,EAEvBsI,EAAAtH,UAAAwB,YAAA,WACE,MAAO,CACL+D,OAAM,WAAa,IAIvB+B,EAAAtH,UAAAyL,OAAA,WAAuB,EAEvBnE,EAAAtH,UAAAkL,IAAA,WAAoB,EAEpB5D,EAAAtH,UAAA0L,OAAA,WAAuB,EAEvBpE,EAAAtH,UAAA2L,IAAA,WAAoB,EACpBrE,EAAAtH,UAAA4L,UAAA,WAA0B,EAC1BtE,EAAAtH,UAAA6L,OAAA,WAAuB,EACvBvE,EAAAtH,UAAA8L,UAAA,WAA0B,EAE1BxE,EAAAtH,UAAAqJ,YAAA,WACE,KAAI9L,KAAKN,OAAOkH,OAAS,GAIzB,IAAK,IAAI4C,EAAI,EAAGA,EAAIxJ,KAAKiN,WAAWrG,OAAQ4C,IAC1CxJ,KAAKN,OAAOuJ,KAAK,CACfvI,IAAK,GAAAiE,OAAG3E,KAAK+M,WAAavD,EAAI,GAAE,KAAA7E,OAAI3E,KAAKgN,gBACzCrM,OAAQX,KAAKiN,WAAWzD,IAAM,EAC9B5I,MAAOZ,KAAKiN,WAAWzD,IAAM,KAKnCO,EAAAtH,UAAAiI,gBAAA,WAKE,IAJA,IAAMhD,EAAU1H,KAAK8H,aAEfH,EAAS,IAAI9H,OAAOC,KAAKwI,aAEVC,EAAA,EAAAC,EAAOd,EAAPa,WAAAA,IAAS,CAAzB,IACGE,EADSD,EAAAD,GACSG,cAEpBD,GACFd,EAAO/H,OAAO6I,EAEjB,CAED,IAAMvF,EAAOlD,KAA4C0B,SAE7C,OAARwB,GAAgB,cAAeA,GACjCA,EAAIC,UAAUwE,IAKlBoC,EAAAtH,UAAA4E,YAAA,WACE,OAAOrH,KAAKoH,UAGd2C,EAAWtH,UAAAsJ,YAAX,SAAY3E,GACVpH,KAAKoH,SAAWA,GAGlB2C,EAAAtH,UAAA8E,sBAAA,WACE,OAAOvH,KAAKsH,gBAGdyC,EAAqBtH,UAAAyH,sBAArB,SAAsB0C,GACpB5M,KAAKsH,eAAiBsF,GAGxB7C,EAAAtH,UAAAM,WAAA,WACE,OAAO/C,KAAKmJ,SAGdY,EAAUtH,UAAAwJ,WAAV,SAAW9C,GACTnJ,KAAKmJ,QAAUA,GAGjBY,EAAAtH,UAAAgE,UAAA,WACE,OAAOzG,KAAKN,QAGdqK,EAAStH,UAAA0J,UAAT,SAAUzM,GACRM,KAAKN,OAASA,GAGhBqK,EAAAtH,UAAAyC,SAAA,WACE,OAAOlF,KAAKiF,OAGd8E,EAAQtH,UAAA2J,SAAR,SAASnH,GACPjF,KAAKiF,MAAQA,GAGf8E,EAAAtH,UAAAI,eAAA,WACE,OAAO7C,KAAK6M,aAGd9C,EAActH,UAAAsI,eAAd,SAAe8B,GACb7M,KAAK6M,YAAcA,GAGrB9C,EAAAtH,UAAAgF,iBAAA,WACE,OAAOzH,KAAKwH,eAGduC,EAAgBtH,UAAA+H,iBAAhB,SAAiBhD,GACfxH,KAAKwH,cAAgBA,GAGvBuC,EAAAtH,UAAAkI,gBAAA,WACE,OAAO3K,KAAK8M,cAGd/C,EAAetH,UAAAmI,gBAAf,SAAgBkC,GACd9M,KAAK8M,aAAeA,GAGtB/C,EAAAtH,UAAA0H,qBAAA,WACE,OAAOnK,KAAK8F,mBAGdiE,EAAoBtH,UAAA2H,qBAApB,SAAqBtE,GACnB9F,KAAK8F,kBAAoBA,GAG3BiE,EAAAtH,UAAA6H,kBAAA,WACE,OAAOtK,KAAKgN,gBAGdjD,EAAiBtH,UAAA8H,kBAAjB,SAAkByC,GAChBhN,KAAKgN,eAAiBA,GAGxBjD,EAAAtH,UAAAgJ,aAAA,WACE,OAAOzL,KAAK+M,WAGdhD,EAAYtH,UAAAiJ,aAAZ,SAAaqB,GACX/M,KAAK+M,UAAYA,GAGnBhD,EAAAtH,UAAA2I,cAAA,WACE,OAAOpL,KAAKiN,YAGdlD,EAAatH,UAAA4I,cAAb,SAAc4B,GACZjN,KAAKiN,WAAaA,GAGpBlD,EAAAtH,UAAA6G,cAAA,WACE,OAAOtJ,KAAKkN,YAGdnD,EAAatH,UAAA6I,cAAb,SAAc4B,GACZlN,KAAKkN,WAAaA,GAGpBnD,EAAAtH,UAAAuI,eAAA,WACE,OAAOhL,KAAKoN,aAGdrD,EAActH,UAAAwI,eAAd,SAAemC,GACbpN,KAAKoN,YAAcA,GAGrBrD,EAAAtH,UAAAvC,gBAAA,WACE,OAAOF,KAAKqN,cAGdtD,EAAetH,UAAAoI,gBAAf,SAAgBwC,GACdrN,KAAKqN,aAAeA,GAGtBtD,EAAAtH,UAAAqF,WAAA,WACE,OAAO9H,KAAK0H,SAGdqC,EAAAtH,UAAAqI,gBAAA,WACE,OAAO9K,KAAK0H,QAAQd,QAGtBmD,EAAAtH,UAAAuJ,YAAA,WACE,OAAOhM,KAAKwM,UAGdzC,EAAAtH,UAAAgI,iBAAA,WACE,OAAOzK,KAAKwM,SAAS5F,QAGvBmD,EAAAtH,UAAAwF,UAAA,SAAUU,EAAwB6F,GAChCxO,KAAK2L,aAAahD,GAEb6F,GACHxO,KAAKuM,UAITxC,EAAAtH,UAAAyJ,WAAA,SAAWxE,EAA2B8G,GACpC,IAAK,IAAMC,KAAO/G,EAChB,GAAIgH,OAAOjM,UAAUkM,eAAeC,KAAKlH,EAAS+G,GAAM,CACtD,IAAM9F,EAASjB,EAAQ+G,GAEnB9F,GACF3I,KAAK2L,aAAahD,EAErB,CAGE6F,GACHxO,KAAKuM,UAITxC,EAAYtH,UAAAkJ,aAAZ,SAAahD,GAAb,IAeCkG,EAAA7O,KAbK2I,EAAOmG,gBACTjP,OAAOC,KAAK4C,MAAMuB,YAAY0E,EAAQ,WAAW,WAC3CkG,EAAKlC,QACPhE,EAAOK,SAAU,EAEjB6F,EAAKxC,UAET,IAGF1D,EAAOK,SAAU,EAEjBhJ,KAAK0H,QAAQuB,KAAKN,IAGpBoB,EAAatH,UAAAsM,cAAb,SAAcpG,GACZ,IAAI7B,GAAS,EAEb,GAAI9G,KAAK0H,QAAQ+F,QACf3G,EAAQ9G,KAAK0H,QAAQ+F,QAAQ9E,QAE7B,IAAK,IAAIa,EAAI,EAAGA,EAAIxJ,KAAK0H,QAAQd,OAAQ4C,IACvC,GAAIb,IAAW3I,KAAK0H,QAAQ8B,GAAI,CAC9B1C,EAAQ0C,EAER,KACD,CAIL,OAAe,IAAX1C,IAKJ6B,EAAOlH,OAAO,MAEdzB,KAAK0H,QAAQsH,OAAOlI,EAAO,IAEpB,IAGTiD,EAAAtH,UAAAmJ,aAAA,SAAajD,EAAwB6F,GACnC,IAAMS,EAAUjP,KAAK+O,cAAcpG,GAMnC,OAJK6F,GAAaS,GAChBjP,KAAKqM,UAGA4C,GAGTlF,EAAAtH,UAAA8I,cAAA,SAAc7D,EAA2B8G,GAGvC,IAFA,IAAIS,GAAU,EAEO1G,EAAA,EAAA2G,EAAOxH,EAAPa,WAAAA,IAAS,CAAzB,IAAMI,EAAMuG,EAAA3G,GACf0G,EAAUA,GAAWjP,KAAK+O,cAAcpG,EACzC,CAMD,OAJK6F,GAAaS,GAChBjP,KAAKqM,UAGA4C,GAGTlF,EAAAtH,UAAAoJ,aAAA,WACE7L,KAAKwL,eAAc,GAEnBxL,KAAK0H,QAAU,IAGjBqC,EAAAtH,UAAA4J,QAAA,WACE,IAAM8C,EAAcnP,KAAKwM,SAAS4C,QAElCpP,KAAKwM,SAAW,GAEhBxM,KAAKwL,eAAc,GAEnBxL,KAAKuM,SAILlJ,YAAW,WACT,IAAyB,IAAAkF,EAAA,EAAA8G,EAAWF,EAAX5G,WAAAA,IAAa,CAAjB8G,EAAA9G,GACRP,QACZ,CACF,GAAE,IAGL+B,EAAiBtH,UAAA4G,kBAAjB,SAAkB1B,GAChB,IAAM2H,EAActP,KAA4CgH,gBAG1DuI,EAAQD,EAAWrI,qBAEvB,IAAIpH,OAAOC,KAAK+I,OAAOlB,EAAO6H,eAAe1G,MAAOnB,EAAO6H,eAAezG,QAG9D,OAAVwG,IACFA,EAAMzK,GAAK9E,KAAKoH,SAChBmI,EAAM3K,GAAK5E,KAAKoH,UAGlB,IAAMqI,EAAQH,EAAWrI,qBAEvB,IAAIpH,OAAOC,KAAK+I,OAAOlB,EAAO+H,eAAe5G,MAAOnB,EAAO+H,eAAe3G,QAU5E,GAPc,OAAV0G,IACFA,EAAM3K,GAAK9E,KAAKoH,SAChBqI,EAAM7K,GAAK5E,KAAKoH,UAKJ,OAAVmI,EAAgB,CAElB,IAAMI,EAASL,EAAWxB,qBAAqByB,GAEhC,OAAXI,GACFhI,EAAO/H,OAAO+P,EAEjB,CAED,GAAc,OAAVF,EAAgB,CAElB,IAAMG,EAAUN,EAAWxB,qBAAqB2B,GAEjC,OAAXG,GACFjI,EAAO/H,OACLgQ,EAGL,CAGD,OAAOjI,GAGToC,EAAAtH,UAAA8J,OAAA,WAEEvM,KAAKkL,eAAe,IAGtBnB,EAAatH,UAAA+I,cAAb,SAAcqE,GAEZ,IAAsB,IAAAtH,EAAA,EAAA1E,EAAA7D,KAAKwM,SAALjE,EAAa1E,EAAA+C,OAAb2B,IAAe,CAAnB1E,EAAA0E,GACRP,QACT,CAEDhI,KAAKwM,SAAW,GAGhB,IAAqB,IAAA/G,EAAA,EAAAQ,EAAAjG,KAAK0H,QAALjC,EAAYQ,EAAAW,OAAZnB,IAAc,CAA9B,IAAMkD,EAAM1C,EAAAR,GACfkD,EAAOK,SAAU,EAEb6G,GACFlH,EAAOlH,OAAO,KAEjB,GAGHsI,EAAAtH,UAAAqN,sBAAA,SAAsBC,EAAwBC,GAC5C,IAEMC,GAASD,EAAGlH,MAAQiH,EAAGjH,OAASpC,KAAKwJ,GAAM,IAC3CC,GAASH,EAAGjH,MAAQgH,EAAGhH,OAASrC,KAAKwJ,GAAM,IAE3CE,EACJ1J,KAAK2J,IAAIJ,EAAO,GAAKvJ,KAAK2J,IAAIJ,EAAO,GACrCvJ,KAAK4J,IAAKP,EAAGjH,MAAQpC,KAAKwJ,GAAM,KAC9BxJ,KAAK4J,IAAKN,EAAGlH,MAAQpC,KAAKwJ,GAAM,KAChCxJ,KAAK2J,IAAIF,EAAO,GAChBzJ,KAAK2J,IAAIF,EAAO,GAEpB,OAAY,EAAIzJ,KAAK6J,MAAM7J,KAAK8J,KAAKJ,GAAI1J,KAAK8J,KAAK,EAAIJ,IAZ7C,MAeZrG,EAAAtH,UAAAgO,iBAAA,SAAiB9H,EAAwBhB,GACvC,IAAMc,EAAWE,EAAOD,cAExB,QAAID,GACKd,EAAOyB,SAASX,IAM3BsB,EAAmBtH,UAAA4H,oBAAnB,SAAoB1B,GAOlB,IANA,IAAIlJ,EAEAiR,EAAW,IAEXC,EAAiB,KAEQpI,EAAA,EAAA1E,EAAA7D,KAAKwM,SAALjE,EAAa1E,EAAA+C,OAAb2B,IAAe,CAAvC,IAGGnI,GAFNX,EADuBoE,EAAA0E,IAGAR,YAEjBU,EAAWE,EAAOD,cAExB,GAAItI,GAAUqI,EAAU,CACtB,IAAMmI,EAAI5Q,KAAK8P,sBAAsB1P,EAAQqI,GAEzCmI,EAAIF,IACNA,EAAWE,EAEXD,EAAiBlR,EAEpB,CACF,CAEGkR,GAAkBA,EAAezI,wBAAwBS,GAC3DgI,EAAe1I,UAAUU,KAEzBlJ,EAAU,IAAIyH,EAAQlH,OAEdiI,UAAUU,GAElB3I,KAAKwM,SAASvD,KAAKxJ,KAIvBsK,EAActH,UAAAyI,eAAd,SAAe2F,GAAf,IA+EChC,EAAA7O,KA9EC,GAAKA,KAAK2M,MAAV,CAKe,IAAXkE,IAQFhR,OAAOC,KAAK4C,MAAME,QAAQ5C,KAAM,kBAAmBA,MAEvB,OAAxBA,KAAK0N,iBACPtK,OAAOmB,aAAavE,KAAK0N,uBAIlB1N,KAAK0N,iBA2BhB,IAvBA,IAAMxK,EAAOlD,KAA4C0B,SAEnDiG,GAAiB,OAARzE,GAAgB,cAAeA,EAAMA,EAAID,YAAc,MAOhE6N,IALQ5N,aAAA,EAAAA,EAAKK,YAAa,GAKP,EACnB,IAAI1D,OAAOC,KAAKwI,aACdX,aAAM,EAANA,EAAQ+H,eACR/H,aAAM,EAANA,EAAQ6H,gBAEV,IAAI3P,OAAOC,KAAKwI,aACd,IAAIzI,OAAOC,KAAK+I,OAAO,mBAAoB,iBAC3C,IAAIhJ,OAAOC,KAAK+I,QAAQ,kBAAmB,kBAG7CkI,EAAoB/Q,KAAKqJ,kBAAkByH,GAE3CE,EAAQtK,KAAKC,IAAIkK,EAAS7Q,KAAKmN,UAAWnN,KAAK0H,QAAQd,QAEpD4C,EAAIqH,EAAQrH,EAAIwH,EAAOxH,IAAK,CACnC,IAAMb,EAAS3I,KAAK0H,QAAQ8B,GAExBb,IAAWA,EAAOK,SAAWhJ,KAAKyQ,iBAAiB9H,EAAQoI,MAAwB/Q,KAAK8M,cAAiB9M,KAAK8M,cAAgBnE,EAAOsI,eACvIjR,KAAKqK,oBAAoB1B,EAE5B,CAED,GAAIqI,EAAQhR,KAAK0H,QAAQd,OACvB5G,KAAK0N,eAAiBtK,OAAOC,YAC3B,WACEwL,EAAK3D,eAAe8F,EACrB,GACD,OAEG,CACLhR,KAAK0N,eAAiB,KAStB7N,OAAOC,KAAK4C,MAAME,QAAQ5C,KAAM,gBAAiBA,MAEjD,IAAsB,IAAAuI,EAAA,EAAA1E,EAAA7D,KAAKwM,SAALjE,EAAa1E,EAAA+C,OAAb2B,IAAe,CAAnB1E,EAAA0E,GACRH,YACT,CACF,CA3EA,GA8EH2B,EAAAtH,UAAA7C,OAAA,SAAwDsR,EAASC,GAC/D,OAAO,SAA8BC,GACnC,IAAK,IAAMC,KAAYD,EAAO3O,UAAW,CAGvC,IAAM6O,EAAOD,EAIbrR,KAAKyC,UAAU6O,GAAQF,EAAO3O,UAAU6O,EACzC,CAED,OAAOtR,IACR,EAACuR,MAA8CL,EAAM,CAACC,KAE1DpH,CAAD"}