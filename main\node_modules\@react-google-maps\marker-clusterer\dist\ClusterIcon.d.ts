import type { Cluster } from './Cluster';
import type { ClusterIconStyle, ClusterIconInfo } from './types';
export declare class ClusterIcon {
    cluster: Cluster;
    className: string;
    clusterClassName: string;
    styles: ClusterIconStyle[];
    center: google.maps.LatLng | undefined;
    div: HTMLDivElement | null;
    sums: ClusterIconInfo | null;
    visible: boolean;
    url: string;
    height: number;
    width: number;
    anchorText: [number, number];
    anchorIcon: [number, number];
    textColor: string;
    textSize: number;
    textDecoration: string;
    fontWeight: string;
    fontStyle: string;
    fontFamily: string;
    backgroundPosition: string;
    cMouseDownInCluster: boolean | null;
    cDraggingMapByCluster: boolean | null;
    timeOut: number | null;
    boundsChangedListener: google.maps.MapsEventListener | null;
    constructor(cluster: Cluster, styles: ClusterIconStyle[]);
    onBoundsChanged(): void;
    onMouseDown(): void;
    onClick(event: Event): void;
    onMouseOver(): void;
    onMouseOut(): void;
    onAdd(): void;
    onRemove(): void;
    draw(): void;
    hide(): void;
    show(): void;
    useStyle(sums: ClusterIconInfo): void;
    setCenter(center: google.maps.LatLng): void;
    getPosFromLatLng(latlng: google.maps.LatLng): google.maps.Point | null;
}
