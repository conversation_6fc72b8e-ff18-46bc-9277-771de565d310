/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"816be40f0325\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxBcHAgcXXhuqNuIGzDvSBjaGkgdGnDqnVcXGFwcCBj4bunYSBt4bq5XFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODE2YmU0MGYwMzI1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Pacifico\",\"arguments\":[{\"weight\":\"400\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-pacifico\"}],\"variableName\":\"pacifico\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Pacifico\\\",\\\"arguments\\\":[{\\\"weight\\\":\\\"400\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-pacifico\\\"}],\\\"variableName\\\":\\\"pacifico\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"Readdy Site\",\n    description: \"Generated by Readdy\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Pacifico_arguments_weight_400_subsets_latin_display_swap_variable_font_pacifico_variableName_pacifico___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center h-screen text-center px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl md:text-5xl font-semibold text-gray-100\",\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\not-found.tsx\",\n                lineNumber: 4,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl md:text-3xl font-semibold mt-6\",\n                children: \"This page has not been generated\"\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\not-found.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-4 text-xl md:text-2xl text-gray-500\",\n                children: \"Tell me what you would like on this page\"\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\not-found.tsx\",\n        lineNumber: 3,\n        columnNumber: 7\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0E7SUFDcEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBbUQ7Ozs7OzswQkFDakUsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQzs7Ozs7OzBCQUN4RCw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQXlDOzs7Ozs7Ozs7Ozs7QUFHNUQiLCJzb3VyY2VzIjpbIkQ6XFxBcHAgcXXhuqNuIGzDvSBjaGkgdGnDqnVcXGFwcCBj4bunYSBt4bq5XFxhcHBcXG5vdC1mb3VuZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1zY3JlZW4gdGV4dC1jZW50ZXIgcHgtNFwiPlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC01eGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktMTAwXCI+NDA0PC9oMT5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIG1kOnRleHQtM3hsIGZvbnQtc2VtaWJvbGQgbXQtNlwiPlRoaXMgcGFnZSBoYXMgbm90IGJlZW4gZ2VuZXJhdGVkPC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LXhsIG1kOnRleHQtMnhsIHRleHQtZ3JheS01MDBcIj5UZWxsIG1lIHdoYXQgeW91IHdvdWxkIGxpa2Ugb24gdGhpcyBwYWdlPC9wPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfSJdLCJuYW1lcyI6WyJOb3RGb3VuZCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\App quản lý chi tiêu\\\\app của mẹ\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\App quản lý chi tiêu\\app của mẹ\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\App quản lý chi tiêu\\\\app của mẹ\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\App quản lý chi tiêu\\\\app của mẹ\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"D:\\\\App quản lý chi tiêu\\\\app của mẹ\\\\app\\\\not-found.tsx\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\App quản lý chi tiêu\\\\app của mẹ\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBcHAlMjBxdSVFMSVCQSVBM24lMjBsJUMzJUJEJTIwY2hpJTIwdGklQzMlQUF1JTVDJTVDYXBwJTIwYyVFMSVCQiVBN2ElMjBtJUUxJUJBJUI5JTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUF3RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQXBwIHF14bqjbiBsw70gY2hpIHRpw6p1XFxcXGFwcCBj4bunYSBt4bq5XFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/components/DashboardStats.tsx":
/*!*******************************************!*\
  !*** ./app/components/DashboardStats.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction DashboardStats({ totalIncome, totalExpenses, totalInvestments, totalInvestmentProfitLoss, currentBalance }) {\n    const stats = [\n        {\n            title: 'Tổng thu nhập',\n            value: totalIncome,\n            icon: 'ri-arrow-up-circle-fill',\n            color: 'text-green-600',\n            bgColor: 'bg-green-50'\n        },\n        {\n            title: 'Tổng chi tiêu',\n            value: totalExpenses,\n            icon: 'ri-arrow-down-circle-fill',\n            color: 'text-red-600',\n            bgColor: 'bg-red-50'\n        },\n        {\n            title: 'Tổng đầu tư',\n            value: totalInvestments,\n            icon: 'ri-line-chart-fill',\n            color: 'text-blue-600',\n            bgColor: 'bg-blue-50'\n        },\n        {\n            title: 'Tổng lãi/lỗ đầu tư',\n            value: totalInvestmentProfitLoss,\n            icon: totalInvestmentProfitLoss >= 0 ? 'ri-trend-up-fill' : 'ri-trend-down-fill',\n            color: totalInvestmentProfitLoss >= 0 ? 'text-green-600' : 'text-red-600',\n            bgColor: totalInvestmentProfitLoss >= 0 ? 'bg-green-50' : 'bg-red-50'\n        },\n        {\n            title: 'Số dư hiện tại',\n            value: currentBalance,\n            icon: 'ri-wallet-3-fill',\n            color: currentBalance >= 0 ? 'text-green-600' : 'text-red-600',\n            bgColor: currentBalance >= 0 ? 'bg-green-50' : 'bg-red-50'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-600\",\n                                    children: stat.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-2xl font-bold ${stat.color}`,\n                                    children: [\n                                        stat.value >= 0 && stat.title === 'Tổng lãi/lỗ đầu tư' ? '+' : '',\n                                        stat.value.toLocaleString(),\n                                        \"₫\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-12 h-12 flex items-center justify-center rounded-lg ${stat.bgColor}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: `${stat.icon} ${stat.color} text-xl`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this)\n            }, index, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\DashboardStats.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9EYXNoYm9hcmRTdGF0cy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQVdlLFNBQVNBLGVBQWUsRUFDckNDLFdBQVcsRUFDWEMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLHlCQUF5QixFQUN6QkMsY0FBYyxFQUNNO0lBQ3BCLE1BQU1DLFFBQVE7UUFDWjtZQUNFQyxPQUFPO1lBQ1BDLE9BQU9QO1lBQ1BRLE1BQU07WUFDTkMsT0FBTztZQUNQQyxTQUFTO1FBQ1g7UUFDQTtZQUNFSixPQUFPO1lBQ1BDLE9BQU9OO1lBQ1BPLE1BQU07WUFDTkMsT0FBTztZQUNQQyxTQUFTO1FBQ1g7UUFDQTtZQUNFSixPQUFPO1lBQ1BDLE9BQU9MO1lBQ1BNLE1BQU07WUFDTkMsT0FBTztZQUNQQyxTQUFTO1FBQ1g7UUFDQTtZQUNFSixPQUFPO1lBQ1BDLE9BQU9KO1lBQ1BLLE1BQU1MLDZCQUE2QixJQUFJLHFCQUFxQjtZQUM1RE0sT0FBT04sNkJBQTZCLElBQUksbUJBQW1CO1lBQzNETyxTQUFTUCw2QkFBNkIsSUFBSSxnQkFBZ0I7UUFDNUQ7UUFDQTtZQUNFRyxPQUFPO1lBQ1BDLE9BQU9IO1lBQ1BJLE1BQU07WUFDTkMsT0FBT0wsa0JBQWtCLElBQUksbUJBQW1CO1lBQ2hETSxTQUFTTixrQkFBa0IsSUFBSSxnQkFBZ0I7UUFDakQ7S0FDRDtJQUVELHFCQUNFLDhEQUFDTztRQUFJQyxXQUFVO2tCQUNaUCxNQUFNUSxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ2hCLDhEQUFDSjtnQkFBZ0JDLFdBQVU7MEJBQ3pCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ0s7b0NBQUVKLFdBQVU7OENBQXFDRSxLQUFLUixLQUFLOzs7Ozs7OENBQzVELDhEQUFDVTtvQ0FBRUosV0FBVyxDQUFDLG1CQUFtQixFQUFFRSxLQUFLTCxLQUFLLEVBQUU7O3dDQUM3Q0ssS0FBS1AsS0FBSyxJQUFJLEtBQUtPLEtBQUtSLEtBQUssS0FBSyx1QkFBdUIsTUFBTTt3Q0FBSVEsS0FBS1AsS0FBSyxDQUFDVSxjQUFjO3dDQUFHOzs7Ozs7Ozs7Ozs7O3NDQUdwRyw4REFBQ047NEJBQUlDLFdBQVcsQ0FBQyxzREFBc0QsRUFBRUUsS0FBS0osT0FBTyxFQUFFO3NDQUNyRiw0RUFBQ1E7Z0NBQUVOLFdBQVcsR0FBR0UsS0FBS04sSUFBSSxDQUFDLENBQUMsRUFBRU0sS0FBS0wsS0FBSyxDQUFDLFFBQVEsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7ZUFUOUNNOzs7Ozs7Ozs7O0FBZ0JsQiIsInNvdXJjZXMiOlsiRDpcXEFwcCBxdeG6o24gbMO9IGNoaSB0acOqdVxcYXBwIGPhu6dhIG3hurlcXGFwcFxcY29tcG9uZW50c1xcRGFzaGJvYXJkU3RhdHMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuJ3VzZSBjbGllbnQnO1xuXG5pbnRlcmZhY2UgRGFzaGJvYXJkU3RhdHNQcm9wcyB7XG4gIHRvdGFsSW5jb21lOiBudW1iZXI7XG4gIHRvdGFsRXhwZW5zZXM6IG51bWJlcjtcbiAgdG90YWxJbnZlc3RtZW50czogbnVtYmVyO1xuICB0b3RhbEludmVzdG1lbnRQcm9maXRMb3NzOiBudW1iZXI7XG4gIGN1cnJlbnRCYWxhbmNlOiBudW1iZXI7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFN0YXRzKHsgXG4gIHRvdGFsSW5jb21lLCBcbiAgdG90YWxFeHBlbnNlcywgXG4gIHRvdGFsSW52ZXN0bWVudHMsIFxuICB0b3RhbEludmVzdG1lbnRQcm9maXRMb3NzLFxuICBjdXJyZW50QmFsYW5jZSBcbn06IERhc2hib2FyZFN0YXRzUHJvcHMpIHtcbiAgY29uc3Qgc3RhdHMgPSBbXG4gICAge1xuICAgICAgdGl0bGU6ICdU4buVbmcgdGh1IG5o4bqtcCcsXG4gICAgICB2YWx1ZTogdG90YWxJbmNvbWUsXG4gICAgICBpY29uOiAncmktYXJyb3ctdXAtY2lyY2xlLWZpbGwnLFxuICAgICAgY29sb3I6ICd0ZXh0LWdyZWVuLTYwMCcsXG4gICAgICBiZ0NvbG9yOiAnYmctZ3JlZW4tNTAnLFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICdU4buVbmcgY2hpIHRpw6p1JyxcbiAgICAgIHZhbHVlOiB0b3RhbEV4cGVuc2VzLFxuICAgICAgaWNvbjogJ3JpLWFycm93LWRvd24tY2lyY2xlLWZpbGwnLFxuICAgICAgY29sb3I6ICd0ZXh0LXJlZC02MDAnLFxuICAgICAgYmdDb2xvcjogJ2JnLXJlZC01MCcsXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ1Thu5VuZyDEkeG6p3UgdMawJyxcbiAgICAgIHZhbHVlOiB0b3RhbEludmVzdG1lbnRzLFxuICAgICAgaWNvbjogJ3JpLWxpbmUtY2hhcnQtZmlsbCcsXG4gICAgICBjb2xvcjogJ3RleHQtYmx1ZS02MDAnLFxuICAgICAgYmdDb2xvcjogJ2JnLWJsdWUtNTAnLFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICdU4buVbmcgbMOjaS9s4buXIMSR4bqndSB0xrAnLFxuICAgICAgdmFsdWU6IHRvdGFsSW52ZXN0bWVudFByb2ZpdExvc3MsXG4gICAgICBpY29uOiB0b3RhbEludmVzdG1lbnRQcm9maXRMb3NzID49IDAgPyAncmktdHJlbmQtdXAtZmlsbCcgOiAncmktdHJlbmQtZG93bi1maWxsJyxcbiAgICAgIGNvbG9yOiB0b3RhbEludmVzdG1lbnRQcm9maXRMb3NzID49IDAgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtcmVkLTYwMCcsXG4gICAgICBiZ0NvbG9yOiB0b3RhbEludmVzdG1lbnRQcm9maXRMb3NzID49IDAgPyAnYmctZ3JlZW4tNTAnIDogJ2JnLXJlZC01MCcsXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ1Phu5EgZMawIGhp4buHbiB04bqhaScsXG4gICAgICB2YWx1ZTogY3VycmVudEJhbGFuY2UsXG4gICAgICBpY29uOiAncmktd2FsbGV0LTMtZmlsbCcsXG4gICAgICBjb2xvcjogY3VycmVudEJhbGFuY2UgPj0gMCA/ICd0ZXh0LWdyZWVuLTYwMCcgOiAndGV4dC1yZWQtNjAwJyxcbiAgICAgIGJnQ29sb3I6IGN1cnJlbnRCYWxhbmNlID49IDAgPyAnYmctZ3JlZW4tNTAnIDogJ2JnLXJlZC01MCcsXG4gICAgfSxcbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNSBnYXAtNlwiPlxuICAgICAge3N0YXRzLm1hcCgoc3RhdCwgaW5kZXgpID0+IChcbiAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+e3N0YXQudGl0bGV9PC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBmb250LWJvbGQgJHtzdGF0LmNvbG9yfWB9PlxuICAgICAgICAgICAgICAgIHtzdGF0LnZhbHVlID49IDAgJiYgc3RhdC50aXRsZSA9PT0gJ1Thu5VuZyBsw6NpL2zhu5cgxJHhuqd1IHTGsCcgPyAnKycgOiAnJ317c3RhdC52YWx1ZS50b0xvY2FsZVN0cmluZygpfeKCq1xuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0xMiBoLTEyIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbGcgJHtzdGF0LmJnQ29sb3J9YH0+XG4gICAgICAgICAgICAgIDxpIGNsYXNzTmFtZT17YCR7c3RhdC5pY29ufSAke3N0YXQuY29sb3J9IHRleHQteGxgfT48L2k+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJEYXNoYm9hcmRTdGF0cyIsInRvdGFsSW5jb21lIiwidG90YWxFeHBlbnNlcyIsInRvdGFsSW52ZXN0bWVudHMiLCJ0b3RhbEludmVzdG1lbnRQcm9maXRMb3NzIiwiY3VycmVudEJhbGFuY2UiLCJzdGF0cyIsInRpdGxlIiwidmFsdWUiLCJpY29uIiwiY29sb3IiLCJiZ0NvbG9yIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFwIiwic3RhdCIsImluZGV4IiwicCIsInRvTG9jYWxlU3RyaW5nIiwiaSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/DashboardStats.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SavingsBooks.tsx":
/*!*****************************************!*\
  !*** ./app/components/SavingsBooks.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SavingsBooks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SavingsBooks({ savingsBooks, onAdd, onUpdate, onDelete, onAddExpense, isPreview = false }) {\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExpenseForm, setShowExpenseForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showExpenseList, setShowExpenseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newBook, setNewBook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        initialAmount: '',\n        description: ''\n    });\n    const [newExpense, setNewExpense] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: '',\n        description: ''\n    });\n    const handleAddBook = (e)=>{\n        e.preventDefault();\n        if (!newBook.name || !newBook.initialAmount) return;\n        const amount = parseFloat(newBook.initialAmount);\n        onAdd({\n            name: newBook.name,\n            initialAmount: amount,\n            currentAmount: amount,\n            description: newBook.description,\n            createdDate: new Date().toISOString().split('T')[0]\n        });\n        setNewBook({\n            name: '',\n            initialAmount: '',\n            description: ''\n        });\n        setShowAddForm(false);\n    };\n    const handleAddExpense = (e, bookId)=>{\n        e.preventDefault();\n        if (!newExpense.amount || !newExpense.description) return;\n        const amount = parseFloat(newExpense.amount);\n        const book = savingsBooks.find((b)=>b.id === bookId);\n        if (!book || book.currentAmount < amount) return;\n        onAddExpense(bookId, {\n            amount,\n            description: newExpense.description,\n            date: new Date().toISOString().split('T')[0]\n        });\n        // Update current amount\n        onUpdate(bookId, {\n            currentAmount: book.currentAmount - amount\n        });\n        setNewExpense({\n            amount: '',\n            description: ''\n        });\n        setShowExpenseForm(null);\n    };\n    const formatNumber = (num)=>{\n        return num.replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');\n    };\n    const parseNumber = (str)=>{\n        return str.replace(/\\./g, '');\n    };\n    const handleInitialAmountChange = (value)=>{\n        // Remove all non-numeric characters except dots\n        const numericValue = value.replace(/[^0-9]/g, '');\n        setNewBook({\n            ...newBook,\n            initialAmount: numericValue\n        });\n    };\n    const handleExpenseAmountChange = (value)=>{\n        // Remove all non-numeric characters except dots\n        const numericValue = value.replace(/[^0-9]/g, '');\n        setNewExpense({\n            ...newExpense,\n            amount: numericValue\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Sổ tiết kiệm\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddForm(true),\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-add-line mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            \"Th\\xeam sổ mới\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this),\n            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Th\\xeam sổ tiết kiệm mới\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleAddBook,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"T\\xean sổ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newBook.name,\n                                                onChange: (e)=>setNewBook({\n                                                        ...newBook,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                                placeholder: \"V\\xed dụ: Sổ du lịch\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Số tiền ban đầu\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newBook.initialAmount ? formatNumber(newBook.initialAmount) : '',\n                                                        onChange: (e)=>handleInitialAmountChange(e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                                        placeholder: \"0\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                                        children: \"₫\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"M\\xf4 tả\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: newBook.description,\n                                        onChange: (e)=>setNewBook({\n                                                ...newBook,\n                                                description: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                        placeholder: \"M\\xf4 tả mục đ\\xedch sử dụng\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                        children: \"Th\\xeam sổ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddForm(false),\n                                        className: \"bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                        children: \"Hủy\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `grid grid-cols-1 ${isPreview ? 'md:grid-cols-1' : 'md:grid-cols-2 lg:grid-cols-3'} gap-6`,\n                children: savingsBooks.map((book)=>{\n                    const totalExpenses = book.expenses.reduce((sum, expense)=>sum + expense.amount, 0);\n                    const isEmptyBook = book.currentAmount <= 0;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white rounded-lg shadow p-6 ${isEmptyBook ? 'border-2 border-red-200' : ''}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: book.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onDelete(book.id),\n                                        className: \"w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-delete-bin-line text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this),\n                            isEmptyBook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-alert-line text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-700 font-medium text-sm\",\n                                            children: \"Sổ đ\\xe3 tất to\\xe1n\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Số tiền ban đầu:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: [\n                                                    book.initialAmount.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Đ\\xe3 chi ti\\xeau:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-red-600\",\n                                                children: [\n                                                    totalExpenses.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"C\\xf2n lại:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `font-bold ${isEmptyBook ? 'text-red-600' : 'text-green-600'}`,\n                                                children: [\n                                                    book.currentAmount.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this),\n                            book.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mt-3 italic\",\n                                children: book.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        !isEmptyBook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowExpenseForm(showExpenseForm === book.id ? null : book.id),\n                                            className: \"flex-1 bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-subtract-line mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"R\\xfat tiền\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 21\n                                        }, this),\n                                        book.expenses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowExpenseList(showExpenseList === book.id ? null : book.id),\n                                            className: \"flex-1 bg-gray-600 text-white px-3 py-2 rounded-md hover:bg-gray-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-list-check mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Lịch sử\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this),\n                            showExpenseForm === book.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"R\\xfat tiền từ sổ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: (e)=>handleAddExpense(e, book.id),\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Số tiền r\\xfat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: newExpense.amount ? formatNumber(newExpense.amount) : '',\n                                                                onChange: (e)=>handleExpenseAmountChange(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                                                placeholder: \"0\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                                                children: \"₫\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            \"Tối đa: \",\n                                                            book.currentAmount.toLocaleString(),\n                                                            \"₫\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Mục đ\\xedch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newExpense.description,\n                                                        onChange: (e)=>setNewExpense({\n                                                                ...newExpense,\n                                                                description: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                                        placeholder: \"Để l\\xe0m g\\xec?\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        className: \"bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                                        children: \"R\\xfat tiền\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowExpenseForm(null),\n                                                        className: \"bg-gray-300 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-400 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                                                        children: \"Hủy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 17\n                            }, this),\n                            showExpenseList === book.id && book.expenses.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Lịch sử chi ti\\xeau\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-48 overflow-y-auto\",\n                                        children: book.expenses.map((expense)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: expense.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(expense.date).toLocaleDateString('vi-VN')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-red-600\",\n                                                        children: [\n                                                            \"-\",\n                                                            expense.amount.toLocaleString(),\n                                                            \"₫\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, expense.id, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, book.id, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            savingsBooks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-book-line text-4xl mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mb-2\",\n                        children: \"Chưa c\\xf3 sổ tiết kiệm n\\xe0o\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: \"Tạo sổ tiết kiệm đầu ti\\xean để bắt đầu tiết kiệm\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this),\n                    !isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAddForm(true),\n                        className: \"mt-4 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-add-line mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, this),\n                            \"Th\\xeam sổ tiết kiệm\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this),\n            isPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowAddForm(true),\n                    className: \"w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 shadow-lg cursor-pointer flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-add-line text-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\SavingsBooks.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SavingsBooks.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/TransactionForm.tsx":
/*!********************************************!*\
  !*** ./app/components/TransactionForm.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransactionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction TransactionForm({ onSubmit }) {\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('income');\n    const [amount, setAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [displayAmount, setDisplayAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [date, setDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().toISOString().split('T')[0]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Investment profit/loss fields\n    const [profitLoss, setProfitLoss] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [displayProfitLoss, setDisplayProfitLoss] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [period, setPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('month');\n    const [showProfitLossSuggestions, setShowProfitLossSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const formatNumber = (num)=>{\n        return num.replace(/\\B(?=(\\d{3})+(?!\\d))/g, '.');\n    };\n    const parseNumber = (str)=>{\n        return str.replace(/\\./g, '');\n    };\n    const generateSuggestions = (input)=>{\n        const baseNum = parseNumber(input);\n        if (!baseNum || isNaN(Number(baseNum))) return [];\n        const suggestions = [];\n        const base = Number(baseNum);\n        if (base < 10) {\n            suggestions.push(base * 1000);\n            suggestions.push(base * 10000);\n            suggestions.push(base * 100000);\n            suggestions.push(base * 500000);\n            suggestions.push(base * 1000000);\n        } else if (base < 100) {\n            suggestions.push(base * 100);\n            suggestions.push(base * 1000);\n            suggestions.push(base * 10000);\n            suggestions.push(base * 50000);\n        } else if (base < 1000) {\n            suggestions.push(base * 10);\n            suggestions.push(base * 100);\n            suggestions.push(base * 1000);\n        } else {\n            suggestions.push(base);\n            suggestions.push(base * 10);\n            suggestions.push(base * 100);\n        }\n        return suggestions.filter((s)=>s > 0).slice(0, 5);\n    };\n    const handleAmountChange = (e)=>{\n        const value = e.target.value;\n        const numericValue = parseNumber(value);\n        if (numericValue === '' || /^\\d+$/.test(numericValue)) {\n            setDisplayAmount(numericValue ? formatNumber(numericValue) : '');\n            setAmount(numericValue);\n            setShowSuggestions(numericValue.length > 0);\n        }\n    };\n    const handleProfitLossChange = (e)=>{\n        const value = e.target.value;\n        let numericValue = parseNumber(value);\n        // Allow negative numbers for losses\n        if (value.startsWith('-')) {\n            numericValue = '-' + numericValue;\n        }\n        if (numericValue === '' || /^-?\\d+$/.test(numericValue)) {\n            setDisplayProfitLoss(numericValue ? numericValue.startsWith('-') ? '-' + formatNumber(numericValue.slice(1)) : formatNumber(numericValue) : '');\n            setProfitLoss(numericValue);\n            setShowProfitLossSuggestions(Math.abs(parseFloat(numericValue || '0')) > 0);\n        }\n    };\n    const selectSuggestion = (suggestion)=>{\n        setAmount(suggestion.toString());\n        setDisplayAmount(formatNumber(suggestion.toString()));\n        setShowSuggestions(false);\n    };\n    const selectProfitLossSuggestion = (suggestion)=>{\n        setProfitLoss(suggestion.toString());\n        setDisplayProfitLoss(suggestion < 0 ? '-' + formatNumber(Math.abs(suggestion).toString()) : formatNumber(suggestion.toString()));\n        setShowProfitLossSuggestions(false);\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!amount || !category || !description) return;\n        const transaction = {\n            type,\n            amount: parseFloat(amount),\n            category,\n            description,\n            date\n        };\n        // Add investment data if it's an investment transaction\n        if (type === 'investment' && profitLoss) {\n            transaction.investmentData = {\n                profitLoss: parseFloat(profitLoss),\n                period,\n                lastUpdated: new Date().toISOString()\n            };\n        }\n        onSubmit(transaction);\n        // Reset form\n        setAmount('');\n        setDisplayAmount('');\n        setCategory('');\n        setDescription('');\n        setDate(new Date().toISOString().split('T')[0]);\n        setProfitLoss('');\n        setDisplayProfitLoss('');\n        setPeriod('month');\n        setShowSuggestions(false);\n        setShowProfitLossSuggestions(false);\n    };\n    const categories = {\n        income: [\n            'Lương',\n            'Freelance',\n            'Kinh doanh',\n            'Lợi nhuận đầu tư',\n            'Khác'\n        ],\n        expense: [\n            'Tiền nhà',\n            'Thực phẩm',\n            'Giao thông',\n            'Giải trí',\n            'Y tế',\n            'Khác'\n        ],\n        investment: [\n            'Cổ phiếu',\n            'Trái phiếu',\n            'Bất động sản',\n            'Tiền điện tử',\n            'Quỹ tương hỗ',\n            'Khác'\n        ]\n    };\n    const typeLabels = {\n        income: 'Thu nhập',\n        expense: 'Chi tiêu',\n        investment: 'Đầu tư'\n    };\n    const periodLabels = {\n        week: 'Tuần',\n        month: 'Tháng',\n        year: 'Năm'\n    };\n    const suggestions = generateSuggestions(displayAmount);\n    const profitLossSuggestions = generateSuggestions(displayProfitLoss.replace('-', ''));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Loại\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    'income',\n                                    'expense',\n                                    'investment'\n                                ].map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setType(t),\n                                        className: `px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap cursor-pointer ${type === t ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                                        children: typeLabels[t]\n                                    }, t, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Số tiền\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: displayAmount,\n                                        onChange: handleAmountChange,\n                                        onFocus: ()=>setShowSuggestions(displayAmount.length > 0),\n                                        onBlur: ()=>setTimeout(()=>setShowSuggestions(false), 200),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                        placeholder: \"0\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                        children: \"₫\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            showSuggestions && suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto\",\n                                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>selectSuggestion(suggestion),\n                                        className: \"w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                        children: [\n                                            formatNumber(suggestion.toString()),\n                                            \"₫\"\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            type === 'investment' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"L\\xe3i/Lỗ\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: displayProfitLoss,\n                                        onChange: handleProfitLossChange,\n                                        onFocus: ()=>setShowProfitLossSuggestions(Math.abs(parseFloat(profitLoss || '0')) > 0),\n                                        onBlur: ()=>setTimeout(()=>setShowProfitLossSuggestions(false), 200),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12\",\n                                        placeholder: \"0 (nhập số \\xe2m nếu lỗ)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-2 text-sm text-gray-500\",\n                                        children: \"₫\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            showProfitLossSuggestions && profitLossSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto\",\n                                children: profitLossSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>selectProfitLossSuggestion(suggestion),\n                                                className: \"w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 text-green-600\",\n                                                children: [\n                                                    \"+\",\n                                                    formatNumber(suggestion.toString()),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>selectProfitLossSuggestion(-suggestion),\n                                                className: \"w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 last:border-b-0 text-red-600\",\n                                                children: [\n                                                    \"-\",\n                                                    formatNumber(suggestion.toString()),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Chu kỳ\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    'week',\n                                    'month',\n                                    'year'\n                                ].map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setPeriod(p),\n                                        className: `px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap cursor-pointer ${period === p ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'}`,\n                                        children: periodLabels[p]\n                                    }, p, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Danh mục\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: category,\n                                onChange: (e)=>setCategory(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8\",\n                                required: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Chọn danh mục\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this),\n                                    categories[type].map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: cat,\n                                            children: cat\n                                        }, cat, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Ng\\xe0y\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: date,\n                                onChange: (e)=>setDate(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"M\\xf4 tả\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: description,\n                        onChange: (e)=>setDescription(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\",\n                        placeholder: \"Nhập m\\xf4 tả\",\n                        required: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"submit\",\n                className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium whitespace-nowrap cursor-pointer\",\n                children: \"Th\\xeam giao dịch\"\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionForm.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/TransactionForm.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/TransactionList.tsx":
/*!********************************************!*\
  !*** ./app/components/TransactionList.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransactionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction TransactionList({ transactions, onDelete }) {\n    const getTypeColor = (type)=>{\n        switch(type){\n            case 'income':\n                return 'text-green-600 bg-green-50';\n            case 'expense':\n                return 'text-red-600 bg-red-50';\n            case 'investment':\n                return 'text-blue-600 bg-blue-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case 'income':\n                return 'ri-arrow-up-circle-fill';\n            case 'expense':\n                return 'ri-arrow-down-circle-fill';\n            case 'investment':\n                return 'ri-line-chart-fill';\n            default:\n                return 'ri-money-dollar-circle-fill';\n        }\n    };\n    const getTypeLabel = (type)=>{\n        switch(type){\n            case 'income':\n                return 'Thu nhập';\n            case 'expense':\n                return 'Chi tiêu';\n            case 'investment':\n                return 'Đầu tư';\n            default:\n                return type;\n        }\n    };\n    const getPeriodLabel = (period)=>{\n        switch(period){\n            case 'week':\n                return 'Tuần';\n            case 'month':\n                return 'Tháng';\n            case 'year':\n                return 'Năm';\n            default:\n                return period;\n        }\n    };\n    if (transactions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"ri-inbox-line text-4xl mb-2\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Chưa c\\xf3 giao dịch n\\xe0o\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Loại\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Số tiền\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"L\\xe3i/Lỗ\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Danh mục\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"M\\xf4 tả\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Ng\\xe0y\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"text-left py-3 px-4 font-medium text-gray-700\",\n                                children: \"Thao t\\xe1c\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"border-b border-gray-100 hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-8 h-8 flex items-center justify-center rounded-full ${getTypeColor(transaction.type)}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: `${getTypeIcon(transaction.type)} text-sm`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: getTypeLabel(transaction.type)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `font-medium ${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'}`,\n                                        children: [\n                                            transaction.type === 'income' ? '+' : '-',\n                                            transaction.amount.toLocaleString(),\n                                            \"₫\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: transaction.investmentData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `font-medium text-sm ${transaction.investmentData.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                                                children: [\n                                                    transaction.investmentData.profitLoss >= 0 ? '+' : '',\n                                                    transaction.investmentData.profitLoss.toLocaleString(),\n                                                    \"₫\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"/\",\n                                                    getPeriodLabel(transaction.investmentData.period)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4 text-sm text-gray-900\",\n                                    children: transaction.category\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4 text-sm text-gray-600\",\n                                    children: transaction.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4 text-sm text-gray-600\",\n                                    children: new Date(transaction.date).toLocaleDateString('vi-VN')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"py-3 px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onDelete(transaction.id),\n                                        className: \"w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-delete-bin-line text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, transaction.id, true, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\components\\\\TransactionList.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/TransactionList.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardStats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/DashboardStats */ \"(ssr)/./app/components/DashboardStats.tsx\");\n/* harmony import */ var _components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/TransactionForm */ \"(ssr)/./app/components/TransactionForm.tsx\");\n/* harmony import */ var _components_TransactionList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/TransactionList */ \"(ssr)/./app/components/TransactionList.tsx\");\n/* harmony import */ var _components_SavingsBooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/SavingsBooks */ \"(ssr)/./app/components/SavingsBooks.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [savingsBooks, setSavingsBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    // Load data from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const savedTransactions = localStorage.getItem('finance-transactions');\n            const savedSavingsBooks = localStorage.getItem('finance-savings-books');\n            if (savedTransactions) {\n                setTransactions(JSON.parse(savedTransactions));\n            } else {\n                // Default transactions if no saved data\n                const defaultTransactions = [\n                    {\n                        id: '1',\n                        type: 'income',\n                        amount: 5000,\n                        category: 'Lương',\n                        description: 'Lương tháng',\n                        date: '2024-01-15'\n                    },\n                    {\n                        id: '2',\n                        type: 'expense',\n                        amount: 1200,\n                        category: 'Tiền nhà',\n                        description: 'Tiền thuê nhà hàng tháng',\n                        date: '2024-01-01'\n                    },\n                    {\n                        id: '3',\n                        type: 'expense',\n                        amount: 300,\n                        category: 'Thực phẩm',\n                        description: 'Mua sắm hàng tuần',\n                        date: '2024-01-08'\n                    },\n                    {\n                        id: '4',\n                        type: 'investment',\n                        amount: 2000,\n                        category: 'Cổ phiếu',\n                        description: 'Mua cổ phiếu công nghệ',\n                        date: '2024-01-10',\n                        investmentData: {\n                            profitLoss: 150,\n                            period: 'month',\n                            lastUpdated: '2024-01-20T10:00:00Z'\n                        }\n                    },\n                    {\n                        id: '5',\n                        type: 'income',\n                        amount: 500,\n                        category: 'Freelance',\n                        description: 'Hoàn thành dự án',\n                        date: '2024-01-12'\n                    },\n                    {\n                        id: '6',\n                        type: 'investment',\n                        amount: 1500,\n                        category: 'Tiền điện tử',\n                        description: 'Mua Bitcoin',\n                        date: '2024-01-18',\n                        investmentData: {\n                            profitLoss: -200,\n                            period: 'week',\n                            lastUpdated: '2024-01-25T15:30:00Z'\n                        }\n                    }\n                ];\n                setTransactions(defaultTransactions);\n            }\n            if (savedSavingsBooks) {\n                setSavingsBooks(JSON.parse(savedSavingsBooks));\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    // Save data to localStorage whenever transactions or savings books change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            localStorage.setItem('finance-transactions', JSON.stringify(transactions));\n        }\n    }[\"Home.useEffect\"], [\n        transactions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            localStorage.setItem('finance-savings-books', JSON.stringify(savingsBooks));\n        }\n    }[\"Home.useEffect\"], [\n        savingsBooks\n    ]);\n    const addTransaction = (transaction)=>{\n        const newTransaction = {\n            ...transaction,\n            id: Date.now().toString()\n        };\n        setTransactions([\n            newTransaction,\n            ...transactions\n        ]);\n    };\n    const deleteTransaction = (id)=>{\n        setTransactions(transactions.filter((t)=>t.id !== id));\n    };\n    const addSavingsBook = (book)=>{\n        const newBook = {\n            ...book,\n            id: Date.now().toString(),\n            expenses: []\n        };\n        setSavingsBooks([\n            ...savingsBooks,\n            newBook\n        ]);\n    };\n    const updateSavingsBook = (id, updates)=>{\n        setSavingsBooks(savingsBooks.map((book)=>book.id === id ? {\n                ...book,\n                ...updates\n            } : book));\n    };\n    const deleteSavingsBook = (id)=>{\n        setSavingsBooks(savingsBooks.filter((book)=>book.id !== id));\n    };\n    const addExpenseToSavingsBook = (bookId, expense)=>{\n        setSavingsBooks(savingsBooks.map((book)=>{\n            if (book.id === bookId) {\n                const newExpense = {\n                    ...expense,\n                    id: Date.now().toString()\n                };\n                return {\n                    ...book,\n                    expenses: [\n                        ...book.expenses,\n                        newExpense\n                    ]\n                };\n            }\n            return book;\n        }));\n    };\n    const totalIncome = transactions.filter((t)=>t.type === 'income').reduce((sum, t)=>sum + t.amount, 0);\n    const totalExpenses = transactions.filter((t)=>t.type === 'expense').reduce((sum, t)=>sum + t.amount, 0);\n    const totalInvestments = transactions.filter((t)=>t.type === 'investment').reduce((sum, t)=>sum + t.amount, 0);\n    // Calculate total profit/loss from investments\n    const totalInvestmentProfitLoss = transactions.filter((t)=>t.type === 'investment' && t.investmentData).reduce((sum, t)=>sum + (t.investmentData?.profitLoss || 0), 0);\n    // Calculate current balance including investment profit/loss\n    const currentBalance = totalIncome - totalExpenses - totalInvestments + totalInvestmentProfitLoss;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 flex items-center justify-center bg-blue-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-wallet-3-line text-white text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Quản l\\xfd t\\xe0i ch\\xednh c\\xe1 nh\\xe2n\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Số dư hiện tại\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-lg font-bold ${currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                                        children: [\n                                            currentBalance.toLocaleString(),\n                                            \"₫\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('dashboard'),\n                                className: `py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer ${activeTab === 'dashboard' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                                children: \"Tổng quan\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('transactions'),\n                                className: `py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer ${activeTab === 'transactions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                                children: \"Giao dịch\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('savings'),\n                                className: `py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer ${activeTab === 'savings' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                                children: \"Sổ tiết kiệm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    activeTab === 'dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardStats__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                totalIncome: totalIncome,\n                                totalExpenses: totalExpenses,\n                                totalInvestments: totalInvestments,\n                                totalInvestmentProfitLoss: totalInvestmentProfitLoss,\n                                currentBalance: currentBalance\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Th\\xeam giao dịch mới\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                onSubmit: addTransaction\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Sổ tiết kiệm của bạn\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SavingsBooks__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                savingsBooks: savingsBooks.slice(0, 3),\n                                                onAdd: addSavingsBook,\n                                                onUpdate: updateSavingsBook,\n                                                onDelete: deleteSavingsBook,\n                                                onAddExpense: addExpenseToSavingsBook,\n                                                isPreview: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            savingsBooks.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('savings'),\n                                                className: \"mt-4 text-blue-600 hover:text-blue-800 text-sm cursor-pointer\",\n                                                children: \"Xem tất cả sổ tiết kiệm →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Giao dịch gần đ\\xe2y\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            transactions: transactions.slice(0, 5),\n                                            onDelete: deleteTransaction\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'transactions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Th\\xeam giao dịch mới\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onSubmit: addTransaction\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Tất cả giao dịch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TransactionList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            transactions: transactions,\n                                            onDelete: deleteTransaction\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'savings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Quản l\\xfd sổ tiết kiệm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SavingsBooks__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    savingsBooks: savingsBooks,\n                                    onAdd: addSavingsBook,\n                                    onUpdate: updateSavingsBook,\n                                    onDelete: deleteSavingsBook,\n                                    onAddExpense: addExpenseToSavingsBook,\n                                    isPreview: false\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\App quản l\\xfd chi ti\\xeau\\\\app của mẹ\\\\app\\\\page.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBcHAlMjBxdSVFMSVCQSVBM24lMjBsJUMzJUJEJTIwY2hpJTIwdGklQzMlQUF1JTVDJTVDYXBwJTIwYyVFMSVCQiVBN2ElMjBtJUUxJUJBJUI5JTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUF3RiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQXBwIHF14bqjbiBsw70gY2hpIHRpw6p1XFxcXGFwcCBj4bunYSBt4bq5XFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Pacifico%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-pacifico%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22pacifico%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5C%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CApp%20qu%E1%BA%A3n%20l%C3%BD%20chi%20ti%C3%AAu%5Capp%20c%E1%BB%A7a%20m%E1%BA%B9&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();