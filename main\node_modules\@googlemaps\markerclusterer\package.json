{"name": "@googlemaps/markerclusterer", "version": "2.5.3", "description": "Creates and manages per-zoom-level clusters for large amounts of markers.", "keywords": ["cluster", "google", "maps", "marker"], "homepage": "https://github.com/googlemaps/js-markerclusterer", "bugs": {"url": "https://github.com/googlemaps/js-markerclusterer/issues"}, "repository": {"type": "git", "url": "https://github.com/googlemaps/js-markerclusterer.git"}, "license": "Apache-2.0", "author": "<PERSON>", "main": "dist/index.umd.js", "unpkg": "dist/index.min.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist/*"], "scripts": {"docs": "typedoc src/index.ts && cp -r dist docs/dist && npm run examples && cp -r public docs/public", "examples": "rollup -c rollup.config.examples.js", "dev": "rollup -c rollup.config.examples.js --watch", "format": "eslint . --fix", "lint": "eslint .", "prepare": "rm -rf dist && rollup -c", "test": "jest --passWithNoTests src/*", "test:all": "jest"}, "dependencies": {"fast-deep-equal": "^3.1.3", "supercluster": "^8.0.1"}, "devDependencies": {"@babel/preset-env": "^7.11.5", "@babel/runtime-corejs3": "^7.11.2", "@googlemaps/jest-mocks": "^2.19.1", "@googlemaps/js-api-loader": "^1.12.3", "@rollup/plugin-babel": "^6.0.0", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-html": "^1.0.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@types/d3-interpolate": "^3.0.1", "@types/google.maps": "^3.53.1", "@types/jest": "^27.0.1", "@types/supercluster": "^7.1.0", "@typescript-eslint/eslint-plugin": ">=4.1.0", "@typescript-eslint/parser": ">=4.1.0", "core-js": "^3.6.5", "d3-interpolate": "^3.0.1", "eslint": "^8.41.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^26.4.2", "prettier": "^2.1.1", "rollup": "^2.26.11", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-serve": "^2.0.2", "rollup-plugin-terser": "^7.0.2", "selenium-webdriver": "^4.0.0-alpha.7", "ts-jest": "^26.3.0", "typedoc": "^0.25.0", "typescript": "^4.0.2"}, "publishConfig": {"access": "public", "registry": "https://wombat-dressing-room.appspot.com"}}