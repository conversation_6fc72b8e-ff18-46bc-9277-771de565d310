
'use client';

import { useState } from 'react';
import { Transaction } from '../types/finance';

interface TransactionFormProps {
  onSubmit: (transaction: Omit<Transaction, 'id'>) => void;
}

export default function TransactionForm({ onSubmit }: TransactionFormProps) {
  const [type, setType] = useState<'income' | 'expense' | 'investment'>('income');
  const [amount, setAmount] = useState('');
  const [displayAmount, setDisplayAmount] = useState('');
  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  // Investment profit/loss fields
  const [profitLoss, setProfitLoss] = useState('');
  const [displayProfitLoss, setDisplayProfitLoss] = useState('');
  const [period, setPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [showProfitLossSuggestions, setShowProfitLossSuggestions] = useState(false);

  const formatNumber = (num: string) => {
    return num.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  const parseNumber = (str: string) => {
    return str.replace(/\./g, '');
  };

  const generateSuggestions = (input: string) => {
    const baseNum = parseNumber(input);
    if (!baseNum || isNaN(Number(baseNum))) return [];
    
    const suggestions = [];
    const base = Number(baseNum);
    
    if (base < 10) {
      suggestions.push(base * 1000);
      suggestions.push(base * 10000);
      suggestions.push(base * 100000);
      suggestions.push(base * 500000);
      suggestions.push(base * 1000000);
    } else if (base < 100) {
      suggestions.push(base * 100);
      suggestions.push(base * 1000);
      suggestions.push(base * 10000);
      suggestions.push(base * 50000);
    } else if (base < 1000) {
      suggestions.push(base * 10);
      suggestions.push(base * 100);
      suggestions.push(base * 1000);
    } else {
      suggestions.push(base);
      suggestions.push(base * 10);
      suggestions.push(base * 100);
    }
    
    return suggestions.filter(s => s > 0).slice(0, 5);
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = parseNumber(value);
    
    if (numericValue === '' || /^\d+$/.test(numericValue)) {
      setDisplayAmount(numericValue ? formatNumber(numericValue) : '');
      setAmount(numericValue);
      setShowSuggestions(numericValue.length > 0);
    }
  };

  const handleProfitLossChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    let numericValue = parseNumber(value);
    
    // Allow negative numbers for losses
    if (value.startsWith('-')) {
      numericValue = '-' + numericValue;
    }
    
    if (numericValue === '' || /^-?\d+$/.test(numericValue)) {
      setDisplayProfitLoss(numericValue ? (numericValue.startsWith('-') ? '-' + formatNumber(numericValue.slice(1)) : formatNumber(numericValue)) : '');
      setProfitLoss(numericValue);
      setShowProfitLossSuggestions(Math.abs(parseFloat(numericValue || '0')) > 0);
    }
  };

  const selectSuggestion = (suggestion: number) => {
    setAmount(suggestion.toString());
    setDisplayAmount(formatNumber(suggestion.toString()));
    setShowSuggestions(false);
  };

  const selectProfitLossSuggestion = (suggestion: number) => {
    setProfitLoss(suggestion.toString());
    setDisplayProfitLoss(suggestion < 0 ? '-' + formatNumber(Math.abs(suggestion).toString()) : formatNumber(suggestion.toString()));
    setShowProfitLossSuggestions(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!amount || !category || !description) return;

    const transaction: Omit<Transaction, 'id'> = {
      type,
      amount: parseFloat(amount),
      category,
      description,
      date,
    };

    // Add investment data if it's an investment transaction
    if (type === 'investment' && profitLoss) {
      transaction.investmentData = {
        profitLoss: parseFloat(profitLoss),
        period,
        lastUpdated: new Date().toISOString(),
      };
    }

    onSubmit(transaction);

    // Reset form
    setAmount('');
    setDisplayAmount('');
    setCategory('');
    setDescription('');
    setDate(new Date().toISOString().split('T')[0]);
    setProfitLoss('');
    setDisplayProfitLoss('');
    setPeriod('month');
    setShowSuggestions(false);
    setShowProfitLossSuggestions(false);
  };

  const categories = {
    income: ['Lương', 'Freelance', 'Kinh doanh', 'Lợi nhuận đầu tư', 'Khác'],
    expense: ['Tiền nhà', 'Thực phẩm', 'Giao thông', 'Giải trí', 'Y tế', 'Khác'],
    investment: ['Cổ phiếu', 'Trái phiếu', 'Bất động sản', 'Tiền điện tử', 'Quỹ tương hỗ', 'Khác'],
  };

  const typeLabels = {
    income: 'Thu nhập',
    expense: 'Chi tiêu',
    investment: 'Đầu tư'
  };

  const periodLabels = {
    week: 'Tuần',
    month: 'Tháng',
    year: 'Năm'
  };

  const suggestions = generateSuggestions(displayAmount);
  const profitLossSuggestions = generateSuggestions(displayProfitLoss.replace('-', ''));

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Loại</label>
          <div className="flex space-x-2">
            {(['income', 'expense', 'investment'] as const).map((t) => (
              <button
                key={t}
                type="button"
                onClick={() => setType(t)}
                className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap cursor-pointer ${
                  type === t
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {typeLabels[t]}
              </button>
            ))}
          </div>
        </div>

        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền</label>
          <div className="relative">
            <input
              type="text"
              value={displayAmount}
              onChange={handleAmountChange}
              onFocus={() => setShowSuggestions(displayAmount.length > 0)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12"
              placeholder="0"
              required
            />
            <div className="absolute right-3 top-2 text-sm text-gray-500">₫</div>
          </div>
          
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => selectSuggestion(suggestion)}
                  className="w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  {formatNumber(suggestion.toString())}₫
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Investment profit/loss section */}
      {type === 'investment' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">Lãi/Lỗ</label>
            <div className="relative">
              <input
                type="text"
                value={displayProfitLoss}
                onChange={handleProfitLossChange}
                onFocus={() => setShowProfitLossSuggestions(Math.abs(parseFloat(profitLoss || '0')) > 0)}
                onBlur={() => setTimeout(() => setShowProfitLossSuggestions(false), 200)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12"
                placeholder="0 (nhập số âm nếu lỗ)"
              />
              <div className="absolute right-3 top-2 text-sm text-gray-500">₫</div>
            </div>
            
            {showProfitLossSuggestions && profitLossSuggestions.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
                {profitLossSuggestions.map((suggestion, index) => (
                  <div key={index}>
                    <button
                      type="button"
                      onClick={() => selectProfitLossSuggestion(suggestion)}
                      className="w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 text-green-600"
                    >
                      +{formatNumber(suggestion.toString())}₫
                    </button>
                    <button
                      type="button"
                      onClick={() => selectProfitLossSuggestion(-suggestion)}
                      className="w-full px-3 py-2 text-left hover:bg-gray-50 text-sm cursor-pointer border-b border-gray-100 last:border-b-0 text-red-600"
                    >
                      -{formatNumber(suggestion.toString())}₫
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Chu kỳ</label>
            <div className="flex space-x-2">
              {(['week', 'month', 'year'] as const).map((p) => (
                <button
                  key={p}
                  type="button"
                  onClick={() => setPeriod(p)}
                  className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap cursor-pointer ${
                    period === p
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                  }`}
                >
                  {periodLabels[p]}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Danh mục</label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-8"
            required
          >
            <option value="">Chọn danh mục</option>
            {categories[type].map((cat) => (
              <option key={cat} value={cat}>
                {cat}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Ngày</label>
          <input
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
        <input
          type="text"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          placeholder="Nhập mô tả"
          required
        />
      </div>

      <button
        type="submit"
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium whitespace-nowrap cursor-pointer"
      >
        Thêm giao dịch
      </button>
    </form>
  );
}