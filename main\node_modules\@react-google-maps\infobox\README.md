# @react-google-maps/infobox

![logo](https://raw.githubusercontent.com/JustFly1984/react-google-maps-api/master/logo.png)

[![npm package](https://img.shields.io/npm/v/@react-google-maps/infobox)](https://www.npmjs.com/package/@react-google-maps/infobox)
[![npm downloads](https://img.shields.io/npm/dt/@react-google-maps/infobox)](https://www.npmjs.com/package/@react-google-maps/infobox)
[![npm bundle size](https://img.shields.io/bundlephobia/min/@react-google-maps/infobox)](https://www.npmjs.com/package/@react-google-maps/infobox)
[![Join the community on Spectrum](https://withspectrum.github.io/badge/badge.svg)](https://spectrum.chat/react-google-maps)

@react-google-maps/infobox

> This library is dependency of main project @react-google-maps/api
