
'use client';

interface DashboardStatsProps {
  totalIncome: number;
  totalExpenses: number;
  totalInvestments: number;
  totalInvestmentProfitLoss: number;
  currentBalance: number;
}

export default function DashboardStats({ 
  totalIncome, 
  totalExpenses, 
  totalInvestments, 
  totalInvestmentProfitLoss,
  currentBalance 
}: DashboardStatsProps) {
  const stats = [
    {
      title: 'Tổng thu nhập',
      value: totalIncome,
      icon: 'ri-arrow-up-circle-fill',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Tổng chi tiêu',
      value: totalExpenses,
      icon: 'ri-arrow-down-circle-fill',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Tổng đầu tư',
      value: totalInvestments,
      icon: 'ri-line-chart-fill',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Tổng lãi/lỗ đầu tư',
      value: totalInvestmentProfitLoss,
      icon: totalInvestmentProfitLoss >= 0 ? 'ri-trend-up-fill' : 'ri-trend-down-fill',
      color: totalInvestmentProfitLoss >= 0 ? 'text-green-600' : 'text-red-600',
      bgColor: totalInvestmentProfitLoss >= 0 ? 'bg-green-50' : 'bg-red-50',
    },
    {
      title: 'Số dư hiện tại',
      value: currentBalance,
      icon: 'ri-wallet-3-fill',
      color: currentBalance >= 0 ? 'text-green-600' : 'text-red-600',
      bgColor: currentBalance >= 0 ? 'bg-green-50' : 'bg-red-50',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      {stats.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
              <p className={`text-2xl font-bold ${stat.color}`}>
                {stat.value >= 0 && stat.title === 'Tổng lãi/lỗ đầu tư' ? '+' : ''}{stat.value.toLocaleString()}₫
              </p>
            </div>
            <div className={`w-12 h-12 flex items-center justify-center rounded-lg ${stat.bgColor}`}>
              <i className={`${stat.icon} ${stat.color} text-xl`}></i>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
