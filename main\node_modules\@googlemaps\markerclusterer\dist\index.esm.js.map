{"version": 3, "file": "index.esm.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../src/marker-utils.ts", "../src/cluster.ts", "../src/algorithms/utils.ts", "../src/algorithms/core.ts", "../src/algorithms/grid.ts", "../src/algorithms/noop.ts", "../src/algorithms/supercluster.ts", "../src/algorithms/superviewport.ts", "../src/renderer.ts", "../src/overlay-view-safe.ts", "../src/markerclusterer.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Supports markers of either either \"legacy\" or \"advanced\" types.\n */\nexport type Marker =\n  | google.maps.Marker\n  | google.maps.marker.AdvancedMarkerElement;\n\n/**\n * util class that creates a common set of convenience functions to wrap\n * shared behavior of Advanced Markers and Markers.\n */\nexport class MarkerUtils {\n  public static isAdvancedMarkerAvailable(map: google.maps.Map): boolean {\n    return (\n      google.maps.marker &&\n      map.getMapCapabilities().isAdvancedMarkersAvailable === true\n    );\n  }\n\n  public static isAdvancedMarker(\n    marker: Marker\n  ): marker is google.maps.marker.AdvancedMarkerElement {\n    return (\n      google.maps.marker &&\n      marker instanceof google.maps.marker.AdvancedMarkerElement\n    );\n  }\n\n  public static setMap(marker: Marker, map: google.maps.Map | null) {\n    if (this.isAdvancedMarker(marker)) {\n      marker.map = map;\n    } else {\n      marker.setMap(map);\n    }\n  }\n\n  public static getPosition(marker: Marker): google.maps.LatLng {\n    // SuperClusterAlgorithm.calculate expects a LatLng instance so we fake it for Adv Markers\n    if (this.isAdvancedMarker(marker)) {\n      if (marker.position) {\n        if (marker.position instanceof google.maps.LatLng) {\n          return marker.position;\n        }\n        // since we can't cast to LatLngLiteral for reasons =(\n        if (marker.position.lat && marker.position.lng) {\n          return new google.maps.LatLng(\n            marker.position.lat,\n            marker.position.lng\n          );\n        }\n      }\n      return new google.maps.LatLng(null);\n    }\n    return marker.getPosition();\n  }\n\n  public static getVisible(marker: Marker) {\n    if (this.isAdvancedMarker(marker)) {\n      /**\n       * Always return true for Advanced Markers because the clusterer\n       * uses getVisible as a way to count legacy markers not as an actual\n       * indicator of visibility for some reason. Even when markers are hidden\n       * Marker.getVisible returns `true` and this is used to set the marker count\n       * on the cluster. See the behavior of Cluster.count\n       */\n      return true;\n    }\n    return marker.getVisible();\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MarkerUtils, Marker } from \"./marker-utils\";\n\nexport interface ClusterOptions {\n  position?: google.maps.LatLng | google.maps.LatLngLiteral;\n  markers?: Marker[];\n}\n\nexport class Cluster {\n  public marker?: Marker;\n  public readonly markers?: Marker[];\n  protected _position: google.maps.LatLng;\n\n  constructor({ markers, position }: ClusterOptions) {\n    this.markers = markers;\n\n    if (position) {\n      if (position instanceof google.maps.LatLng) {\n        this._position = position;\n      } else {\n        this._position = new google.maps.LatLng(position);\n      }\n    }\n  }\n\n  public get bounds(): google.maps.LatLngBounds | undefined {\n    if (this.markers.length === 0 && !this._position) {\n      return;\n    }\n\n    const bounds = new google.maps.LatLngBounds(this._position, this._position);\n    for (const marker of this.markers) {\n      bounds.extend(MarkerUtils.getPosition(marker));\n    }\n    return bounds;\n  }\n\n  public get position(): google.maps.LatLng {\n    return this._position || this.bounds.getCenter();\n  }\n\n  /**\n   * Get the count of **visible** markers.\n   */\n  public get count(): number {\n    return this.markers.filter((m: Marker) => MarkerUtils.getVisible(m)).length;\n  }\n\n  /**\n   * Add a marker to the cluster.\n   */\n  public push(marker: Marker): void {\n    this.markers.push(marker);\n  }\n\n  /**\n   * Cleanup references and remove marker from map.\n   */\n  public delete(): void {\n    if (this.marker) {\n      MarkerUtils.setMap(this.marker, null);\n      this.marker = undefined;\n    }\n    this.markers.length = 0;\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\n/**\n * Returns the markers visible in a padded map viewport\n *\n * @param map\n * @param mapCanvasProjection\n * @param markers The list of marker to filter\n * @param viewportPaddingPixels The padding in pixel\n * @returns The list of markers in the padded viewport\n */\nexport const filterMarkersToPaddedViewport = (\n  map: google.maps.Map,\n  mapCanvasProjection: google.maps.MapCanvasProjection,\n  markers: Marker[],\n  viewportPaddingPixels: number\n): Marker[] => {\n  const extendedMapBounds = extendBoundsToPaddedViewport(\n    map.getBounds(),\n    mapCanvasProjection,\n    viewportPaddingPixels\n  );\n  return markers.filter((marker) =>\n    extendedMapBounds.contains(MarkerUtils.getPosition(marker))\n  );\n};\n\n/**\n * Extends a bounds by a number of pixels in each direction\n */\nexport const extendBoundsToPaddedViewport = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection,\n  numPixels: number\n): google.maps.LatLngBounds => {\n  const { northEast, southWest } = latLngBoundsToPixelBounds(\n    bounds,\n    projection\n  );\n  const extendedPixelBounds = extendPixelBounds(\n    { northEast, southWest },\n    numPixels\n  );\n  return pixelBoundsToLatLngBounds(extendedPixelBounds, projection);\n};\n\n/**\n * Gets the extended bounds as a bbox [westLng, southLat, eastLng, northLat]\n */\nexport const getPaddedViewport = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection,\n  pixels: number\n): [number, number, number, number] => {\n  const extended = extendBoundsToPaddedViewport(bounds, projection, pixels);\n  const ne = extended.getNorthEast();\n  const sw = extended.getSouthWest();\n\n  return [sw.lng(), sw.lat(), ne.lng(), ne.lat()];\n};\n\n/**\n * Returns the distance between 2 positions.\n *\n * @hidden\n */\nexport const distanceBetweenPoints = (\n  p1: google.maps.LatLngLiteral,\n  p2: google.maps.LatLngLiteral\n): number => {\n  const R = 6371; // Radius of the Earth in km\n  const dLat = ((p2.lat - p1.lat) * Math.PI) / 180;\n  const dLon = ((p2.lng - p1.lng) * Math.PI) / 180;\n  const sinDLat = Math.sin(dLat / 2);\n  const sinDLon = Math.sin(dLon / 2);\n  const a =\n    sinDLat * sinDLat +\n    Math.cos((p1.lat * Math.PI) / 180) *\n      Math.cos((p2.lat * Math.PI) / 180) *\n      sinDLon *\n      sinDLon;\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  return R * c;\n};\n\ntype PixelBounds = {\n  northEast: google.maps.Point;\n  southWest: google.maps.Point;\n};\n\n/**\n * Converts a LatLng bound to pixels.\n *\n * @hidden\n */\nconst latLngBoundsToPixelBounds = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection\n): PixelBounds => {\n  return {\n    northEast: projection.fromLatLngToDivPixel(bounds.getNorthEast()),\n    southWest: projection.fromLatLngToDivPixel(bounds.getSouthWest()),\n  };\n};\n\n/**\n * Extends a pixel bounds by numPixels in all directions.\n *\n * @hidden\n */\nexport const extendPixelBounds = (\n  { northEast, southWest }: PixelBounds,\n  numPixels: number\n): PixelBounds => {\n  northEast.x += numPixels;\n  northEast.y -= numPixels;\n\n  southWest.x -= numPixels;\n  southWest.y += numPixels;\n\n  return { northEast, southWest };\n};\n\n/**\n * @hidden\n */\nexport const pixelBoundsToLatLngBounds = (\n  { northEast, southWest }: PixelBounds,\n  projection: google.maps.MapCanvasProjection\n): google.maps.LatLngBounds => {\n  const sw = projection.fromDivPixelToLatLng(southWest);\n  const ne = projection.fromDivPixelToLatLng(northEast);\n  return new google.maps.LatLngBounds(sw, ne);\n};\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Cluster } from \"../cluster\";\nimport { filterMarkersToPaddedViewport } from \"./utils\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\nexport interface AlgorithmInput {\n  /**\n   * The map containing the markers and clusters.\n   */\n  map: google.maps.Map;\n  /**\n   * An array of markers to be clustered.\n   *\n   * There are some specific edge cases to be aware of including the following:\n   * * Markers that are not visible.\n   */\n  markers: Marker[];\n  /**\n   * The `mapCanvasProjection` enables easy conversion from lat/lng to pixel.\n   *\n   * @see [MapCanvasProjection](https://developers.google.com/maps/documentation/javascript/reference/overlay-view#MapCanvasProjection)\n   */\n  mapCanvasProjection: google.maps.MapCanvasProjection;\n}\n\nexport interface AlgorithmOutput {\n  /**\n   * The clusters returned based upon the {@link AlgorithmInput}.\n   */\n  clusters: Cluster[];\n  /**\n   * A boolean flag indicating that the clusters have not changed.\n   */\n  changed?: boolean;\n}\n\nexport interface Algorithm {\n  /**\n   * Calculates an array of {@link Cluster}.\n   */\n  calculate: ({ markers, map }: AlgorithmInput) => AlgorithmOutput;\n}\n\nexport interface AlgorithmOptions {\n  // Markers are not clustered at maxZoom and above.\n  maxZoom?: number;\n}\n\n/**\n * @hidden\n */\nexport abstract class AbstractAlgorithm implements Algorithm {\n  protected maxZoom: number;\n\n  constructor({ maxZoom = 16 }: AlgorithmOptions) {\n    this.maxZoom = maxZoom;\n  }\n  /**\n   * Helper function to bypass clustering based upon some map state such as\n   * zoom, number of markers, etc.\n   *\n   * ```typescript\n   *  cluster({markers, map}: AlgorithmInput): Cluster[] {\n   *    if (shouldBypassClustering(map)) {\n   *      return this.noop({markers})\n   *    }\n   * }\n   * ```\n   */\n  protected noop<T extends Pick<AlgorithmInput, \"markers\">>({\n    markers,\n  }: T): Cluster[] {\n    return noop(markers);\n  }\n  /**\n   * Calculates an array of {@link Cluster}. Calculate is separate from\n   * {@link cluster} as it does preprocessing on the markers such as filtering\n   * based upon the viewport as in {@link AbstractViewportAlgorithm}. Caching\n   * and other optimizations can also be done here.\n   */\n  public abstract calculate({ markers, map }: AlgorithmInput): AlgorithmOutput;\n\n  /**\n   * Clusters the markers and called from {@link calculate}.\n   */\n  protected abstract cluster({ markers, map }: AlgorithmInput): Cluster[];\n}\n\n/**\n * @hidden\n */\nexport interface ViewportAlgorithmOptions extends AlgorithmOptions {\n  /**\n   * The number of pixels to extend beyond the viewport bounds when filtering\n   * markers prior to clustering.\n   */\n  viewportPadding?: number;\n}\n\n/**\n * Abstract viewport algorithm proves a class to filter markers by a padded\n * viewport. This is a common optimization.\n *\n * @hidden\n */\nexport abstract class AbstractViewportAlgorithm extends AbstractAlgorithm {\n  protected viewportPadding = 60;\n\n  constructor({ viewportPadding = 60, ...options }: ViewportAlgorithmOptions) {\n    super(options);\n    this.viewportPadding = viewportPadding;\n  }\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers,\n        }),\n        changed: false,\n      };\n    }\n\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(\n          map,\n          mapCanvasProjection,\n          markers,\n          this.viewportPadding\n        ),\n        map,\n        mapCanvasProjection,\n      }),\n    };\n  }\n  protected abstract cluster({ markers, map }: AlgorithmInput): Cluster[];\n}\n\n/**\n * @hidden\n */\nexport const noop = (markers: Marker[]): Cluster[] => {\n  const clusters = markers.map(\n    (marker) =>\n      new Cluster({\n        position: MarkerUtils.getPosition(marker),\n        markers: [marker],\n      })\n  );\n  return clusters;\n};\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractViewportAlgorithm,\n  AlgorithmInput,\n  AlgorithmOutput,\n  ViewportAlgorithmOptions,\n} from \"./core\";\nimport {\n  distanceBetweenPoints,\n  extendBoundsToPaddedViewport,\n  filterMarkersToPaddedViewport,\n} from \"./utils\";\n\nimport { Cluster } from \"../cluster\";\nimport equal from \"fast-deep-equal\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\nexport interface GridOptions extends ViewportAlgorithmOptions {\n  gridSize?: number;\n  /**\n   * Max distance between cluster center and point in meters.\n   * @default 10000\n   */\n  maxDistance?: number;\n}\n\n/**\n * The default Grid algorithm historically used in Google Maps marker\n * clustering.\n *\n * The Grid algorithm does not implement caching and markers may flash as the\n * viewport changes. Instead use {@link SuperClusterAlgorithm}.\n */\nexport class GridAlgorithm extends AbstractViewportAlgorithm {\n  protected gridSize: number;\n  protected maxDistance: number;\n  protected clusters: Cluster[] = [];\n  protected state = { zoom: -1 };\n\n  constructor({ maxDistance = 40000, gridSize = 40, ...options }: GridOptions) {\n    super(options);\n\n    this.maxDistance = maxDistance;\n    this.gridSize = gridSize;\n  }\n\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    const state = { zoom: map.getZoom() };\n    let changed = false;\n    if (this.state.zoom >= this.maxZoom && state.zoom >= this.maxZoom) {\n      // still at or beyond maxZoom, no change\n    } else {\n      changed = !equal(this.state, state);\n    }\n    this.state = state;\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers,\n        }),\n        changed,\n      };\n    }\n\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(\n          map,\n          mapCanvasProjection,\n          markers,\n          this.viewportPadding\n        ),\n        map,\n        mapCanvasProjection,\n      }),\n    };\n  }\n\n  protected cluster({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): Cluster[] {\n    this.clusters = [];\n    markers.forEach((marker) => {\n      this.addToClosestCluster(marker, map, mapCanvasProjection);\n    });\n\n    return this.clusters;\n  }\n\n  protected addToClosestCluster(\n    marker: Marker,\n    map: google.maps.Map,\n    projection: google.maps.MapCanvasProjection\n  ): void {\n    let maxDistance = this.maxDistance; // Some large number\n    let cluster: Cluster = null;\n\n    for (let i = 0; i < this.clusters.length; i++) {\n      const candidate = this.clusters[i];\n      const distance = distanceBetweenPoints(\n        candidate.bounds.getCenter().toJSON(),\n        MarkerUtils.getPosition(marker).toJSON()\n      );\n\n      if (distance < maxDistance) {\n        maxDistance = distance;\n        cluster = candidate;\n      }\n    }\n\n    if (\n      cluster &&\n      extendBoundsToPaddedViewport(\n        cluster.bounds,\n        projection,\n        this.gridSize\n      ).contains(MarkerUtils.getPosition(marker))\n    ) {\n      cluster.push(marker);\n    } else {\n      const cluster = new Cluster({ markers: [marker] });\n      this.clusters.push(cluster);\n    }\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractAlgorithm,\n  AlgorithmInput,\n  AlgorithmOptions,\n  AlgorithmOutput,\n} from \"./core\";\n\nimport { Cluster } from \"../cluster\";\n\n/**\n * Noop algorithm does not generate any clusters or filter markers by the an extended viewport.\n */\nexport class NoopAlgorithm extends AbstractAlgorithm {\n  constructor({ ...options }: AlgorithmOptions) {\n    super(options);\n  }\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    return {\n      clusters: this.cluster({ markers, map, mapCanvasProjection }),\n      changed: false,\n    };\n  }\n\n  protected cluster(input: AlgorithmInput): Cluster[] {\n    return this.noop(input);\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AbstractAlgorithm, AlgorithmInput, AlgorithmOutput } from \"./core\";\nimport SuperCluster, { ClusterFeature } from \"supercluster\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\nimport { Cluster } from \"../cluster\";\nimport equal from \"fast-deep-equal\";\n\nexport type SuperClusterOptions = SuperCluster.Options<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  { [name: string]: any },\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  { [name: string]: any }\n>;\n\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nexport class SuperClusterAlgorithm extends AbstractAlgorithm {\n  protected superCluster: SuperCluster;\n  protected markers: Marker[];\n  protected clusters: Cluster[];\n  protected state = { zoom: -1 };\n\n  constructor({ maxZoom, radius = 60, ...options }: SuperClusterOptions) {\n    super({ maxZoom });\n\n    this.superCluster = new SuperCluster({\n      maxZoom: this.maxZoom,\n      radius,\n      ...options,\n    });\n  }\n\n  public calculate(input: AlgorithmInput): AlgorithmOutput {\n    let changed = false;\n    const state = { zoom: input.map.getZoom() };\n\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n\n      const points = this.markers.map((marker) => {\n        const position = MarkerUtils.getPosition(marker);\n        const coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\" as const,\n          geometry: {\n            type: \"Point\" as const,\n            coordinates,\n          },\n          properties: { marker },\n        };\n      });\n      this.superCluster.load(points);\n    }\n\n    if (!changed) {\n      if (this.state.zoom <= this.maxZoom || state.zoom <= this.maxZoom) {\n        changed = !equal(this.state, state);\n      }\n    }\n\n    this.state = state;\n\n    if (changed) {\n      this.clusters = this.cluster(input);\n    }\n\n    return { clusters: this.clusters, changed };\n  }\n\n  public cluster({ map }: AlgorithmInput): Cluster[] {\n    return this.superCluster\n      .getClusters([-180, -90, 180, 90], Math.round(map.getZoom()))\n      .map((feature: ClusterFeature<{ marker: Marker }>) =>\n        this.transformCluster(feature)\n      );\n  }\n\n  protected transformCluster({\n    geometry: {\n      coordinates: [lng, lat],\n    },\n    properties,\n  }: ClusterFeature<{ marker: Marker }>): Cluster {\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster\n          .getLeaves(properties.cluster_id, Infinity)\n          .map((leaf) => leaf.properties.marker),\n        position: { lat, lng },\n      });\n    }\n\n    const marker = properties.marker;\n\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker),\n    });\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractViewportAlgorithm,\n  AlgorithmInput,\n  AlgorithmOutput,\n  ViewportAlgorithmOptions,\n} from \"./core\";\nimport { SuperClusterOptions } from \"./supercluster\";\nimport SuperCluster, { ClusterFeature } from \"supercluster\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\nimport { Cluster } from \"../cluster\";\nimport { getPaddedViewport } from \"./utils\";\nimport equal from \"fast-deep-equal\";\n\nexport interface SuperClusterViewportOptions\n  extends SuperClusterOptions,\n    ViewportAlgorithmOptions {}\n\nexport interface SuperClusterViewportState {\n  /* The current zoom level */\n  zoom: number;\n\n  /* The current viewport as a bbox [westLng, southLat, eastLng, northLat] */\n  view: [number, number, number, number];\n}\n\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nexport class SuperClusterViewportAlgorithm extends AbstractViewportAlgorithm {\n  protected superCluster: SuperCluster;\n  protected markers: Marker[];\n  protected clusters: Cluster[];\n  protected state: SuperClusterViewportState;\n\n  constructor({\n    maxZoom,\n    radius = 60,\n    viewportPadding = 60,\n    ...options\n  }: SuperClusterViewportOptions) {\n    super({ maxZoom, viewportPadding });\n\n    this.superCluster = new SuperCluster({\n      maxZoom: this.maxZoom,\n      radius,\n      ...options,\n    });\n\n    this.state = { zoom: -1, view: [0, 0, 0, 0] };\n  }\n\n  public calculate(input: AlgorithmInput): AlgorithmOutput {\n    const state: SuperClusterViewportState = {\n      zoom: Math.round(input.map.getZoom()),\n      view: getPaddedViewport(\n        input.map.getBounds(),\n        input.mapCanvasProjection,\n        this.viewportPadding\n      ),\n    };\n\n    let changed = !equal(this.state, state);\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n\n      const points = this.markers.map((marker) => {\n        const position = MarkerUtils.getPosition(marker);\n        const coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\" as const,\n          geometry: {\n            type: \"Point\" as const,\n            coordinates,\n          },\n          properties: { marker },\n        };\n      });\n      this.superCluster.load(points);\n    }\n\n    if (changed) {\n      this.clusters = this.cluster(input);\n      this.state = state;\n    }\n\n    return { clusters: this.clusters, changed };\n  }\n\n  public cluster({ map, mapCanvasProjection }: AlgorithmInput): Cluster[] {\n    /* recalculate new state because we can't use the cached version. */\n    const state: SuperClusterViewportState = {\n      zoom: Math.round(map.getZoom()),\n      view: getPaddedViewport(\n        map.getBounds(),\n        mapCanvasProjection,\n        this.viewportPadding\n      ),\n    };\n\n    return this.superCluster\n      .getClusters(state.view, state.zoom)\n      .map((feature: ClusterFeature<{ marker: Marker }>) =>\n        this.transformCluster(feature)\n      );\n  }\n\n  protected transformCluster({\n    geometry: {\n      coordinates: [lng, lat],\n    },\n    properties,\n  }: ClusterFeature<{ marker: Marker }>): Cluster {\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster\n          .getLeaves(properties.cluster_id, Infinity)\n          .map((leaf) => leaf.properties.marker),\n        position: { lat, lng },\n      });\n    }\n\n    const marker = properties.marker;\n\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker),\n    });\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Cluster } from \"./cluster\";\nimport { Marker, MarkerUtils } from \"./marker-utils\";\n\n/**\n * Provides statistics on all clusters in the current render cycle for use in {@link Renderer.render}.\n */\nexport class ClusterStats {\n  public readonly markers: { sum: number };\n  public readonly clusters: {\n    count: number;\n    markers: {\n      mean: number;\n      sum: number;\n      min: number;\n      max: number;\n    };\n  };\n\n  constructor(markers: Marker[], clusters: Cluster[]) {\n    this.markers = { sum: markers.length };\n    const clusterMarkerCounts = clusters.map((a) => a.count);\n    const clusterMarkerSum = clusterMarkerCounts.reduce((a, b) => a + b, 0);\n\n    this.clusters = {\n      count: clusters.length,\n      markers: {\n        mean: clusterMarkerSum / clusters.length,\n        sum: clusterMarkerSum,\n        min: Math.min(...clusterMarkerCounts),\n        max: Math.max(...clusterMarkerCounts),\n      },\n    };\n  }\n}\n\nexport interface Renderer {\n  /**\n   * Turn a {@link Cluster} into a `Marker`.\n   *\n   * Below is a simple example to create a marker with the number of markers in the cluster as a label.\n   *\n   * ```typescript\n   * return new google.maps.Marker({\n   *   position,\n   *   label: String(markers.length),\n   * });\n   * ```\n   */\n  render(cluster: Cluster, stats: ClusterStats, map: google.maps.Map): Marker;\n}\n\nexport class DefaultRenderer implements Renderer {\n  /**\n   * The default render function for the library used by {@link MarkerClusterer}.\n   *\n   * Currently set to use the following:\n   *\n   * ```typescript\n   * // change color if this cluster has more markers than the mean cluster\n   * const color =\n   *   count > Math.max(10, stats.clusters.markers.mean)\n   *     ? \"#ff0000\"\n   *     : \"#0000ff\";\n   *\n   * // create svg url with fill color\n   * const svg = window.btoa(`\n   * <svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\">\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".1\" r=\"130\" />\n   * </svg>`);\n   *\n   * // create marker using svg icon\n   * return new google.maps.Marker({\n   *   position,\n   *   icon: {\n   *     url: `data:image/svg+xml;base64,${svg}`,\n   *     scaledSize: new google.maps.Size(45, 45),\n   *   },\n   *   label: {\n   *     text: String(count),\n   *     color: \"rgba(255,255,255,0.9)\",\n   *     fontSize: \"12px\",\n   *   },\n   *   // adjust zIndex to be above other markers\n   *   zIndex: 1000 + count,\n   * });\n   * ```\n   */\n  public render(\n    { count, position }: Cluster,\n    stats: ClusterStats,\n    map: google.maps.Map\n  ): Marker {\n    // change color if this cluster has more markers than the mean cluster\n    const color =\n      count > Math.max(10, stats.clusters.markers.mean) ? \"#ff0000\" : \"#0000ff\";\n\n    // create svg literal with fill color\n    const svg = `<svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\" width=\"50\" height=\"50\">\n<circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n<text x=\"50%\" y=\"50%\" style=\"fill:#fff\" text-anchor=\"middle\" font-size=\"50\" dominant-baseline=\"middle\" font-family=\"roboto,arial,sans-serif\">${count}</text>\n</svg>`;\n\n    const title = `Cluster of ${count} markers`,\n      // adjust zIndex to be above other markers\n      zIndex: number = Number(google.maps.Marker.MAX_ZINDEX) + count;\n\n    if (MarkerUtils.isAdvancedMarkerAvailable(map)) {\n      // create cluster SVG element\n      const parser = new DOMParser();\n      const svgEl = parser.parseFromString(\n        svg,\n        \"image/svg+xml\"\n      ).documentElement;\n      svgEl.setAttribute(\"transform\", \"translate(0 25)\");\n\n      const clusterOptions: google.maps.marker.AdvancedMarkerElementOptions = {\n        map,\n        position,\n        zIndex,\n        title,\n        content: svgEl,\n      };\n      return new google.maps.marker.AdvancedMarkerElement(clusterOptions);\n    }\n\n    const clusterOptions: google.maps.MarkerOptions = {\n      position,\n      zIndex,\n      title,\n      icon: {\n        url: `data:image/svg+xml;base64,${btoa(svg)}`,\n        anchor: new google.maps.Point(25, 25),\n      },\n    };\n    return new google.maps.Marker(clusterOptions);\n  }\n}\n", "/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface OverlayViewSafe extends google.maps.OverlayView {}\n\n/**\n * Extends an object's prototype by another's.\n *\n * @param type1 The Type to be extended.\n * @param type2 The Type to extend with.\n * @ignore\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction extend(type1: any, type2: any): void {\n  /* istanbul ignore next */\n  // eslint-disable-next-line prefer-const\n  for (let property in type2.prototype) {\n    type1.prototype[property] = type2.prototype[property];\n  }\n}\n\n/**\n * @ignore\n */\nexport class OverlayViewSafe {\n  constructor() {\n    // MarkerClusterer implements google.maps.OverlayView interface. We use the\n    // extend function to extend MarkerClusterer with google.maps.OverlayView\n    // because it might not always be available when the code is defined so we\n    // look for it at the last possible moment. If it doesn't exist now then\n    // there is no point going ahead :)\n    extend(OverlayViewSafe, google.maps.OverlayView);\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Algorithm,\n  AlgorithmOptions,\n  SuperClusterAlgorithm,\n} from \"./algorithms\";\nimport { ClusterStats, DefaultRenderer, Renderer } from \"./renderer\";\nimport { Cluster } from \"./cluster\";\nimport { OverlayViewSafe } from \"./overlay-view-safe\";\nimport { MarkerUtils, Marker } from \"./marker-utils\";\n\nexport type onClusterClickHandler = (\n  event: google.maps.MapMouseEvent,\n  cluster: Cluster,\n  map: google.maps.Map\n) => void;\nexport interface MarkerClustererOptions {\n  markers?: Marker[];\n  /**\n   * An algorithm to cluster markers. Default is {@link SuperClusterAlgorithm}. Must\n   * provide a `calculate` method accepting {@link AlgorithmInput} and returning\n   * an array of {@link Cluster}.\n   */\n  algorithm?: Algorithm;\n  algorithmOptions?: AlgorithmOptions;\n  map?: google.maps.Map | null;\n  /**\n   * An object that converts a {@link Cluster} into a `google.maps.Marker`.\n   * Default is {@link DefaultRenderer}.\n   */\n  renderer?: Renderer;\n  onClusterClick?: onClusterClickHandler;\n}\n\nexport enum MarkerClustererEvents {\n  CLUSTERING_BEGIN = \"clusteringbegin\",\n  CLUSTERING_END = \"clusteringend\",\n  CLUSTER_CLICK = \"click\",\n}\n\nexport const defaultOnClusterClickHandler: onClusterClickHandler = (\n  _: google.maps.MapMouseEvent,\n  cluster: Cluster,\n  map: google.maps.Map\n): void => {\n  map.fitBounds(cluster.bounds);\n};\n/**\n * MarkerClusterer creates and manages per-zoom-level clusters for large amounts\n * of markers. See {@link MarkerClustererOptions} for more details.\n *\n */\nexport class MarkerClusterer extends OverlayViewSafe {\n  /** @see {@link MarkerClustererOptions.onClusterClick} */\n  public onClusterClick: onClusterClickHandler;\n  /** @see {@link MarkerClustererOptions.algorithm} */\n  protected algorithm: Algorithm;\n  protected clusters: Cluster[];\n  protected markers: Marker[];\n  /** @see {@link MarkerClustererOptions.renderer} */\n  protected renderer: Renderer;\n  /** @see {@link MarkerClustererOptions.map} */\n  protected map: google.maps.Map | null;\n  protected idleListener: google.maps.MapsEventListener;\n\n  constructor({\n    map,\n    markers = [],\n    algorithmOptions = {},\n    algorithm = new SuperClusterAlgorithm(algorithmOptions),\n    renderer = new DefaultRenderer(),\n    onClusterClick = defaultOnClusterClickHandler,\n  }: MarkerClustererOptions) {\n    super();\n    this.markers = [...markers];\n    this.clusters = [];\n\n    this.algorithm = algorithm;\n    this.renderer = renderer;\n\n    this.onClusterClick = onClusterClick;\n\n    if (map) {\n      this.setMap(map);\n    }\n  }\n\n  public addMarker(marker: Marker, noDraw?: boolean): void {\n    if (this.markers.includes(marker)) {\n      return;\n    }\n\n    this.markers.push(marker);\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  public addMarkers(markers: Marker[], noDraw?: boolean): void {\n    markers.forEach((marker) => {\n      this.addMarker(marker, true);\n    });\n\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  public removeMarker(marker: Marker, noDraw?: boolean): boolean {\n    const index = this.markers.indexOf(marker);\n\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false;\n    }\n\n    MarkerUtils.setMap(marker, null);\n    this.markers.splice(index, 1); // Remove the marker from the list of managed markers\n\n    if (!noDraw) {\n      this.render();\n    }\n\n    return true;\n  }\n\n  public removeMarkers(markers: Marker[], noDraw?: boolean): boolean {\n    let removed = false;\n\n    markers.forEach((marker) => {\n      removed = this.removeMarker(marker, true) || removed;\n    });\n\n    if (removed && !noDraw) {\n      this.render();\n    }\n\n    return removed;\n  }\n\n  public clearMarkers(noDraw?: boolean): void {\n    this.markers.length = 0;\n\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  /**\n   * Recalculates and draws all the marker clusters.\n   */\n  public render(): void {\n    const map = this.getMap();\n    if (map instanceof google.maps.Map && map.getProjection()) {\n      google.maps.event.trigger(\n        this,\n        MarkerClustererEvents.CLUSTERING_BEGIN,\n        this\n      );\n      const { clusters, changed } = this.algorithm.calculate({\n        markers: this.markers,\n        map,\n        mapCanvasProjection: this.getProjection(),\n      });\n\n      // Allow algorithms to return flag on whether the clusters/markers have changed.\n      if (changed || changed == undefined) {\n        // Accumulate the markers of the clusters composed of a single marker.\n        // Those clusters directly use the marker.\n        // Clusters with more than one markers use a group marker generated by a renderer.\n        const singleMarker = new Set<Marker>();\n        for (const cluster of clusters) {\n          if (cluster.markers.length == 1) {\n            singleMarker.add(cluster.markers[0]);\n          }\n        }\n\n        const groupMarkers: Marker[] = [];\n        // Iterate the clusters that are currently rendered.\n        for (const cluster of this.clusters) {\n          if (cluster.marker == null) {\n            continue;\n          }\n          if (cluster.markers.length == 1) {\n            if (!singleMarker.has(cluster.marker)) {\n              // The marker:\n              // - was previously rendered because it is from a cluster with 1 marker,\n              // - should no more be rendered as it is not in singleMarker.\n              MarkerUtils.setMap(cluster.marker, null);\n            }\n          } else {\n            // Delay the removal of old group markers to avoid flickering.\n            groupMarkers.push(cluster.marker);\n          }\n        }\n\n        this.clusters = clusters;\n        this.renderClusters();\n\n        // Delayed removal of the markers of the former groups.\n        requestAnimationFrame(() =>\n          groupMarkers.forEach((marker) => MarkerUtils.setMap(marker, null))\n        );\n      }\n      google.maps.event.trigger(\n        this,\n        MarkerClustererEvents.CLUSTERING_END,\n        this\n      );\n    }\n  }\n\n  public onAdd(): void {\n    this.idleListener = this.getMap().addListener(\n      \"idle\",\n      this.render.bind(this)\n    );\n    this.render();\n  }\n\n  public onRemove(): void {\n    google.maps.event.removeListener(this.idleListener);\n    this.reset();\n  }\n\n  protected reset(): void {\n    this.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n    this.clusters.forEach((cluster) => cluster.delete());\n    this.clusters = [];\n  }\n\n  protected renderClusters(): void {\n    // Generate stats to pass to renderers.\n    const stats = new ClusterStats(this.markers, this.clusters);\n    const map = this.getMap() as google.maps.Map;\n\n    this.clusters.forEach((cluster) => {\n      if (cluster.markers.length === 1) {\n        cluster.marker = cluster.markers[0];\n      } else {\n        // Generate the marker to represent the group.\n        cluster.marker = this.renderer.render(cluster, stats, map);\n        // Make sure all individual markers are removed from the map.\n        cluster.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n        if (this.onClusterClick) {\n          cluster.marker.addListener(\n            \"click\",\n            /* istanbul ignore next */\n            (event: google.maps.MapMouseEvent) => {\n              google.maps.event.trigger(\n                this,\n                MarkerClustererEvents.CLUSTER_CLICK,\n                cluster\n              );\n              this.onClusterClick(event, cluster, map);\n            }\n          );\n        }\n      }\n      MarkerUtils.setMap(cluster.marker, map);\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA0BA;AACO,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;AAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;AACf,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;AACvF,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,UAAU;AACvE,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChF,YAAY,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1F,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,SAAS;AACT,IAAI,OAAO,CAAC,CAAC;AACb;;AClDA;;;;;;;;;;;;;;AAcG;AASH;;;AAGG;MACU,WAAW,CAAA;IACf,OAAO,yBAAyB,CAAC,GAAoB,EAAA;AAC1D,QAAA,QACE,MAAM,CAAC,IAAI,CAAC,MAAM;YAClB,GAAG,CAAC,kBAAkB,EAAE,CAAC,0BAA0B,KAAK,IAAI,EAC5D;KACH;IAEM,OAAO,gBAAgB,CAC5B,MAAc,EAAA;AAEd,QAAA,QACE,MAAM,CAAC,IAAI,CAAC,MAAM;YAClB,MAAM,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAC1D;KACH;AAEM,IAAA,OAAO,MAAM,CAAC,MAAc,EAAE,GAA2B,EAAA;AAC9D,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;AACjC,YAAA,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AAClB,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACpB,SAAA;KACF;IAEM,OAAO,WAAW,CAAC,MAAc,EAAA;;AAEtC,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;YACjC,IAAI,MAAM,CAAC,QAAQ,EAAE;gBACnB,IAAI,MAAM,CAAC,QAAQ,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;oBACjD,OAAO,MAAM,CAAC,QAAQ,CAAC;AACxB,iBAAA;;gBAED,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE;AAC9C,oBAAA,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAC3B,MAAM,CAAC,QAAQ,CAAC,GAAG,EACnB,MAAM,CAAC,QAAQ,CAAC,GAAG,CACpB,CAAC;AACH,iBAAA;AACF,aAAA;YACD,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrC,SAAA;AACD,QAAA,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC;KAC7B;IAEM,OAAO,UAAU,CAAC,MAAc,EAAA;AACrC,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;AACjC;;;;;;AAMG;AACH,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACD,QAAA,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC;KAC5B;AACF;;ACrFD;;;;;;;;;;;;;;AAcG;MASU,OAAO,CAAA;AAKlB,IAAA,WAAA,CAAY,EAAE,OAAO,EAAE,QAAQ,EAAkB,EAAA;AAC/C,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAEvB,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;AAC1C,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC3B,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACnD,aAAA;AACF,SAAA;KACF;AAED,IAAA,IAAW,MAAM,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAChD,OAAO;AACR,SAAA;AAED,QAAA,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5E,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YACjC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AAChD,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,IAAW,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;KAClD;AAED;;AAEG;AACH,IAAA,IAAW,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAS,KAAK,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;KAC7E;AAED;;AAEG;AACI,IAAA,IAAI,CAAC,MAAc,EAAA;AACxB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC3B;AAED;;AAEG;IACI,MAAM,GAAA;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACtC,YAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AACzB,SAAA;AACD,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KACzB;AACF;;AChFD;;;;;;;;;;;;;;AAcG;AAIH;;;;;;;;AAQG;AACI,MAAM,6BAA6B,GAAG,CAC3C,GAAoB,EACpB,mBAAoD,EACpD,OAAiB,EACjB,qBAA6B,KACjB;AACZ,IAAA,MAAM,iBAAiB,GAAG,4BAA4B,CACpD,GAAG,CAAC,SAAS,EAAE,EACf,mBAAmB,EACnB,qBAAqB,CACtB,CAAC;IACF,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAC3B,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAC5D,CAAC;AACJ,EAAE;AAEF;;AAEG;AACU,MAAA,4BAA4B,GAAG,CAC1C,MAAgC,EAChC,UAA2C,EAC3C,SAAiB,KACW;AAC5B,IAAA,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,yBAAyB,CACxD,MAAM,EACN,UAAU,CACX,CAAC;AACF,IAAA,MAAM,mBAAmB,GAAG,iBAAiB,CAC3C,EAAE,SAAS,EAAE,SAAS,EAAE,EACxB,SAAS,CACV,CAAC;AACF,IAAA,OAAO,yBAAyB,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;AACpE,EAAE;AAEF;;AAEG;AACU,MAAA,iBAAiB,GAAG,CAC/B,MAAgC,EAChC,UAA2C,EAC3C,MAAc,KACsB;IACpC,MAAM,QAAQ,GAAG,4BAA4B,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AAC1E,IAAA,MAAM,EAAE,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;AACnC,IAAA,MAAM,EAAE,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;IAEnC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAClD,EAAE;AAEF;;;;AAIG;MACU,qBAAqB,GAAG,CACnC,EAA6B,EAC7B,EAA6B,KACnB;AACV,IAAA,MAAM,CAAC,GAAG,IAAI,CAAC;AACf,IAAA,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;AACjD,IAAA,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;IACjD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IACnC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AACnC,IAAA,MAAM,CAAC,GACL,OAAO,GAAG,OAAO;AACjB,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;AAChC,YAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;YAClC,OAAO;AACP,YAAA,OAAO,CAAC;IACZ,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,CAAC;AACf,EAAE;AAOF;;;;AAIG;AACH,MAAM,yBAAyB,GAAG,CAChC,MAAgC,EAChC,UAA2C,KAC5B;IACf,OAAO;QACL,SAAS,EAAE,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;QACjE,SAAS,EAAE,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;KAClE,CAAC;AACJ,CAAC,CAAC;AAEF;;;;AAIG;AACI,MAAM,iBAAiB,GAAG,CAC/B,EAAE,SAAS,EAAE,SAAS,EAAe,EACrC,SAAiB,KACF;AACf,IAAA,SAAS,CAAC,CAAC,IAAI,SAAS,CAAC;AACzB,IAAA,SAAS,CAAC,CAAC,IAAI,SAAS,CAAC;AAEzB,IAAA,SAAS,CAAC,CAAC,IAAI,SAAS,CAAC;AACzB,IAAA,SAAS,CAAC,CAAC,IAAI,SAAS,CAAC;AAEzB,IAAA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;AAClC,EAAE;AAEF;;AAEG;AACI,MAAM,yBAAyB,GAAG,CACvC,EAAE,SAAS,EAAE,SAAS,EAAe,EACrC,UAA2C,KACf;IAC5B,MAAM,EAAE,GAAG,UAAU,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;IACtD,MAAM,EAAE,GAAG,UAAU,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;IACtD,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC9C;;ACrJA;;;;;;;;;;;;;;AAcG;AAiDH;;AAEG;MACmB,iBAAiB,CAAA;AAGrC,IAAA,WAAA,CAAY,EAAE,OAAO,GAAG,EAAE,EAAoB,EAAA;AAC5C,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KACxB;AACD;;;;;;;;;;;AAWG;IACO,IAAI,CAA4C,EACxD,OAAO,GACL,EAAA;AACF,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;KACtB;AAaF,CAAA;AAaD;;;;;AAKG;AACG,MAAgB,yBAA0B,SAAQ,iBAAiB,CAAA;AAGvE,IAAA,WAAA,CAAY,EAA8D,EAAA;YAA9D,EAAE,eAAe,GAAG,EAAE,EAAA,GAAA,EAAwC,EAAnC,OAAO,GAAA,MAAA,CAAA,EAAA,EAAlC,mBAAoC,CAAF,CAAA;QAC5C,KAAK,CAAC,OAAO,CAAC,CAAC;QAHP,IAAe,CAAA,eAAA,GAAG,EAAE,CAAC;AAI7B,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;KACxC;AACM,IAAA,SAAS,CAAC,EACf,OAAO,EACP,GAAG,EACH,mBAAmB,GACJ,EAAA;QACf,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;YACjC,OAAO;AACL,gBAAA,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;oBAClB,OAAO;iBACR,CAAC;AACF,gBAAA,OAAO,EAAE,KAAK;aACf,CAAC;AACH,SAAA;QAED,OAAO;AACL,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC;AACrB,gBAAA,OAAO,EAAE,6BAA6B,CACpC,GAAG,EACH,mBAAmB,EACnB,OAAO,EACP,IAAI,CAAC,eAAe,CACrB;gBACD,GAAG;gBACH,mBAAmB;aACpB,CAAC;SACH,CAAC;KACH;AAEF,CAAA;AAED;;AAEG;AACU,MAAA,IAAI,GAAG,CAAC,OAAiB,KAAe;AACnD,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAC1B,CAAC,MAAM,KACL,IAAI,OAAO,CAAC;AACV,QAAA,QAAQ,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC;QACzC,OAAO,EAAE,CAAC,MAAM,CAAC;AAClB,KAAA,CAAC,CACL,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC;AAClB;;ACzKA;;;;;;;;;;;;;;AAcG;AA2BH;;;;;;AAMG;AACG,MAAO,aAAc,SAAQ,yBAAyB,CAAA;AAM1D,IAAA,WAAA,CAAY,EAA+D,EAAA;AAA/D,QAAA,IAAA,EAAE,WAAW,GAAG,KAAK,EAAE,QAAQ,GAAG,EAAE,EAAA,GAAA,EAA2B,EAAtB,OAAO,GAAhD,MAAA,CAAA,EAAA,EAAA,CAAA,aAAA,EAAA,UAAA,CAAkD,CAAF,CAAA;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QAJP,IAAQ,CAAA,QAAA,GAAc,EAAE,CAAC;AACzB,QAAA,IAAA,CAAA,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;AAK7B,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC1B;AAEM,IAAA,SAAS,CAAC,EACf,OAAO,EACP,GAAG,EACH,mBAAmB,GACJ,EAAA;QACf,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;QACtC,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAElE;AAAM,aAAA;YACL,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACrC,SAAA;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE;YACjC,OAAO;AACL,gBAAA,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;oBAClB,OAAO;iBACR,CAAC;gBACF,OAAO;aACR,CAAC;AACH,SAAA;QAED,OAAO;AACL,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC;AACrB,gBAAA,OAAO,EAAE,6BAA6B,CACpC,GAAG,EACH,mBAAmB,EACnB,OAAO,EACP,IAAI,CAAC,eAAe,CACrB;gBACD,GAAG;gBACH,mBAAmB;aACpB,CAAC;SACH,CAAC;KACH;AAES,IAAA,OAAO,CAAC,EAChB,OAAO,EACP,GAAG,EACH,mBAAmB,GACJ,EAAA;AACf,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACnB,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;YACzB,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,mBAAmB,CAAC,CAAC;AAC7D,SAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAES,IAAA,mBAAmB,CAC3B,MAAc,EACd,GAAoB,EACpB,UAA2C,EAAA;AAE3C,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnC,IAAI,OAAO,GAAY,IAAI,CAAC;AAE5B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,qBAAqB,CACpC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,EACrC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CACzC,CAAC;YAEF,IAAI,QAAQ,GAAG,WAAW,EAAE;gBAC1B,WAAW,GAAG,QAAQ,CAAC;gBACvB,OAAO,GAAG,SAAS,CAAC;AACrB,aAAA;AACF,SAAA;AAED,QAAA,IACE,OAAO;YACP,4BAA4B,CAC1B,OAAO,CAAC,MAAM,EACd,UAAU,EACV,IAAI,CAAC,QAAQ,CACd,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAC3C;AACA,YAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtB,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACnD,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,SAAA;KACF;AACF;;ACjJD;;;;;;;;;;;;;;AAcG;AAWH;;AAEG;AACG,MAAO,aAAc,SAAQ,iBAAiB,CAAA;AAClD,IAAA,WAAA,CAAY,EAAgC,EAAA;YAA3B,OAAO,GAAA,MAAA,CAAA,EAAA,EAAZ,EAAc,CAAF,CAAA;QACtB,KAAK,CAAC,OAAO,CAAC,CAAC;KAChB;AACM,IAAA,SAAS,CAAC,EACf,OAAO,EACP,GAAG,EACH,mBAAmB,GACJ,EAAA;QACf,OAAO;AACL,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,mBAAmB,EAAE,CAAC;AAC7D,YAAA,OAAO,EAAE,KAAK;SACf,CAAC;KACH;AAES,IAAA,OAAO,CAAC,KAAqB,EAAA;AACrC,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzB;AACF;;AC9CD;;;;;;;;;;;;;;AAcG;AAeH;;;;AAIG;AACG,MAAO,qBAAsB,SAAQ,iBAAiB,CAAA;AAM1D,IAAA,WAAA,CAAY,EAAyD,EAAA;YAAzD,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,OAAmC,EAA9B,OAAO,GAAlC,MAAA,CAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,CAAoC,CAAF,CAAA;AAC5C,QAAA,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;AAHX,QAAA,IAAA,CAAA,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;AAK7B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,iBAClC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EACH,EAAA,OAAO,EACV,CAAC;KACJ;AAEM,IAAA,SAAS,CAAC,KAAqB,EAAA;QACpC,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,QAAA,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC;QAE5C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,GAAG,IAAI,CAAC;;YAEf,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;YAElC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAI;gBACzC,MAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjD,gBAAA,MAAM,WAAW,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;gBACrD,OAAO;AACL,oBAAA,IAAI,EAAE,SAAkB;AACxB,oBAAA,QAAQ,EAAE;AACR,wBAAA,IAAI,EAAE,OAAgB;wBACtB,WAAW;AACZ,qBAAA;oBACD,UAAU,EAAE,EAAE,MAAM,EAAE;iBACvB,CAAC;AACJ,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,SAAA;QAED,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjE,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACrC,aAAA;AACF,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAEnB,QAAA,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACrC,SAAA;QAED,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;KAC7C;IAEM,OAAO,CAAC,EAAE,GAAG,EAAkB,EAAA;QACpC,OAAO,IAAI,CAAC,YAAY;aACrB,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AAC5D,aAAA,GAAG,CAAC,CAAC,OAA2C,KAC/C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAC/B,CAAC;KACL;AAES,IAAA,gBAAgB,CAAC,EACzB,QAAQ,EAAE,EACR,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GACxB,EACD,UAAU,GACyB,EAAA;QACnC,IAAI,UAAU,CAAC,OAAO,EAAE;YACtB,OAAO,IAAI,OAAO,CAAC;gBACjB,OAAO,EAAE,IAAI,CAAC,YAAY;AACvB,qBAAA,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,CAAC;qBAC1C,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACxC,gBAAA,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvB,aAAA,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QAEjC,OAAO,IAAI,OAAO,CAAC;YACjB,OAAO,EAAE,CAAC,MAAM,CAAC;AACjB,YAAA,QAAQ,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1C,SAAA,CAAC,CAAC;KACJ;AACF;;ACvHD;;;;;;;;;;;;;;AAcG;AA2BH;;;;AAIG;AACG,MAAO,6BAA8B,SAAQ,yBAAyB,CAAA;AAM1E,IAAA,WAAA,CAAY,EAKkB,EAAA;AALlB,QAAA,IAAA,EACV,OAAO,EACP,MAAM,GAAG,EAAE,EACX,eAAe,GAAG,EAAE,EAEQ,GAAA,EAAA,EADzB,OAAO,GAAA,MAAA,CAAA,EAAA,EAJA,wCAKX,CADW,CAAA;AAEV,QAAA,KAAK,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;AAEpC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,iBAClC,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,MAAM,EACH,EAAA,OAAO,EACV,CAAC;QAEH,IAAI,CAAC,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;KAC/C;AAEM,IAAA,SAAS,CAAC,KAAqB,EAAA;AACpC,QAAA,MAAM,KAAK,GAA8B;YACvC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;AACrC,YAAA,IAAI,EAAE,iBAAiB,CACrB,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,EACrB,KAAK,CAAC,mBAAmB,EACzB,IAAI,CAAC,eAAe,CACrB;SACF,CAAC;QAEF,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,GAAG,IAAI,CAAC;;YAEf,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;YAElC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAI;gBACzC,MAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjD,gBAAA,MAAM,WAAW,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;gBACrD,OAAO;AACL,oBAAA,IAAI,EAAE,SAAkB;AACxB,oBAAA,QAAQ,EAAE;AACR,wBAAA,IAAI,EAAE,OAAgB;wBACtB,WAAW;AACZ,qBAAA;oBACD,UAAU,EAAE,EAAE,MAAM,EAAE;iBACvB,CAAC;AACJ,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB,SAAA;QAED,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;KAC7C;AAEM,IAAA,OAAO,CAAC,EAAE,GAAG,EAAE,mBAAmB,EAAkB,EAAA;;AAEzD,QAAA,MAAM,KAAK,GAA8B;YACvC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;AAC/B,YAAA,IAAI,EAAE,iBAAiB,CACrB,GAAG,CAAC,SAAS,EAAE,EACf,mBAAmB,EACnB,IAAI,CAAC,eAAe,CACrB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,YAAY;aACrB,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;AACnC,aAAA,GAAG,CAAC,CAAC,OAA2C,KAC/C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAC/B,CAAC;KACL;AAES,IAAA,gBAAgB,CAAC,EACzB,QAAQ,EAAE,EACR,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GACxB,EACD,UAAU,GACyB,EAAA;QACnC,IAAI,UAAU,CAAC,OAAO,EAAE;YACtB,OAAO,IAAI,OAAO,CAAC;gBACjB,OAAO,EAAE,IAAI,CAAC,YAAY;AACvB,qBAAA,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,QAAQ,CAAC;qBAC1C,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACxC,gBAAA,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvB,aAAA,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QAEjC,OAAO,IAAI,OAAO,CAAC;YACjB,OAAO,EAAE,CAAC,MAAM,CAAC;AACjB,YAAA,QAAQ,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1C,SAAA,CAAC,CAAC;KACJ;AACF;;ACpJD;;;;;;;;;;;;;;AAcG;AAKH;;AAEG;MACU,YAAY,CAAA;IAYvB,WAAY,CAAA,OAAiB,EAAE,QAAmB,EAAA;QAChD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;AACvC,QAAA,MAAM,mBAAmB,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACzD,QAAA,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAExE,IAAI,CAAC,QAAQ,GAAG;YACd,KAAK,EAAE,QAAQ,CAAC,MAAM;AACtB,YAAA,OAAO,EAAE;AACP,gBAAA,IAAI,EAAE,gBAAgB,GAAG,QAAQ,CAAC,MAAM;AACxC,gBAAA,GAAG,EAAE,gBAAgB;AACrB,gBAAA,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC;AACrC,gBAAA,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC;AACtC,aAAA;SACF,CAAC;KACH;AACF,CAAA;MAkBY,eAAe,CAAA;AAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCG;IACI,MAAM,CACX,EAAE,KAAK,EAAE,QAAQ,EAAW,EAC5B,KAAmB,EACnB,GAAoB,EAAA;;QAGpB,MAAM,KAAK,GACT,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;;QAG5E,MAAM,GAAG,GAAG,CAAA,WAAA,EAAc,KAAK,CAAA;;;;+IAI4G,KAAK,CAAA;OAC7I,CAAC;AAEJ,QAAA,MAAM,KAAK,GAAG,CAAc,WAAA,EAAA,KAAK,CAAU,QAAA,CAAA;;AAEzC,QAAA,MAAM,GAAW,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;AAEjE,QAAA,IAAI,WAAW,CAAC,yBAAyB,CAAC,GAAG,CAAC,EAAE;;AAE9C,YAAA,MAAM,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;AAC/B,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,eAAe,CAClC,GAAG,EACH,eAAe,CAChB,CAAC,eAAe,CAAC;AAClB,YAAA,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAEnD,YAAA,MAAM,cAAc,GAAoD;gBACtE,GAAG;gBACH,QAAQ;gBACR,MAAM;gBACN,KAAK;AACL,gBAAA,OAAO,EAAE,KAAK;aACf,CAAC;YACF,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,MAAM,cAAc,GAA8B;YAChD,QAAQ;YACR,MAAM;YACN,KAAK;AACL,YAAA,IAAI,EAAE;AACJ,gBAAA,GAAG,EAAE,CAA6B,0BAAA,EAAA,IAAI,CAAC,GAAG,CAAC,CAAE,CAAA;gBAC7C,MAAM,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;AACtC,aAAA;SACF,CAAC;QACF,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;KAC/C;AACF;;AC7JD;;;;;;;;;;;;;;AAcG;AAKH;;;;;;AAMG;AACH;AACA,SAAS,MAAM,CAAC,KAAU,EAAE,KAAU,EAAA;;;AAGpC,IAAA,KAAK,IAAI,QAAQ,IAAI,KAAK,CAAC,SAAS,EAAE;AACpC,QAAA,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACvD,KAAA;AACH,CAAC;AAED;;AAEG;MACU,eAAe,CAAA;AAC1B,IAAA,WAAA,GAAA;;;;;;QAME,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAClD;AACF;;AC/CD;;;;;;;;;;;;;;AAcG;IAmCS,sBAIX;AAJD,CAAA,UAAY,qBAAqB,EAAA;AAC/B,IAAA,qBAAA,CAAA,kBAAA,CAAA,GAAA,iBAAoC,CAAA;AACpC,IAAA,qBAAA,CAAA,gBAAA,CAAA,GAAA,eAAgC,CAAA;AAChC,IAAA,qBAAA,CAAA,eAAA,CAAA,GAAA,OAAuB,CAAA;AACzB,CAAC,EAJW,qBAAqB,KAArB,qBAAqB,GAIhC,EAAA,CAAA,CAAA,CAAA;AAEY,MAAA,4BAA4B,GAA0B,CACjE,CAA4B,EAC5B,OAAgB,EAChB,GAAoB,KACZ;AACR,IAAA,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAChC,EAAE;AACF;;;;AAIG;AACG,MAAO,eAAgB,SAAQ,eAAe,CAAA;AAalD,IAAA,WAAA,CAAY,EACV,GAAG,EACH,OAAO,GAAG,EAAE,EACZ,gBAAgB,GAAG,EAAE,EACrB,SAAS,GAAG,IAAI,qBAAqB,CAAC,gBAAgB,CAAC,EACvD,QAAQ,GAAG,IAAI,eAAe,EAAE,EAChC,cAAc,GAAG,4BAA4B,GACtB,EAAA;AACvB,QAAA,KAAK,EAAE,CAAC;AACR,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AAEnB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAEzB,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AAErC,QAAA,IAAI,GAAG,EAAE;AACP,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAClB,SAAA;KACF;IAEM,SAAS,CAAC,MAAc,EAAE,MAAgB,EAAA;QAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACjC,OAAO;AACR,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;KACF;IAEM,UAAU,CAAC,OAAiB,EAAE,MAAgB,EAAA;AACnD,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACzB,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/B,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;KACF;IAEM,YAAY,CAAC,MAAc,EAAE,MAAgB,EAAA;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAE3C,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;;AAEhB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE9B,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACb;IAEM,aAAa,CAAC,OAAiB,EAAE,MAAgB,EAAA;QACtD,IAAI,OAAO,GAAG,KAAK,CAAC;AAEpB,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;YACzB,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC;AACvD,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AAEM,IAAA,YAAY,CAAC,MAAgB,EAAA;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAExB,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,MAAM,EAAE,CAAC;AACf,SAAA;KACF;AAED;;AAEG;IACI,MAAM,GAAA;AACX,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,QAAA,IAAI,GAAG,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,EAAE;AACzD,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CACvB,IAAI,EACJ,qBAAqB,CAAC,gBAAgB,EACtC,IAAI,CACL,CAAC;YACF,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;gBACrD,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,GAAG;AACH,gBAAA,mBAAmB,EAAE,IAAI,CAAC,aAAa,EAAE;AAC1C,aAAA,CAAC,CAAC;;AAGH,YAAA,IAAI,OAAO,IAAI,OAAO,IAAI,SAAS,EAAE;;;;AAInC,gBAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;AACvC,gBAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAC9B,oBAAA,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;wBAC/B,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,qBAAA;AACF,iBAAA;gBAED,MAAM,YAAY,GAAa,EAAE,CAAC;;AAElC,gBAAA,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,oBAAA,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;wBAC1B,SAAS;AACV,qBAAA;AACD,oBAAA,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;wBAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;;;4BAIrC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC1C,yBAAA;AACF,qBAAA;AAAM,yBAAA;;AAEL,wBAAA,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,qBAAA;AACF,iBAAA;AAED,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBACzB,IAAI,CAAC,cAAc,EAAE,CAAC;;gBAGtB,qBAAqB,CAAC,MACpB,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CACnE,CAAC;AACH,aAAA;AACD,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CACvB,IAAI,EACJ,qBAAqB,CAAC,cAAc,EACpC,IAAI,CACL,CAAC;AACH,SAAA;KACF;IAEM,KAAK,GAAA;QACV,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,CAC3C,MAAM,EACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CACvB,CAAC;QACF,IAAI,CAAC,MAAM,EAAE,CAAC;KACf;IAEM,QAAQ,GAAA;QACb,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;IAES,KAAK,GAAA;AACb,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;KACpB;IAES,cAAc,GAAA;;AAEtB,QAAA,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5D,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAqB,CAAC;QAE7C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AAChC,YAAA,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrC,aAAA;AAAM,iBAAA;;AAEL,gBAAA,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;;AAE3D,gBAAA,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;gBACtE,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,oBAAA,OAAO,CAAC,MAAM,CAAC,WAAW,CACxB,OAAO;;oBAEP,CAAC,KAAgC,KAAI;AACnC,wBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CACvB,IAAI,EACJ,qBAAqB,CAAC,aAAa,EACnC,OAAO,CACR,CAAC;wBACF,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAC3C,qBAAC,CACF,CAAC;AACH,iBAAA;AACF,aAAA;YACD,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC1C,SAAC,CAAC,CAAC;KACJ;AACF;;;;"}