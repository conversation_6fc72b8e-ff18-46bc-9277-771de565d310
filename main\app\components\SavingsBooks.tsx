
'use client';

import { useState } from 'react';
import { SavingsBook } from '../types/finance';

interface SavingsBooksProps {
  savingsBooks: SavingsBook[];
  onAdd: (book: Omit<SavingsBook, 'id' | 'expenses'>) => void;
  onUpdate: (id: string, updates: Partial<SavingsBook>) => void;
  onDelete: (id: string) => void;
  onAddExpense: (bookId: string, expense: Omit<SavingsBook['expenses'][0], 'id'>) => void;
  isPreview?: boolean;
}

export default function SavingsBooks({
  savingsBooks,
  onAdd,
  onUpdate,
  onDelete,
  onAddExpense,
  isPreview = false
}: SavingsBooksProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [showExpenseForm, setShowExpenseForm] = useState<string | null>(null);
  const [showExpenseList, setShowExpenseList] = useState<string | null>(null);
  const [newBook, setNewBook] = useState({
    name: '',
    initialAmount: '',
    description: ''
  });
  const [newExpense, setNewExpense] = useState({
    amount: '',
    description: ''
  });

  const handleAddBook = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newBook.name || !newBook.initialAmount) return;

    const amount = parseFloat(newBook.initialAmount);
    onAdd({
      name: newBook.name,
      initialAmount: amount,
      currentAmount: amount,
      description: newBook.description,
      createdDate: new Date().toISOString().split('T')[0]
    });

    setNewBook({ name: '', initialAmount: '', description: '' });
    setShowAddForm(false);
  };

  const handleAddExpense = (e: React.FormEvent, bookId: string) => {
    e.preventDefault();
    if (!newExpense.amount || !newExpense.description) return;

    const amount = parseFloat(newExpense.amount);
    const book = savingsBooks.find(b => b.id === bookId);
    if (!book || book.currentAmount < amount) return;

    onAddExpense(bookId, {
      amount,
      description: newExpense.description,
      date: new Date().toISOString().split('T')[0]
    });

    // Update current amount
    onUpdate(bookId, {
      currentAmount: book.currentAmount - amount
    });

    setNewExpense({ amount: '', description: '' });
    setShowExpenseForm(null);
  };

  const formatNumber = (num: string) => {
    return num.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  const parseNumber = (str: string) => {
    return str.replace(/\./g, '');
  };

  const handleInitialAmountChange = (value: string) => {
    // Remove all non-numeric characters except dots
    const numericValue = value.replace(/[^0-9]/g, '');
    setNewBook({ ...newBook, initialAmount: numericValue });
  };

  const handleExpenseAmountChange = (value: string) => {
    // Remove all non-numeric characters except dots
    const numericValue = value.replace(/[^0-9]/g, '');
    setNewExpense({ ...newExpense, amount: numericValue });
  };

  return (
    <div className="space-y-6">
      {/* Add new savings book button */}
      {!isPreview && (
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">Sổ tiết kiệm</h3>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer"
          >
            <i className="ri-add-line mr-2"></i>
            Thêm sổ mới
          </button>
        </div>
      )}

      {/* Add form */}
      {showAddForm && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Thêm sổ tiết kiệm mới</h4>
          <form onSubmit={handleAddBook} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tên sổ</label>
                <input
                  type="text"
                  value={newBook.name}
                  onChange={(e) => setNewBook({ ...newBook, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  placeholder="Ví dụ: Sổ du lịch"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền ban đầu</label>
                <div className="relative">
                  <input
                    type="text"
                    value={newBook.initialAmount ? formatNumber(newBook.initialAmount) : ''}
                    onChange={(e) => handleInitialAmountChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12"
                    placeholder="0"
                    required
                  />
                  <div className="absolute right-3 top-2 text-sm text-gray-500">₫</div>
                </div>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
              <input
                type="text"
                value={newBook.description}
                onChange={(e) => setNewBook({ ...newBook, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                placeholder="Mô tả mục đích sử dụng"
              />
            </div>
            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer"
              >
                Thêm sổ
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 text-sm font-medium whitespace-nowrap cursor-pointer"
              >
                Hủy
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Savings books grid */}
      <div className={`grid grid-cols-1 ${isPreview ? 'md:grid-cols-1' : 'md:grid-cols-2 lg:grid-cols-3'} gap-6`}>
        {savingsBooks.map((book) => {
          const totalExpenses = book.expenses.reduce((sum, expense) => sum + expense.amount, 0);
          const isEmptyBook = book.currentAmount <= 0;

          return (
            <div key={book.id} className={`bg-white rounded-lg shadow p-6 ${isEmptyBook ? 'border-2 border-red-200' : ''}`}>
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900">{book.name}</h4>
                {!isPreview && (
                  <button
                    onClick={() => onDelete(book.id)}
                    className="w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full cursor-pointer"
                  >
                    <i className="ri-delete-bin-line text-sm"></i>
                  </button>
                )}
              </div>

              {isEmptyBook && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center space-x-2">
                    <i className="ri-alert-line text-red-600"></i>
                    <span className="text-red-700 font-medium text-sm">Sổ đã tất toán</span>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Số tiền ban đầu:</span>
                  <span className="font-medium text-gray-900">{book.initialAmount.toLocaleString()}₫</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Đã chi tiêu:</span>
                  <span className="font-medium text-red-600">{totalExpenses.toLocaleString()}₫</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Còn lại:</span>
                  <span className={`font-bold ${isEmptyBook ? 'text-red-600' : 'text-green-600'}`}>
                    {book.currentAmount.toLocaleString()}₫
                  </span>
                </div>
              </div>

              {book.description && (
                <p className="text-sm text-gray-600 mt-3 italic">{book.description}</p>
              )}

              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  {!isEmptyBook && (
                    <button
                      onClick={() => setShowExpenseForm(showExpenseForm === book.id ? null : book.id)}
                      className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer"
                    >
                      <i className="ri-subtract-line mr-1"></i>
                      Rút tiền
                    </button>
                  )}
                  {book.expenses.length > 0 && (
                    <button
                      onClick={() => setShowExpenseList(showExpenseList === book.id ? null : book.id)}
                      className="flex-1 bg-gray-600 text-white px-3 py-2 rounded-md hover:bg-gray-700 text-sm font-medium whitespace-nowrap cursor-pointer"
                    >
                      <i className="ri-list-check mr-1"></i>
                      Lịch sử
                    </button>
                  )}
                </div>
              </div>

              {/* Expense form */}
              {showExpenseForm === book.id && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h5 className="font-medium text-gray-900 mb-3">Rút tiền từ sổ</h5>
                  <form onSubmit={(e) => handleAddExpense(e, book.id)} className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền rút</label>
                      <div className="relative">
                        <input
                          type="text"
                          value={newExpense.amount ? formatNumber(newExpense.amount) : ''}
                          onChange={(e) => handleExpenseAmountChange(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm pr-12"
                          placeholder="0"
                          required
                        />
                        <div className="absolute right-3 top-2 text-sm text-gray-500">₫</div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Tối đa: {book.currentAmount.toLocaleString()}₫
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Mục đích</label>
                      <input
                        type="text"
                        value={newExpense.description}
                        onChange={(e) => setNewExpense({ ...newExpense, description: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        placeholder="Để làm gì?"
                        required
                      />
                    </div>
                    <div className="flex space-x-2">
                      <button
                        type="submit"
                        className="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer"
                      >
                        Rút tiền
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowExpenseForm(null)}
                        className="bg-gray-300 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-400 text-sm font-medium whitespace-nowrap cursor-pointer"
                      >
                        Hủy
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {/* Expense list */}
              {showExpenseList === book.id && book.expenses.length > 0 && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h5 className="font-medium text-gray-900 mb-3">Lịch sử chi tiêu</h5>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {book.expenses.map((expense) => (
                      <div key={expense.id} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{expense.description}</p>
                          <p className="text-xs text-gray-500">{new Date(expense.date).toLocaleDateString('vi-VN')}</p>
                        </div>
                        <span className="text-sm font-medium text-red-600">
                          -{expense.amount.toLocaleString()}₫
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {savingsBooks.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <i className="ri-book-line text-4xl mb-4"></i>
          <p className="text-lg mb-2">Chưa có sổ tiết kiệm nào</p>
          <p className="text-sm">Tạo sổ tiết kiệm đầu tiên để bắt đầu tiết kiệm</p>
          {!isPreview && (
            <button
              onClick={() => setShowAddForm(true)}
              className="mt-4 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 text-sm font-medium whitespace-nowrap cursor-pointer"
            >
              <i className="ri-add-line mr-2"></i>
              Thêm sổ tiết kiệm
            </button>
          )}
        </div>
      )}

      {/* Add floating action button for preview mode */}
      {isPreview && (
        <div className="text-center">
          <button
            onClick={() => setShowAddForm(true)}
            className="w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 shadow-lg cursor-pointer flex items-center justify-center"
          >
            <i className="ri-add-line text-lg"></i>
          </button>
        </div>
      )}
    </div>
  );
}
