
export interface Transaction {
  id: string;
  type: 'income' | 'expense' | 'investment';
  amount: number;
  category: string;
  description: string;
  date: string;
  investmentData?: {
    profitLoss: number;
    period: 'week' | 'month' | 'year';
    lastUpdated: string;
  };
}

export interface Investment {
  month: string;
  value: number;
  profit: number;
}

export interface SavingsBook {
  id: string;
  name: string;
  initialAmount: number;
  currentAmount: number;
  description: string;
  createdDate: string;
  expenses: Array<{
    id: string;
    amount: number;
    description: string;
    date: string;
  }>;
}
