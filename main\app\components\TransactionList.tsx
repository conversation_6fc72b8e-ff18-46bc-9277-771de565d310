
'use client';

import { Transaction } from '../types/finance';

interface TransactionListProps {
  transactions: Transaction[];
  onDelete: (id: string) => void;
}

export default function TransactionList({ transactions, onDelete }: TransactionListProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'income':
        return 'text-green-600 bg-green-50';
      case 'expense':
        return 'text-red-600 bg-red-50';
      case 'investment':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'income':
        return 'ri-arrow-up-circle-fill';
      case 'expense':
        return 'ri-arrow-down-circle-fill';
      case 'investment':
        return 'ri-line-chart-fill';
      default:
        return 'ri-money-dollar-circle-fill';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'income':
        return 'Thu nhập';
      case 'expense':
        return 'Chi tiêu';
      case 'investment':
        return 'Đầu tư';
      default:
        return type;
    }
  };

  const getPeriodLabel = (period: string) => {
    switch (period) {
      case 'week':
        return 'Tuần';
      case 'month':
        return 'Tháng';
      case 'year':
        return 'Năm';
      default:
        return period;
    }
  };

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <i className="ri-inbox-line text-4xl mb-2"></i>
        <p>Chưa có giao dịch nào</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-200">
            <th className="text-left py-3 px-4 font-medium text-gray-700">Loại</th>
            <th className="text-left py-3 px-4 font-medium text-gray-700">Số tiền</th>
            <th className="text-left py-3 px-4 font-medium text-gray-700">Lãi/Lỗ</th>
            <th className="text-left py-3 px-4 font-medium text-gray-700">Danh mục</th>
            <th className="text-left py-3 px-4 font-medium text-gray-700">Mô tả</th>
            <th className="text-left py-3 px-4 font-medium text-gray-700">Ngày</th>
            <th className="text-left py-3 px-4 font-medium text-gray-700">Thao tác</th>
          </tr>
        </thead>
        <tbody>
          {transactions.map((transaction) => (
            <tr key={transaction.id} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="py-3 px-4">
                <div className="flex items-center space-x-2">
                  <div className={`w-8 h-8 flex items-center justify-center rounded-full ${getTypeColor(transaction.type)}`}>
                    <i className={`${getTypeIcon(transaction.type)} text-sm`}></i>
                  </div>
                  <span className="text-sm font-medium">{getTypeLabel(transaction.type)}</span>
                </div>
              </td>
              <td className="py-3 px-4">
                <span className={`font-medium ${
                  transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {transaction.type === 'income' ? '+' : '-'}{transaction.amount.toLocaleString()}₫
                </span>
              </td>
              <td className="py-3 px-4">
                {transaction.investmentData ? (
                  <div className="space-y-1">
                    <div className={`font-medium text-sm ${
                      transaction.investmentData.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.investmentData.profitLoss >= 0 ? '+' : ''}{transaction.investmentData.profitLoss.toLocaleString()}₫
                    </div>
                    <div className="text-xs text-gray-500">
                      /{getPeriodLabel(transaction.investmentData.period)}
                    </div>
                  </div>
                ) : (
                  <span className="text-sm text-gray-400">-</span>
                )}
              </td>
              <td className="py-3 px-4 text-sm text-gray-900">{transaction.category}</td>
              <td className="py-3 px-4 text-sm text-gray-600">{transaction.description}</td>
              <td className="py-3 px-4 text-sm text-gray-600">{new Date(transaction.date).toLocaleDateString('vi-VN')}</td>
              <td className="py-3 px-4">
                <button
                  onClick={() => onDelete(transaction.id)}
                  className="w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full cursor-pointer"
                >
                  <i className="ri-delete-bin-line text-sm"></i>
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}