import { Cluster } from './Cluster';
import type { ClusterIcon } from './ClusterIcon';
import type { MarkerExtended, ClustererOptions, ClusterIconStyle, TCalculator } from './types';
export declare class Clusterer implements google.maps.OverlayView {
    markers: MarkerExtended[];
    clusters: Cluster[];
    listeners: google.maps.MapsEventListener[];
    activeMap: google.maps.Map | google.maps.StreetViewPanorama | null;
    ready: boolean;
    gridSize: number;
    minClusterSize: number;
    maxZoom: number | null;
    styles: ClusterIconStyle[];
    title: string;
    zoomOnClick: boolean;
    averageCenter: boolean;
    ignoreHidden: boolean;
    enableRetinaIcons: boolean;
    imagePath: string;
    imageExtension: string;
    imageSizes: number[];
    calculator: TCalculator;
    batchSize: number;
    batchSizeIE: number;
    clusterClass: string;
    timerRefStatic: number | null;
    constructor(map: google.maps.Map, optMarkers?: MarkerExtended[], optOptions?: ClustererOptions);
    onZoomChanged(): void;
    onIdle(): void;
    onAdd(): void;
    onRemove(): void;
    draw(): void;
    getMap(): null;
    getPanes(): null;
    getProjection(): {
        fromContainerPixelToLatLng(): null;
        fromDivPixelToLatLng(): null;
        fromLatLngToContainerPixel(): null;
        fromLatLngToDivPixel(): null;
        getVisibleRegion(): null;
        getWorldWidth(): number;
    };
    setMap(): void;
    addListener(): {
        remove(): void;
    };
    bindTo(): void;
    get(): void;
    notify(): void;
    set(): void;
    setValues(): void;
    unbind(): void;
    unbindAll(): void;
    setupStyles(): void;
    fitMapToMarkers(): void;
    getGridSize(): number;
    setGridSize(gridSize: number): void;
    getMinimumClusterSize(): number;
    setMinimumClusterSize(minimumClusterSize: number): void;
    getMaxZoom(): number | null;
    setMaxZoom(maxZoom: number): void;
    getStyles(): ClusterIconStyle[];
    setStyles(styles: ClusterIconStyle[]): void;
    getTitle(): string;
    setTitle(title: string): void;
    getZoomOnClick(): boolean;
    setZoomOnClick(zoomOnClick: boolean): void;
    getAverageCenter(): boolean;
    setAverageCenter(averageCenter: boolean): void;
    getIgnoreHidden(): boolean;
    setIgnoreHidden(ignoreHidden: boolean): void;
    getEnableRetinaIcons(): boolean;
    setEnableRetinaIcons(enableRetinaIcons: boolean): void;
    getImageExtension(): string;
    setImageExtension(imageExtension: string): void;
    getImagePath(): string;
    setImagePath(imagePath: string): void;
    getImageSizes(): number[];
    setImageSizes(imageSizes: number[]): void;
    getCalculator(): TCalculator;
    setCalculator(calculator: TCalculator): void;
    getBatchSizeIE(): number;
    setBatchSizeIE(batchSizeIE: number): void;
    getClusterClass(): string;
    setClusterClass(clusterClass: string): void;
    getMarkers(): MarkerExtended[];
    getTotalMarkers(): number;
    getClusters(): Cluster[];
    getTotalClusters(): number;
    addMarker(marker: MarkerExtended, optNoDraw: boolean): void;
    addMarkers(markers: MarkerExtended[], optNoDraw: boolean): void;
    pushMarkerTo(marker: MarkerExtended): void;
    removeMarker_(marker: MarkerExtended): boolean;
    removeMarker(marker: MarkerExtended, optNoDraw: boolean): boolean;
    removeMarkers(markers: MarkerExtended[], optNoDraw: boolean): boolean;
    clearMarkers(): void;
    repaint(): void;
    getExtendedBounds(bounds: google.maps.LatLngBounds): google.maps.LatLngBounds;
    redraw(): void;
    resetViewport(optHide: boolean): void;
    distanceBetweenPoints(p1: google.maps.LatLng, p2: google.maps.LatLng): number;
    isMarkerInBounds(marker: MarkerExtended, bounds: google.maps.LatLngBounds): boolean;
    addToClosestCluster(marker: MarkerExtended): void;
    createClusters(iFirst: number): void;
    extend<A extends typeof Clusterer | typeof ClusterIcon>(obj1: A, obj2: typeof google.maps.OverlayView): A;
}
