
'use client';

import { useState, useEffect } from 'react';
import DashboardStats from './components/DashboardStats';
import TransactionForm from './components/TransactionForm';
import TransactionList from './components/TransactionList';
import SavingsBooks from './components/SavingsBooks';
import { Transaction, Investment, SavingsBook } from './types/finance';

export default function Home() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [savingsBooks, setSavingsBooks] = useState<SavingsBook[]>([]);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'transactions' | 'savings'>('dashboard');

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedTransactions = localStorage.getItem('finance-transactions');
    const savedSavingsBooks = localStorage.getItem('finance-savings-books');
    
    if (savedTransactions) {
      setTransactions(JSON.parse(savedTransactions));
    } else {
      // Default transactions if no saved data
      const defaultTransactions = [
        { id: '1', type: 'income', amount: 5000, category: 'Lương', description: 'L<PERSON>ơng tháng', date: '2024-01-15' },
        { id: '2', type: 'expense', amount: 1200, category: 'Tiền nhà', description: 'Tiền thuê nhà hàng tháng', date: '2024-01-01' },
        { id: '3', type: 'expense', amount: 300, category: 'Thực phẩm', description: 'Mua sắm hàng tuần', date: '2024-01-08' },
        { 
          id: '4', 
          type: 'investment', 
          amount: 2000, 
          category: 'Cổ phiếu', 
          description: 'Mua cổ phiếu công nghệ', 
          date: '2024-01-10',
          investmentData: {
            profitLoss: 150,
            period: 'month',
            lastUpdated: '2024-01-20T10:00:00Z'
          }
        },
        { id: '5', type: 'income', amount: 500, category: 'Freelance', description: 'Hoàn thành dự án', date: '2024-01-12' },
        { 
          id: '6', 
          type: 'investment', 
          amount: 1500, 
          category: 'Tiền điện tử', 
          description: 'Mua Bitcoin', 
          date: '2024-01-18',
          investmentData: {
            profitLoss: -200,
            period: 'week',
            lastUpdated: '2024-01-25T15:30:00Z'
          }
        },
      ];
      setTransactions(defaultTransactions);
    }

    if (savedSavingsBooks) {
      setSavingsBooks(JSON.parse(savedSavingsBooks));
    }
  }, []);

  // Save data to localStorage whenever transactions or savings books change
  useEffect(() => {
    localStorage.setItem('finance-transactions', JSON.stringify(transactions));
  }, [transactions]);

  useEffect(() => {
    localStorage.setItem('finance-savings-books', JSON.stringify(savingsBooks));
  }, [savingsBooks]);

  const addTransaction = (transaction: Omit<Transaction, 'id'>) => {
    const newTransaction = {
      ...transaction,
      id: Date.now().toString(),
    };
    setTransactions([newTransaction, ...transactions]);
  };

  const deleteTransaction = (id: string) => {
    setTransactions(transactions.filter(t => t.id !== id));
  };

  const addSavingsBook = (book: Omit<SavingsBook, 'id' | 'expenses'>) => {
    const newBook: SavingsBook = {
      ...book,
      id: Date.now().toString(),
      expenses: [],
    };
    setSavingsBooks([...savingsBooks, newBook]);
  };

  const updateSavingsBook = (id: string, updates: Partial<SavingsBook>) => {
    setSavingsBooks(savingsBooks.map(book => 
      book.id === id ? { ...book, ...updates } : book
    ));
  };

  const deleteSavingsBook = (id: string) => {
    setSavingsBooks(savingsBooks.filter(book => book.id !== id));
  };

  const addExpenseToSavingsBook = (bookId: string, expense: Omit<SavingsBook['expenses'][0], 'id'>) => {
    setSavingsBooks(savingsBooks.map(book => {
      if (book.id === bookId) {
        const newExpense = {
          ...expense,
          id: Date.now().toString(),
        };
        return {
          ...book,
          expenses: [...book.expenses, newExpense],
        };
      }
      return book;
    }));
  };

  const totalIncome = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
  const totalExpenses = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
  const totalInvestments = transactions.filter(t => t.type === 'investment').reduce((sum, t) => sum + t.amount, 0);
  
  // Calculate total profit/loss from investments
  const totalInvestmentProfitLoss = transactions
    .filter(t => t.type === 'investment' && t.investmentData)
    .reduce((sum, t) => sum + (t.investmentData?.profitLoss || 0), 0);

  // Calculate current balance including investment profit/loss
  const currentBalance = totalIncome - totalExpenses - totalInvestments + totalInvestmentProfitLoss;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 flex items-center justify-center bg-blue-600 rounded-lg">
                <i className="ri-wallet-3-line text-white text-lg"></i>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">Quản lý tài chính cá nhân</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">Số dư hiện tại</div>
              <div className={`text-lg font-bold ${currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {currentBalance.toLocaleString()}₫
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer ${
                activeTab === 'dashboard'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Tổng quan
            </button>
            <button
              onClick={() => setActiveTab('transactions')}
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer ${
                activeTab === 'transactions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Giao dịch
            </button>
            <button
              onClick={() => setActiveTab('savings')}
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap cursor-pointer ${
                activeTab === 'savings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Sổ tiết kiệm
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            <DashboardStats 
              totalIncome={totalIncome}
              totalExpenses={totalExpenses}
              totalInvestments={totalInvestments}
              totalInvestmentProfitLoss={totalInvestmentProfitLoss}
              currentBalance={currentBalance}
            />
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Thêm giao dịch mới</h3>
                <TransactionForm onSubmit={addTransaction} />
              </div>
              
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Sổ tiết kiệm của bạn</h3>
                <SavingsBooks 
                  savingsBooks={savingsBooks.slice(0, 3)}
                  onAdd={addSavingsBook}
                  onUpdate={updateSavingsBook}
                  onDelete={deleteSavingsBook}
                  onAddExpense={addExpenseToSavingsBook}
                  isPreview={true}
                />
                {savingsBooks.length > 3 && (
                  <button
                    onClick={() => setActiveTab('savings')}
                    className="mt-4 text-blue-600 hover:text-blue-800 text-sm cursor-pointer"
                  >
                    Xem tất cả sổ tiết kiệm →
                  </button>
                )}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Giao dịch gần đây</h3>
                <TransactionList 
                  transactions={transactions.slice(0, 5)} 
                  onDelete={deleteTransaction}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Thêm giao dịch mới</h3>
              <TransactionForm onSubmit={addTransaction} />
            </div>
            
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tất cả giao dịch</h3>
                <TransactionList 
                  transactions={transactions} 
                  onDelete={deleteTransaction}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'savings' && (
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quản lý sổ tiết kiệm</h3>
              <SavingsBooks 
                savingsBooks={savingsBooks}
                onAdd={addSavingsBook}
                onUpdate={updateSavingsBook}
                onDelete={deleteSavingsBook}
                onAddExpense={addExpenseToSavingsBook}
                isPreview={false}
              />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
