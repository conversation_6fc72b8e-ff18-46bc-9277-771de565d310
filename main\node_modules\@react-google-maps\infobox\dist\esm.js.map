{"version": 3, "file": "esm.js", "sources": ["../src/InfoBox.tsx"], "sourcesContent": ["/* global google */\n/* eslint-disable filenames/match-regex */\nimport type { InfoBoxOptions } from './types'\n\n// This handler prevents an event in the InfoBox from being passed on to the map.\nfunction cancelHandler(event: Event) {\n  event.cancelBubble = true\n\n  if (event.stopPropagation) {\n    event.stopPropagation()\n  }\n}\n\nexport class InfoBox {\n  content: string | Node\n  disableAutoPan: boolean\n  maxWidth: number\n  pixelOffset: google.maps.Size\n  position: google.maps.LatLng\n  zIndex: number | undefined | null\n  boxClass: string\n  boxStyle: Partial<CSSStyleDeclaration>\n\n  closeBoxMargin: string\n  closeBoxURL: string\n  infoBoxClearance: google.maps.Size\n  isHidden: boolean\n  alignBottom: boolean\n  pane: keyof google.maps.MapPanes\n  enableEventPropagation: boolean\n  div: HTMLDivElement | null\n  closeListener: google.maps.MapsEventListener | null\n  moveListener: google.maps.MapsEventListener | null\n  mapListener: google.maps.MapsEventListener | null\n  contextListener: google.maps.MapsEventListener | null\n  eventListeners: google.maps.MapsEventListener[] | null\n  fixedWidthSet: boolean | null\n\n  constructor(options: InfoBoxOptions = {}) {\n    this.getCloseClickHandler = this.getCloseClickHandler.bind(this)\n    this.closeClickHandler = this.closeClickHandler.bind(this)\n    this.createInfoBoxDiv = this.createInfoBoxDiv.bind(this)\n    this.addClickHandler = this.addClickHandler.bind(this)\n    this.getCloseBoxImg = this.getCloseBoxImg.bind(this)\n    this.getBoxWidths = this.getBoxWidths.bind(this)\n    this.setBoxStyle = this.setBoxStyle.bind(this)\n    this.setPosition = this.setPosition.bind(this)\n    this.getPosition = this.getPosition.bind(this)\n    this.setOptions = this.setOptions.bind(this)\n    this.setContent = this.setContent.bind(this)\n    this.setVisible = this.setVisible.bind(this)\n    this.getContent = this.getContent.bind(this)\n    this.getVisible = this.getVisible.bind(this)\n    this.setZIndex = this.setZIndex.bind(this)\n    this.getZIndex = this.getZIndex.bind(this)\n    this.onRemove = this.onRemove.bind(this)\n    this.panBox = this.panBox.bind(this)\n    this.extend = this.extend.bind(this)\n    this.close = this.close.bind(this)\n    this.draw = this.draw.bind(this)\n    this.show = this.show.bind(this)\n    this.hide = this.hide.bind(this)\n    this.open = this.open.bind(this)\n\n    this.extend(InfoBox, google.maps.OverlayView)\n\n    // Standard options (in common with google.maps.InfoWindow):\n    this.content = options.content || ''\n    this.disableAutoPan = options.disableAutoPan || false\n    this.maxWidth = options.maxWidth || 0\n    this.pixelOffset = options.pixelOffset || new google.maps.Size(0, 0)\n    this.position = options.position || new google.maps.LatLng(0, 0)\n    this.zIndex = options.zIndex || null\n\n    // Additional options (unique to InfoBox):\n    this.boxClass = options.boxClass || 'infoBox'\n    this.boxStyle = options.boxStyle || {} as Partial<CSSStyleDeclaration>\n    this.closeBoxMargin = options.closeBoxMargin || '2px'\n    this.closeBoxURL = options.closeBoxURL || 'http://www.google.com/intl/en_us/mapfiles/close.gif'\n    if (options.closeBoxURL === '') {\n      this.closeBoxURL = ''\n    }\n    this.infoBoxClearance = options.infoBoxClearance || new google.maps.Size(1, 1)\n\n    if (typeof options.visible === 'undefined') {\n      if (typeof options.isHidden === 'undefined') {\n        options.visible = true\n      } else {\n        options.visible = !options.isHidden\n      }\n    }\n\n    this.isHidden = !options.visible\n\n    this.alignBottom = options.alignBottom || false\n    this.pane = options.pane || 'floatPane'\n    this.enableEventPropagation = options.enableEventPropagation || false\n\n    this.div = null\n    this.closeListener = null\n    this.moveListener = null\n    this.mapListener = null\n    this.contextListener = null\n    this.eventListeners = null\n    this.fixedWidthSet = null\n  }\n\n  createInfoBoxDiv(): void {\n    // This handler ignores the current event in the InfoBox and conditionally prevents\n    // the event from being passed on to the map. It is used for the contextmenu event.\n    const ignoreHandler = (event: Event) => {\n      event.returnValue = false\n\n      if (event.preventDefault) {\n        event.preventDefault()\n      }\n\n      if (!this.enableEventPropagation) {\n        cancelHandler(event)\n      }\n    }\n\n    if (!this.div) {\n      this.div = document.createElement('div')\n      this.setBoxStyle()\n\n      if (typeof this.content === 'string') {\n        this.div.innerHTML = this.getCloseBoxImg() + this.content\n      } else {\n        this.div.innerHTML = this.getCloseBoxImg()\n        this.div.appendChild(this.content)\n      }\n\n      const panes = (this as unknown as google.maps.OverlayView).getPanes()\n\n      if (panes !== null) {\n        panes[this.pane].appendChild(this.div) // Add the InfoBox div to the DOM\n      }\n\n      this.addClickHandler()\n\n      if (this.div.style.width) {\n        this.fixedWidthSet = true\n      } else {\n        if (this.maxWidth !== 0 && this.div.offsetWidth > this.maxWidth) {\n          this.div.style.width = this.maxWidth + 'px'\n          this.fixedWidthSet = true\n        } else {\n          // The following code is needed to overcome problems with MSIE\n          const bw = this.getBoxWidths()\n          this.div.style.width = this.div.offsetWidth - bw.left - bw.right + 'px'\n          this.fixedWidthSet = false\n        }\n      }\n\n      this.panBox(this.disableAutoPan)\n\n      if (!this.enableEventPropagation) {\n        this.eventListeners = []\n\n        // Cancel event propagation.\n        // Note: mousemove not included (to resolve Issue 152)\n        const events = [\n          'mousedown',\n          'mouseover',\n          'mouseout',\n          'mouseup',\n          'click',\n          'dblclick',\n          'touchstart',\n          'touchend',\n          'touchmove',\n        ]\n\n        for (const event of events) {\n          this.eventListeners.push(\n            google.maps.event.addListener(this.div, event, cancelHandler)\n          )\n        }\n\n        // Workaround for Google bug that causes the cursor to change to a pointer\n        // when the mouse moves over a marker underneath InfoBox.\n        this.eventListeners.push(\n          google.maps.event.addListener(\n            this.div,\n            'mouseover',\n            () => {\n              if (this.div) {\n                this.div.style.cursor = 'default'\n              }\n            }\n          )\n        )\n      }\n\n      this.contextListener = google.maps.event.addListener(\n        this.div,\n        'contextmenu',\n        ignoreHandler\n      )\n\n      /**\n       * This event is fired when the DIV containing the InfoBox's content is attached to the DOM.\n       * @name InfoBox#domready\n       * @event\n       */\n      google.maps.event.trigger(this, 'domready')\n    }\n  }\n\n  getCloseBoxImg(): string {\n    let img = ''\n\n    if (this.closeBoxURL !== '') {\n      img = '<img alt=\"\"'\n      img += ' aria-hidden=\"true\"'\n      img += \" src='\" + this.closeBoxURL + \"'\"\n      img += ' align=right' // Do this because Opera chokes on style='float: right;'\n      img += \" style='\"\n      img += ' position: relative;' // Required by MSIE\n      img += ' cursor: pointer;'\n      img += ' margin: ' + this.closeBoxMargin + ';'\n      img += \"'>\"\n    }\n\n    return img\n  }\n\n  addClickHandler(): void {\n    this.closeListener = this.div && this.div.firstChild && this.closeBoxURL !== ''\n      ? google.maps.event.addListener(\n        this.div.firstChild,\n        'click',\n        this.getCloseClickHandler()\n      )\n      : null;\n  }\n\n  closeClickHandler(event: Event): void {\n    // 1.0.3 fix: Always prevent propagation of a close box click to the map:\n    event.cancelBubble = true\n\n    if (event.stopPropagation) {\n      event.stopPropagation()\n    }\n\n    /**\n     * This event is fired when the InfoBox's close box is clicked.\n     * @name InfoBox#closeclick\n     * @event\n     */\n    google.maps.event.trigger(this, 'closeclick')\n\n    this.close()\n  }\n\n  getCloseClickHandler(): (event: Event) => void {\n    return this.closeClickHandler\n  }\n\n  panBox(disablePan?: boolean | undefined): void {\n    if (this.div && !disablePan) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      const map: google.maps.Map | google.maps.StreetViewPanorama | null | undefined = this.getMap()\n\n      // Only pan if attached to map, not panorama\n      if (map instanceof google.maps.Map) {\n        let xOffset = 0\n        let yOffset = 0\n\n        const bounds = map.getBounds()\n        if (bounds && !bounds.contains(this.position)) {\n          // Marker not in visible area of map, so set center\n          // of map to the marker position first.\n          map.setCenter(this.position)\n        }\n\n        const mapDiv = map.getDiv()\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        const mapWidth = mapDiv.offsetWidth\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        const mapHeight = mapDiv.offsetHeight\n        const iwOffsetX = this.pixelOffset.width\n        const iwOffsetY = this.pixelOffset.height\n        const iwWidth = this.div.offsetWidth\n        const iwHeight = this.div.offsetHeight\n        const padX = this.infoBoxClearance.width\n        const padY = this.infoBoxClearance.height\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        const projection: google.maps.MapCanvasProjection = this.getProjection()\n        const pixPosition = projection.fromLatLngToContainerPixel(this.position)\n\n        if (pixPosition !== null) {\n          if (pixPosition.x < -iwOffsetX + padX) {\n            xOffset = pixPosition.x + iwOffsetX - padX\n          } else if (pixPosition.x + iwWidth + iwOffsetX + padX > mapWidth) {\n            xOffset = pixPosition.x + iwWidth + iwOffsetX + padX - mapWidth\n          }\n\n          if (this.alignBottom) {\n            if (pixPosition.y < -iwOffsetY + padY + iwHeight) {\n              yOffset = pixPosition.y + iwOffsetY - padY - iwHeight\n            } else if (pixPosition.y + iwOffsetY + padY > mapHeight) {\n              yOffset = pixPosition.y + iwOffsetY + padY - mapHeight\n            }\n          } else {\n            if (pixPosition.y < -iwOffsetY + padY) {\n              yOffset = pixPosition.y + iwOffsetY - padY\n            } else if (pixPosition.y + iwHeight + iwOffsetY + padY > mapHeight) {\n              yOffset = pixPosition.y + iwHeight + iwOffsetY + padY - mapHeight\n            }\n          }\n        }\n\n        if (!(xOffset === 0 && yOffset === 0)) {\n          // Move the map to the shifted center.\n          map.panBy(xOffset, yOffset)\n        }\n      }\n    }\n  }\n\n  setBoxStyle(): void {\n    if (this.div) {\n      // Apply style values from the style sheet defined in the boxClass parameter:\n      this.div.className = this.boxClass\n\n      // Clear existing inline style values:\n      this.div.style.cssText = ''\n\n      // Apply style values defined in the boxStyle parameter:\n      const boxStyle: Partial<CSSStyleDeclaration> = this.boxStyle\n\n      for (const i in boxStyle) {\n\n        if (Object.prototype.hasOwnProperty.call(boxStyle, i)) {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          this.div.style[i] = boxStyle[i]\n        }\n      }\n\n      // Fix for iOS disappearing InfoBox problem\n      // See http://stackoverflow.com/questions/9229535/google-maps-markers-disappear-at-certain-zoom-level-only-on-iphone-ipad\n      this.div.style.webkitTransform = 'translateZ(0)'\n\n      // Fix up opacity style for benefit of MSIE\n      if (typeof this.div.style.opacity !== 'undefined' && this.div.style.opacity !== '') {\n        // See http://www.quirksmode.org/css/opacity.html\n        const opacity = parseFloat(this.div.style.opacity || '')\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        this.div.style.msFilter =\n          '\"progid:DXImageTransform.Microsoft.Alpha(Opacity=' + opacity * 100 + ')\"'\n        this.div.style.filter = 'alpha(opacity=' + opacity * 100 + ')'\n      }\n\n      // Apply required styles\n      this.div.style.position = 'absolute'\n      this.div.style.visibility = 'hidden'\n      if (this.zIndex !== null) {\n        this.div.style.zIndex = this.zIndex + ''\n      }\n      if (!this.div.style.overflow) {\n        this.div.style.overflow = 'auto'\n      }\n    }\n  }\n\n  getBoxWidths(): { bottom: number; left: number; right: number; top: number } {\n    const bw = { top: 0, bottom: 0, left: 0, right: 0 }\n\n    if (!this.div) {\n      return bw\n    }\n\n    if (document.defaultView) {\n      const ownerDocument = this.div.ownerDocument\n      const computedStyle =\n        ownerDocument && ownerDocument.defaultView\n          ? ownerDocument.defaultView.getComputedStyle(this.div, '')\n          : null\n\n      if (computedStyle) {\n        // The computed styles are always in pixel units (good!)\n        bw.top = parseInt(computedStyle.borderTopWidth || '', 10) || 0\n        bw.bottom = parseInt(computedStyle.borderBottomWidth || '', 10) || 0\n        bw.left = parseInt(computedStyle.borderLeftWidth || '', 10) || 0\n        bw.right = parseInt(computedStyle.borderRightWidth || '', 10) || 0\n      }\n    } else if (\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      document.documentElement.currentStyle // MSIE\n    ) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      const currentStyle = this.div.currentStyle\n\n      if (currentStyle) {\n        // The current styles may not be in pixel units, but assume they are (bad!)\n        bw.top = parseInt(currentStyle.borderTopWidth || '', 10) || 0\n        bw.bottom = parseInt(currentStyle.borderBottomWidth || '', 10) || 0\n        bw.left = parseInt(currentStyle.borderLeftWidth || '', 10) || 0\n        bw.right = parseInt(currentStyle.borderRightWidth || '', 10) || 0\n      }\n    }\n\n    return bw\n  }\n\n  onRemove(): void {\n    if (this.div && this.div.parentNode) {\n      this.div.parentNode.removeChild(this.div)\n      this.div = null\n    }\n  }\n\n  draw(): void {\n    this.createInfoBoxDiv()\n\n    if (this.div) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      const projection: google.maps.MapCanvasProjection = this.getProjection()\n\n      const pixPosition = projection.fromLatLngToDivPixel(this.position)\n\n      if (pixPosition !== null) {\n        this.div.style.left = pixPosition.x + this.pixelOffset.width + 'px'\n\n        if (this.alignBottom) {\n          this.div.style.bottom = -(pixPosition.y + this.pixelOffset.height) + 'px'\n        } else {\n          this.div.style.top = pixPosition.y + this.pixelOffset.height + 'px'\n        }\n      }\n\n      if (this.isHidden) {\n        this.div.style.visibility = 'hidden'\n      } else {\n        this.div.style.visibility = 'visible'\n      }\n    }\n  }\n\n  setOptions(options: InfoBoxOptions = {}): void {\n    if (typeof options.boxClass !== 'undefined') {\n      // Must be first\n      this.boxClass = options.boxClass\n      this.setBoxStyle()\n    }\n    if (typeof options.boxStyle !== 'undefined') {\n      // Must be second\n      this.boxStyle = options.boxStyle\n      this.setBoxStyle()\n    }\n    if (typeof options.content !== 'undefined') {\n      this.setContent(options.content)\n    }\n    if (typeof options.disableAutoPan !== 'undefined') {\n      this.disableAutoPan = options.disableAutoPan\n    }\n    if (typeof options.maxWidth !== 'undefined') {\n      this.maxWidth = options.maxWidth\n    }\n    if (typeof options.pixelOffset !== 'undefined') {\n      this.pixelOffset = options.pixelOffset\n    }\n    if (typeof options.alignBottom !== 'undefined') {\n      this.alignBottom = options.alignBottom\n    }\n    if (typeof options.position !== 'undefined') {\n      this.setPosition(options.position)\n    }\n    if (typeof options.zIndex !== 'undefined') {\n      this.setZIndex(options.zIndex)\n    }\n    if (typeof options.closeBoxMargin !== 'undefined') {\n      this.closeBoxMargin = options.closeBoxMargin\n    }\n    if (typeof options.closeBoxURL !== 'undefined') {\n      this.closeBoxURL = options.closeBoxURL\n    }\n    if (typeof options.infoBoxClearance !== 'undefined') {\n      this.infoBoxClearance = options.infoBoxClearance\n    }\n    if (typeof options.isHidden !== 'undefined') {\n      this.isHidden = options.isHidden\n    }\n    if (typeof options.visible !== 'undefined') {\n      this.isHidden = !options.visible\n    }\n    if (typeof options.enableEventPropagation !== 'undefined') {\n      this.enableEventPropagation = options.enableEventPropagation\n    }\n\n    if (this.div) {\n      this.draw()\n    }\n  }\n\n  setContent(content: string | Node): void {\n    this.content = content\n\n    if (this.div) {\n      if (this.closeListener) {\n        google.maps.event.removeListener(this.closeListener)\n        this.closeListener = null\n      }\n\n      // Odd code required to make things work with MSIE.\n      if (!this.fixedWidthSet) {\n        this.div.style.width = ''\n      }\n\n      if (typeof content === 'string') {\n        this.div.innerHTML = this.getCloseBoxImg() + content\n      } else {\n        this.div.innerHTML = this.getCloseBoxImg()\n        this.div.appendChild(content)\n      }\n\n      // Perverse code required to make things work with MSIE.\n      // (Ensures the close box does, in fact, float to the right.)\n      if (!this.fixedWidthSet) {\n        this.div.style.width = this.div.offsetWidth + 'px'\n        if (typeof content === 'string') {\n          this.div.innerHTML = this.getCloseBoxImg() + content\n        } else {\n          this.div.innerHTML = this.getCloseBoxImg()\n          this.div.appendChild(content)\n        }\n      }\n\n      this.addClickHandler()\n    }\n\n    /**\n     * This event is fired when the content of the InfoBox changes.\n     * @name InfoBox#content_changed\n     * @event\n     */\n    google.maps.event.trigger(this, 'content_changed')\n  }\n\n  setPosition(latLng: google.maps.LatLng): void {\n    this.position = latLng\n\n    if (this.div) {\n      this.draw()\n    }\n\n    /**\n     * This event is fired when the position of the InfoBox changes.\n     * @name InfoBox#position_changed\n     * @event\n     */\n    google.maps.event.trigger(this, 'position_changed')\n  }\n\n  setVisible(isVisible: boolean): void {\n    this.isHidden = !isVisible\n\n    if (this.div) {\n      this.div.style.visibility = this.isHidden ? 'hidden' : 'visible'\n    }\n  }\n\n  setZIndex(index: number): void {\n    this.zIndex = index\n\n    if (this.div) {\n      this.div.style.zIndex = index + ''\n    }\n\n    /**\n     * This event is fired when the zIndex of the InfoBox changes.\n     * @name InfoBox#zindex_changed\n     * @event\n     */\n    google.maps.event.trigger(this, 'zindex_changed')\n  }\n\n  getContent(): string | Node {\n    return this.content\n  }\n\n  getPosition(): google.maps.LatLng {\n    return this.position\n  }\n\n  getZIndex(): number | null | undefined {\n    return this.zIndex\n  }\n\n  getVisible(): boolean {\n    const map: google.maps.Map | google.maps.StreetViewPanorama | null | undefined = (this as unknown as google.maps.OverlayView).getMap()\n\n    return typeof map === 'undefined' || map === null ? false : !this.isHidden\n  }\n\n  show(): void {\n    this.isHidden = false\n\n    if (this.div) {\n      this.div.style.visibility = 'visible'\n    }\n  }\n\n  hide(): void {\n    this.isHidden = true\n\n    if (this.div) {\n      this.div.style.visibility = 'hidden'\n    }\n  }\n\n  open(\n    map: google.maps.Map | google.maps.StreetViewPanorama,\n    anchor?: google.maps.MVCObject | undefined\n  ): void {\n    if (anchor) {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      this.position = anchor.getPosition()\n\n      this.moveListener = google.maps.event.addListener(\n        anchor,\n        'position_changed',\n        () => {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          const position = anchor.getPosition()\n\n          this.setPosition(position)\n        }\n      )\n\n      this.mapListener = google.maps.event.addListener(\n        anchor,\n        'map_changed',\n        () => {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          this.setMap(anchor.map)\n        }\n      )\n    }\n\n    (this as unknown as google.maps.OverlayView).setMap(map)\n\n    if (this.div) {\n      this.panBox()\n    }\n  }\n\n  close() {\n    if (this.closeListener) {\n      google.maps.event.removeListener(this.closeListener)\n\n      this.closeListener = null\n    }\n\n    if (this.eventListeners) {\n      for (const eventListener of this.eventListeners) {\n        google.maps.event.removeListener(eventListener)\n      }\n\n      this.eventListeners = null\n    }\n\n    if (this.moveListener) {\n      google.maps.event.removeListener(this.moveListener)\n\n      this.moveListener = null\n    }\n\n    if (this.mapListener) {\n      google.maps.event.removeListener(this.mapListener)\n\n      this.mapListener = null\n    }\n\n    if (this.contextListener) {\n      google.maps.event.removeListener(this.contextListener)\n\n      this.contextListener = null\n    }\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    this.setMap(null)\n  }\n\n  extend<A extends typeof InfoBox>(obj1: A, obj2: typeof google.maps.OverlayView): A {\n    return function applyExtend(this: A, object: typeof google.maps.OverlayView): A {\n      for (const property in object.prototype) {\n        if (!Object.prototype.hasOwnProperty.call(this, property)) {\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          this.prototype[property] = object.prototype[property  as keyof google.maps.OverlayView]\n        }\n      }\n\n      return this\n    }.apply(obj1, [obj2])\n  }\n}\n"], "names": [], "mappings": "AAIA;AACA,SAAS,aAAa,CAAC,KAAY,EAAA;AACjC,IAAA,KAAK,CAAC,YAAY,GAAG,IAAI,CAAA;AAEzB,IAAA,IAAI,KAAK,CAAC,eAAe,EAAE;QACzB,KAAK,CAAC,eAAe,EAAE,CAAA;KACxB;AACH,CAAC;AAED,IAAA,OAAA,kBAAA,YAAA;AAyBE,IAAA,SAAA,OAAA,CAAY,OAA4B,EAAA;AAA5B,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA4B,GAAA,EAAA,CAAA,EAAA;QACtC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEhC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;;QAG7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAA;QACpC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,KAAK,CAAA;QACrD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAA;AACrC,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACpE,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAChE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAA;;QAGpC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,SAAS,CAAA;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAkC,CAAA;QACtE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,KAAK,CAAA;QACrD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,qDAAqD,CAAA;AAC/F,QAAA,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;AAC9B,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAA;SACtB;AACD,QAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAE9E,QAAA,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE;AAC1C,YAAA,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW,EAAE;AAC3C,gBAAA,OAAO,CAAC,OAAO,GAAG,IAAI,CAAA;aACvB;iBAAM;AACL,gBAAA,OAAO,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAA;aACpC;SACF;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAA;QAEhC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,KAAK,CAAA;QAC/C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,WAAW,CAAA;QACvC,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,IAAI,KAAK,CAAA;AAErE,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;AACf,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;AACvB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;AAC3B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;KAC1B;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;QAAA,IAqGC,KAAA,GAAA,IAAA,CAAA;;;QAlGC,IAAM,aAAa,GAAG,UAAC,KAAY,EAAA;AACjC,YAAA,KAAK,CAAC,WAAW,GAAG,KAAK,CAAA;AAEzB,YAAA,IAAI,KAAK,CAAC,cAAc,EAAE;gBACxB,KAAK,CAAC,cAAc,EAAE,CAAA;aACvB;AAED,YAAA,IAAI,CAAC,KAAI,CAAC,sBAAsB,EAAE;gBAChC,aAAa,CAAC,KAAK,CAAC,CAAA;aACrB;AACH,SAAC,CAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACb,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,CAAC,WAAW,EAAE,CAAA;AAElB,YAAA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE;AACpC,gBAAA,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;aAC1D;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;gBAC1C,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;aACnC;AAED,YAAA,IAAM,KAAK,GAAI,IAA2C,CAAC,QAAQ,EAAE,CAAA;AAErE,YAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,gBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aACvC;YAED,IAAI,CAAC,eAAe,EAAE,CAAA;YAEtB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE;AACxB,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;aAC1B;iBAAM;AACL,gBAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE;AAC/D,oBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;AAC3C,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;iBAC1B;qBAAM;;AAEL,oBAAA,IAAM,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;oBAC9B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,GAAG,IAAI,CAAA;AACvE,oBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;iBAC3B;aACF;AAED,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;AAEhC,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAChC,gBAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;;;AAIxB,gBAAA,IAAM,MAAM,GAAG;oBACb,WAAW;oBACX,WAAW;oBACX,UAAU;oBACV,SAAS;oBACT,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,UAAU;oBACV,WAAW;iBACZ,CAAA;gBAED,KAAoB,IAAA,EAAA,GAAA,CAAM,EAAN,QAAM,GAAA,MAAA,EAAN,oBAAM,EAAN,EAAA,EAAM,EAAE;AAAvB,oBAAA,IAAM,OAAK,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA;oBACd,IAAI,CAAC,cAAc,CAAC,IAAI,CACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,OAAK,EAAE,aAAa,CAAC,CAC9D,CAAA;iBACF;;;AAID,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC3B,IAAI,CAAC,GAAG,EACR,WAAW,EACX,YAAA;AACE,oBAAA,IAAI,KAAI,CAAC,GAAG,EAAE;wBACZ,KAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAA;qBAClC;iBACF,CACF,CACF,CAAA;aACF;AAED,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAClD,IAAI,CAAC,GAAG,EACR,aAAa,EACb,aAAa,CACd,CAAA;AAED;;;;AAIG;YACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;SAC5C;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACE,IAAI,GAAG,GAAG,EAAE,CAAA;AAEZ,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,EAAE,EAAE;YAC3B,GAAG,GAAG,aAAa,CAAA;YACnB,GAAG,IAAI,qBAAqB,CAAA;YAC5B,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,CAAA;AACxC,YAAA,GAAG,IAAI,cAAc,CAAA;YACrB,GAAG,IAAI,UAAU,CAAA;AACjB,YAAA,GAAG,IAAI,sBAAsB,CAAA;YAC7B,GAAG,IAAI,mBAAmB,CAAA;YAC1B,GAAG,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG,GAAG,CAAA;YAC9C,GAAG,IAAI,IAAI,CAAA;SACZ;AAED,QAAA,OAAO,GAAG,CAAA;KACX,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AACE,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,KAAK,EAAE;cAC3E,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC7B,IAAI,CAAC,GAAG,CAAC,UAAU,EACnB,OAAO,EACP,IAAI,CAAC,oBAAoB,EAAE,CAC5B;cACC,IAAI,CAAC;KACV,CAAA;IAED,OAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,KAAY,EAAA;;AAE5B,QAAA,KAAK,CAAC,YAAY,GAAG,IAAI,CAAA;AAEzB,QAAA,IAAI,KAAK,CAAC,eAAe,EAAE;YACzB,KAAK,CAAC,eAAe,EAAE,CAAA;SACxB;AAED;;;;AAIG;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,CAAA;QAE7C,IAAI,CAAC,KAAK,EAAE,CAAA;KACb,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;QACE,OAAO,IAAI,CAAC,iBAAiB,CAAA;KAC9B,CAAA;IAED,OAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,UAAgC,EAAA;AACrC,QAAA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;;;AAG3B,YAAA,IAAM,GAAG,GAAwE,IAAI,CAAC,MAAM,EAAE,CAAA;;YAG9F,IAAI,GAAG,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClC,IAAI,OAAO,GAAG,CAAC,CAAA;gBACf,IAAI,OAAO,GAAG,CAAC,CAAA;AAEf,gBAAA,IAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAA;AAC9B,gBAAA,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;;AAG7C,oBAAA,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;iBAC7B;AAED,gBAAA,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAA;;;AAG3B,gBAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAA;;;AAGnC,gBAAA,IAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAA;AACrC,gBAAA,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAA;AACxC,gBAAA,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;AACzC,gBAAA,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAA;AACpC,gBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAA;AACtC,gBAAA,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAA;AACxC,gBAAA,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAA;;;AAIzC,gBAAA,IAAM,UAAU,GAAoC,IAAI,CAAC,aAAa,EAAE,CAAA;gBACxE,IAAM,WAAW,GAAG,UAAU,CAAC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AAExE,gBAAA,IAAI,WAAW,KAAK,IAAI,EAAE;oBACxB,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,EAAE;wBACrC,OAAO,GAAG,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAA;qBAC3C;AAAM,yBAAA,IAAI,WAAW,CAAC,CAAC,GAAG,OAAO,GAAG,SAAS,GAAG,IAAI,GAAG,QAAQ,EAAE;AAChE,wBAAA,OAAO,GAAG,WAAW,CAAC,CAAC,GAAG,OAAO,GAAG,SAAS,GAAG,IAAI,GAAG,QAAQ,CAAA;qBAChE;AAED,oBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;wBACpB,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,QAAQ,EAAE;4BAChD,OAAO,GAAG,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,QAAQ,CAAA;yBACtD;6BAAM,IAAI,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,SAAS,EAAE;4BACvD,OAAO,GAAG,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,SAAS,CAAA;yBACvD;qBACF;yBAAM;wBACL,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,EAAE;4BACrC,OAAO,GAAG,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAA;yBAC3C;AAAM,6BAAA,IAAI,WAAW,CAAC,CAAC,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI,GAAG,SAAS,EAAE;AAClE,4BAAA,OAAO,GAAG,WAAW,CAAC,CAAC,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI,GAAG,SAAS,CAAA;yBAClE;qBACF;iBACF;gBAED,IAAI,EAAE,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;;AAErC,oBAAA,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;iBAC5B;aACF;SACF;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;AACE,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;;YAEZ,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAA;;YAGlC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAA;;AAG3B,YAAA,IAAM,QAAQ,GAAiC,IAAI,CAAC,QAAQ,CAAA;AAE5D,YAAA,KAAK,IAAM,CAAC,IAAI,QAAQ,EAAE;AAExB,gBAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE;;;AAGrD,oBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;iBAChC;aACF;;;YAID,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,eAAe,CAAA;;YAGhD,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,KAAK,EAAE,EAAE;;AAElF,gBAAA,IAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;;;AAIxD,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ;AACrB,oBAAA,mDAAmD,GAAG,OAAO,GAAG,GAAG,GAAG,IAAI,CAAA;AAC5E,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,gBAAgB,GAAG,OAAO,GAAG,GAAG,GAAG,GAAG,CAAA;aAC/D;;YAGD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAA;YACpC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAA;AACpC,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;AACxB,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;aACzC;YACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAA;aACjC;SACF;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;AACE,QAAA,IAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;AAEnD,QAAA,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;AACb,YAAA,OAAO,EAAE,CAAA;SACV;AAED,QAAA,IAAI,QAAQ,CAAC,WAAW,EAAE;AACxB,YAAA,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAA;AAC5C,YAAA,IAAM,aAAa,GACjB,aAAa,IAAI,aAAa,CAAC,WAAW;AACxC,kBAAE,aAAa,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;kBACxD,IAAI,CAAA;YAEV,IAAI,aAAa,EAAE;;AAEjB,gBAAA,EAAE,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;AAC9D,gBAAA,EAAE,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,iBAAiB,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;AACpE,gBAAA,EAAE,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,eAAe,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;AAChE,gBAAA,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,gBAAgB,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;aACnE;SACF;AAAM,aAAA;;;AAGL,QAAA,QAAQ,CAAC,eAAe,CAAC,YAAY;UACrC;;;AAGA,YAAA,IAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAA;YAE1C,IAAI,YAAY,EAAE;;AAEhB,gBAAA,EAAE,CAAC,GAAG,GAAG,QAAQ,CAAC,YAAY,CAAC,cAAc,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;AAC7D,gBAAA,EAAE,CAAC,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;AACnE,gBAAA,EAAE,CAAC,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,eAAe,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;AAC/D,gBAAA,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,gBAAgB,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;aAClE;SACF;AAED,QAAA,OAAO,EAAE,CAAA;KACV,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QACE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;YACnC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACzC,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;SAChB;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QACE,IAAI,CAAC,gBAAgB,EAAE,CAAA;AAEvB,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;;;AAGZ,YAAA,IAAM,UAAU,GAAoC,IAAI,CAAC,aAAa,EAAE,CAAA;YAExE,IAAM,WAAW,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AAElE,YAAA,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAA;AAEnE,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;iBAC1E;qBAAM;AACL,oBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAA;iBACpE;aACF;AAED,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAA;aACrC;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAA;aACtC;SACF;KACF,CAAA;IAED,OAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,OAA4B,EAAA;AAA5B,QAAA,IAAA,OAAA,KAAA,KAAA,CAAA,EAAA,EAAA,OAA4B,GAAA,EAAA,CAAA,EAAA;AACrC,QAAA,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW,EAAE;;AAE3C,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;YAChC,IAAI,CAAC,WAAW,EAAE,CAAA;SACnB;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW,EAAE;;AAE3C,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;YAChC,IAAI,CAAC,WAAW,EAAE,CAAA;SACnB;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE;AAC1C,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;SACjC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,WAAW,EAAE;AACjD,YAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;SAC7C;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW,EAAE;AAC3C,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;SACjC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,WAAW,EAAE;AAC9C,YAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;SACvC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,WAAW,EAAE;AAC9C,YAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;SACvC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW,EAAE;AAC3C,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;SACnC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE;AACzC,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;SAC/B;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,WAAW,EAAE;AACjD,YAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;SAC7C;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,WAAW,EAAE;AAC9C,YAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;SACvC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,gBAAgB,KAAK,WAAW,EAAE;AACnD,YAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;SACjD;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW,EAAE;AAC3C,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;SACjC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE;AAC1C,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAA;SACjC;AACD,QAAA,IAAI,OAAO,OAAO,CAAC,sBAAsB,KAAK,WAAW,EAAE;AACzD,YAAA,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAA;SAC7D;AAED,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,IAAI,EAAE,CAAA;SACZ;KACF,CAAA;IAED,OAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,OAAsB,EAAA;AAC/B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;AAEtB,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;AACZ,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;AACpD,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;aAC1B;;AAGD,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACvB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;aAC1B;AAED,YAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBAC/B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,CAAA;aACrD;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;AAC1C,gBAAA,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;aAC9B;;;AAID,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACvB,gBAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAA;AAClD,gBAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;oBAC/B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,CAAA;iBACrD;qBAAM;oBACL,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;AAC1C,oBAAA,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;iBAC9B;aACF;YAED,IAAI,CAAC,eAAe,EAAE,CAAA;SACvB;AAED;;;;AAIG;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;KACnD,CAAA;IAED,OAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,MAA0B,EAAA;AACpC,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAA;AAEtB,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,IAAI,EAAE,CAAA;SACZ;AAED;;;;AAIG;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;KACpD,CAAA;IAED,OAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,SAAkB,EAAA;AAC3B,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,CAAA;AAE1B,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;AACZ,YAAA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;SACjE;KACF,CAAA;IAED,OAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,KAAa,EAAA;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;AAEnB,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,EAAE,CAAA;SACnC;AAED;;;;AAIG;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAA;KAClD,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QACE,OAAO,IAAI,CAAC,OAAO,CAAA;KACpB,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACE,OAAO,IAAI,CAAC,QAAQ,CAAA;KACrB,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;QACE,OAAO,IAAI,CAAC,MAAM,CAAA;KACnB,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;AACE,QAAA,IAAM,GAAG,GAAyE,IAA2C,CAAC,MAAM,EAAE,CAAA;AAEtI,QAAA,OAAO,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAA;KAC3E,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AACE,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;AAErB,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAA;SACtC;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AACE,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;AAEpB,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAA;SACrC;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UACE,GAAqD,EACrD,MAA0C,EAAA;QAF5C,IAqCC,KAAA,GAAA,IAAA,CAAA;QAjCC,IAAI,MAAM,EAAE;;;AAGV,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;AAEpC,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC/C,MAAM,EACN,kBAAkB,EAClB,YAAA;;;AAGE,gBAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;AAErC,gBAAA,KAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;AAC5B,aAAC,CACF,CAAA;AAED,YAAA,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC9C,MAAM,EACN,aAAa,EACb,YAAA;;;AAGE,gBAAA,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AACzB,aAAC,CACF,CAAA;SACF;AAEA,QAAA,IAA2C,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AAExD,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,MAAM,EAAE,CAAA;SACd;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;AAEpD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;SAC1B;AAED,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,KAA4B,IAAA,EAAA,GAAA,CAAmB,EAAnB,EAAA,GAAA,IAAI,CAAC,cAAc,EAAnB,EAAmB,GAAA,EAAA,CAAA,MAAA,EAAnB,EAAmB,EAAA,EAAE;AAA5C,gBAAA,IAAM,aAAa,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;aAChD;AAED,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;SAC3B;AAED,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;AAEnD,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;SACzB;AAED,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAElD,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;SACxB;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;AAEtD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;SAC5B;;;AAID,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;KAClB,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAiC,IAAO,EAAE,IAAoC,EAAA;QAC5E,OAAO,SAAS,WAAW,CAAU,MAAsC,EAAA;AACzE,YAAA,KAAK,IAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;AACvC,gBAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;;;AAGzD,oBAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,QAA0C,CAAC,CAAA;iBACxF;aACF;AAED,YAAA,OAAO,IAAI,CAAA;SACZ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;KACtB,CAAA;IACH,OAAC,OAAA,CAAA;AAAD,CAAC,EAAA;;;;"}