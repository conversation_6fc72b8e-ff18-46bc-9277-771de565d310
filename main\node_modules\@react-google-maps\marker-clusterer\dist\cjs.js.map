{"version": 3, "file": "cjs.js", "sources": ["../src/ClusterIcon.tsx", "../src/Cluster.tsx", "../src/Clusterer.tsx"], "sourcesContent": ["/* global google */\n/* eslint-disable filenames/match-regex */\nimport type { Cluster } from './Cluster'\n\nimport type { ClusterIconStyle, ClusterIconInfo } from './types'\n\nexport class ClusterIcon {\n  cluster: Cluster\n  className: string\n  clusterClassName: string\n  styles: ClusterIconStyle[]\n  center: google.maps.LatLng | undefined\n  div: HTMLDivElement | null\n  sums: ClusterIconInfo | null\n  visible: boolean\n  url: string\n  height: number\n  width: number\n  anchorText: [number, number]\n  anchorIcon: [number, number]\n  textColor: string\n  textSize: number\n  textDecoration: string\n  fontWeight: string\n  fontStyle: string\n  fontFamily: string\n  backgroundPosition: string\n  cMouseDownInCluster: boolean | null\n  cDraggingMapByCluster: boolean | null\n  timeOut: number | null\n\n  boundsChangedListener: google.maps.MapsEventListener | null\n\n  constructor(cluster: Cluster, styles: ClusterIconStyle[]) {\n    cluster.getClusterer().extend(ClusterIcon, google.maps.OverlayView)\n\n    this.cluster = cluster\n\n    this.clusterClassName = this.cluster.getClusterer().getClusterClass()\n\n    this.className = this.clusterClassName\n\n    this.styles = styles\n\n    this.center = undefined\n\n    this.div = null\n\n    this.sums = null\n\n    this.visible = false\n\n    this.boundsChangedListener = null\n\n    this.url = ''\n\n    this.height = 0\n    this.width = 0\n\n    this.anchorText = [0, 0]\n    this.anchorIcon = [0, 0]\n\n    this.textColor = 'black'\n    this.textSize = 11\n    this.textDecoration = 'none'\n    this.fontWeight = 'bold'\n    this.fontStyle = 'normal'\n    this.fontFamily = 'Arial,sans-serif'\n\n    this.backgroundPosition = '0 0'\n\n    this.cMouseDownInCluster = null\n    this.cDraggingMapByCluster = null\n    this.timeOut = null;\n\n    (this as unknown as google.maps.OverlayView).setMap(cluster.getMap()) // Note: this causes onAdd to be called\n\n    this.onBoundsChanged = this.onBoundsChanged.bind(this)\n    this.onMouseDown = this.onMouseDown.bind(this)\n    this.onClick = this.onClick.bind(this)\n    this.onMouseOver = this.onMouseOver.bind(this)\n    this.onMouseOut = this.onMouseOut.bind(this)\n    this.onAdd = this.onAdd.bind(this)\n    this.onRemove = this.onRemove.bind(this)\n    this.draw = this.draw.bind(this)\n    this.hide = this.hide.bind(this)\n    this.show = this.show.bind(this)\n    this.useStyle = this.useStyle.bind(this)\n    this.setCenter = this.setCenter.bind(this)\n    this.getPosFromLatLng = this.getPosFromLatLng.bind(this)\n  }\n\n  onBoundsChanged() {\n    this.cDraggingMapByCluster = this.cMouseDownInCluster\n  }\n\n  onMouseDown() {\n    this.cMouseDownInCluster = true\n\n    this.cDraggingMapByCluster = false\n  }\n\n  onClick(event: Event) {\n    this.cMouseDownInCluster = false\n\n    if (!this.cDraggingMapByCluster) {\n      const markerClusterer = this.cluster.getClusterer()\n\n      /**\n       * This event is fired when a cluster marker is clicked.\n       * @name MarkerClusterer#click\n       * @param {Cluster} c The cluster that was clicked.\n       * @event\n       */\n      google.maps.event.trigger(markerClusterer, 'click', this.cluster)\n      google.maps.event.trigger(markerClusterer, 'clusterclick', this.cluster) // deprecated name\n\n      // The default click handler follows. Disable it by setting\n      // the zoomOnClick property to false.\n      if (markerClusterer.getZoomOnClick()) {\n        // Zoom into the cluster.\n        const maxZoom = markerClusterer.getMaxZoom()\n\n        const bounds = this.cluster.getBounds()\n\n        const map = (markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n        if (map !== null && 'fitBounds' in map) {\n          map.fitBounds(bounds)\n        }\n\n\n        // There is a fix for Issue 170 here:\n        this.timeOut = window.setTimeout(() => {\n          const map = (markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n          if (map !== null) {\n            if ('fitBounds' in map) {\n              map.fitBounds(bounds)\n            }\n\n            const zoom = map.getZoom() || 0\n\n            // Don't zoom beyond the max zoom level\n            if (\n              maxZoom !== null &&\n              zoom > maxZoom\n            ) {\n              map.setZoom(maxZoom + 1)\n            }\n          }\n        }, 100)\n      }\n\n      // Prevent event propagation to the map:\n      event.cancelBubble = true\n\n      if (event.stopPropagation) {\n        event.stopPropagation()\n      }\n    }\n  }\n\n  onMouseOver() {\n    /**\n     * This event is fired when the mouse moves over a cluster marker.\n     * @name MarkerClusterer#mouseover\n     * @param {Cluster} c The cluster that the mouse moved over.\n     * @event\n     */\n    google.maps.event.trigger(\n      this.cluster.getClusterer(),\n      'mouseover',\n      this.cluster\n    )\n  }\n\n  onMouseOut() {\n    /**\n     * This event is fired when the mouse moves out of a cluster marker.\n     * @name MarkerClusterer#mouseout\n     * @param {Cluster} c The cluster that the mouse moved out of.\n     * @event\n     */\n    google.maps.event.trigger(\n      this.cluster.getClusterer(),\n      'mouseout',\n      this.cluster\n    )\n  }\n\n  onAdd() {\n    this.div = document.createElement('div')\n\n    this.div.className = this.className\n\n    if (this.visible) {\n      this.show()\n    }\n\n    ;(this as unknown as google.maps.OverlayView).getPanes()?.overlayMouseTarget.appendChild(this.div)\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    if (map !== null) {\n      // Fix for Issue 157\n      this.boundsChangedListener = google.maps.event.addListener(\n        map,\n        'bounds_changed',\n        this.onBoundsChanged\n      )\n\n      this.div.addEventListener('mousedown', this.onMouseDown)\n\n      this.div.addEventListener('click', this.onClick)\n\n      this.div.addEventListener('mouseover', this.onMouseOver)\n\n      this.div.addEventListener('mouseout', this.onMouseOut)\n    }\n  }\n\n  onRemove() {\n    if (this.div && this.div.parentNode) {\n      this.hide()\n\n      if (this.boundsChangedListener !== null) {\n        google.maps.event.removeListener(this.boundsChangedListener)\n      }\n\n      this.div.removeEventListener('mousedown', this.onMouseDown)\n\n      this.div.removeEventListener('click', this.onClick)\n\n      this.div.removeEventListener('mouseover', this.onMouseOver)\n\n      this.div.removeEventListener('mouseout', this.onMouseOut)\n\n      this.div.parentNode.removeChild(this.div)\n\n      if (this.timeOut !== null) {\n        window.clearTimeout(this.timeOut)\n\n        this.timeOut = null\n      }\n\n      this.div = null\n    }\n  }\n\n  draw() {\n    if (this.visible && this.div !== null && this.center) {\n      const pos = this.getPosFromLatLng(this.center)\n\n      this.div.style.top = pos !== null ? `${pos.y}px` : '0'\n      this.div.style.left = pos !== null ? `${pos.x}px` : '0'\n    }\n  }\n\n  hide() {\n    if (this.div) {\n      this.div.style.display = 'none'\n    }\n\n    this.visible = false\n  }\n\n  show() {\n    if (this.div && this.center) {\n      const divTitle = this.sums === null ||\n      typeof this.sums.title === 'undefined' ||\n      this.sums.title === '' ? this.cluster.getClusterer().getTitle() :  this.sums.title\n\n      // NOTE: values must be specified in px units\n      const bp = this.backgroundPosition.split(' ')\n\n      const spriteH = parseInt(bp[0]?.replace(/^\\s+|\\s+$/g, '') || '0', 10)\n      const spriteV = parseInt(bp[1]?.replace(/^\\s+|\\s+$/g, '') || '0', 10)\n\n      const pos = this.getPosFromLatLng(this.center)\n\n      this.div.className = this.className\n      this.div .setAttribute('style', `cursor: pointer; position: absolute; top: ${pos !== null ? `${pos.y}px` : '0'}; left: ${pos !== null ? `${pos.x}px` : '0'}; width: ${this.width}px; height: ${this.height}px; `)\n\n      const img = document.createElement('img')\n\n      img.alt = divTitle\n      img.src = this.url\n      img.width = this.width\n      img.height = this.height\n      img.setAttribute('style', `position: absolute; top: ${spriteV}px; left: ${spriteH}px`)\n\n      if (!this.cluster.getClusterer().enableRetinaIcons) {\n        img.style.clip = `rect(-${spriteV}px, -${spriteH + this.width}px, -${\n          spriteV + this.height\n        }, -${spriteH})`\n      }\n\n      const textElm = document.createElement('div')\n\n      textElm .setAttribute('style', `position: absolute; top: ${this.anchorText[0]}px; left: ${this.anchorText[1]}px; color: ${this.textColor}; font-size: ${this.textSize}px; font-family: ${this.fontFamily}; font-weight: ${this.fontWeight}; fontStyle: ${this.fontStyle}; text-decoration: ${this.textDecoration}; text-align: center; width: ${this.width}px; line-height: ${this.height}px`)\n\n      if (this.sums?.text) textElm.innerText = `${this.sums?.text}`\n      if (this.sums?.html) textElm.innerHTML = `${this.sums?.html}`\n\n      this.div.innerHTML = ''\n\n      this.div.appendChild(img)\n      this.div.appendChild(textElm)\n\n      this.div.title = divTitle\n\n      this.div.style.display = ''\n    }\n\n    this.visible = true\n  }\n\n  useStyle(sums: ClusterIconInfo) {\n    this.sums = sums\n\n    const styles = this.cluster.getClusterer().getStyles()\n\n    const style =\n      styles[Math.min(styles.length - 1, Math.max(0, sums.index - 1))]\n\n    if (style) {\n      this.url = style.url\n      this.height = style.height\n      this.width = style.width\n\n      if (style.className) {\n        this.className = `${this.clusterClassName} ${style.className}`\n      }\n\n      this.anchorText = style.anchorText || [0, 0]\n      this.anchorIcon = style.anchorIcon || [this.height / 2, this.width / 2]\n\n      this.textColor = style.textColor || 'black'\n\n      this.textSize = style.textSize || 11\n\n      this.textDecoration = style.textDecoration || 'none'\n\n      this.fontWeight = style.fontWeight || 'bold'\n\n      this.fontStyle = style.fontStyle || 'normal'\n\n      this.fontFamily = style.fontFamily || 'Arial,sans-serif'\n\n      this.backgroundPosition = style.backgroundPosition || '0 0'\n    }\n  }\n\n  setCenter(center: google.maps.LatLng) {\n    this.center = center\n  }\n\n  getPosFromLatLng(latlng: google.maps.LatLng): google.maps.Point | null {\n    const pos = (this as unknown as google.maps.OverlayView).getProjection().fromLatLngToDivPixel(latlng)\n\n    if (pos !== null) {\n      pos.x -= this.anchorIcon[1]\n\n      pos.y -= this.anchorIcon[0]\n    }\n\n    return pos\n  }\n}\n", "/* global google */\n\nimport type { <PERSON>luster<PERSON> } from './Clusterer'\n\nimport { ClusterIcon } from './ClusterIcon'\n\nimport type { MarkerExtended } from './types'\n\nexport class Cluster {\n  markerClusterer: Clusterer\n  map: google.maps.Map | google.maps.StreetViewPanorama | null\n  gridSize: number\n  minClusterSize: number\n  averageCenter: boolean\n  markers: MarkerExtended[]\n  center: google.maps.LatLng | undefined\n  bounds: google.maps.LatLngBounds | null\n  clusterIcon: ClusterIcon\n\n  constructor(markerClusterer: Clusterer) {\n    this.markerClusterer = markerClusterer\n\n    this.map = (this.markerClusterer as unknown as google.maps.OverlayView).getMap()\n\n    this.gridSize = this.markerClusterer.getGridSize()\n\n    this.minClusterSize = this.markerClusterer.getMinimumClusterSize()\n\n    this.averageCenter = this.markerClusterer.getAverageCenter()\n\n    this.markers = []\n\n    this.center = undefined\n\n    this.bounds = null\n\n    this.clusterIcon = new ClusterIcon(this, this.markerClusterer.getStyles())\n\n    this.getSize = this.getSize.bind(this)\n    this.getMarkers = this.getMarkers.bind(this)\n    this.getCenter = this.getCenter.bind(this)\n    this.getMap = this.getMap.bind(this)\n    this.getClusterer = this.getClusterer.bind(this)\n    this.getBounds = this.getBounds.bind(this)\n    this.remove = this.remove.bind(this)\n    this.addMarker = this.addMarker.bind(this)\n    this.isMarkerInClusterBounds = this.isMarkerInClusterBounds.bind(this)\n    this.calculateBounds = this.calculateBounds.bind(this)\n    this.updateIcon = this.updateIcon.bind(this)\n    this.isMarkerAlreadyAdded = this.isMarkerAlreadyAdded.bind(this)\n  }\n\n  getSize(): number {\n    return this.markers.length\n  }\n\n  getMarkers(): MarkerExtended[] {\n    return this.markers\n  }\n\n  getCenter(): google.maps.LatLng | undefined {\n    return this.center\n  }\n\n  getMap(): google.maps.Map | google.maps.StreetViewPanorama | null {\n    return this.map\n  }\n\n  getClusterer(): Clusterer {\n    return this.markerClusterer\n  }\n\n  getBounds(): google.maps.LatLngBounds {\n    const bounds = new google.maps.LatLngBounds(this.center, this.center)\n\n    const markers = this.getMarkers()\n\n    for (const marker of markers) {\n      const position = marker.getPosition()\n\n      if (position) {\n        bounds.extend(position)\n      }\n    }\n\n    return bounds\n  }\n\n  remove() {\n    (this.clusterIcon as unknown as google.maps.OverlayView).setMap(null)\n\n    this.markers = []\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    delete this.markers\n  }\n\n  addMarker(marker: MarkerExtended): boolean {\n    if (this.isMarkerAlreadyAdded(marker)) {\n      return false\n    }\n\n    if (!this.center) {\n      const position = marker.getPosition()\n\n      if (position) {\n        this.center = position\n\n        this.calculateBounds()\n      }\n    } else {\n      if (this.averageCenter) {\n        const position = marker.getPosition()\n\n        if (position) {\n          const length = this.markers.length + 1\n\n          this.center = new google.maps.LatLng(\n            (this.center.lat() * (length - 1) + position.lat()) / length,\n            (this.center.lng() * (length - 1) + position.lng()) / length\n          )\n\n          this.calculateBounds()\n        }\n      }\n    }\n\n    marker.isAdded = true\n\n    this.markers.push(marker)\n\n    const mCount = this.markers.length\n\n    const maxZoom = this.markerClusterer.getMaxZoom()\n\n    const zoom = this.map?.getZoom()\n\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      // Zoomed in past max zoom, so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map)\n      }\n    } else if (mCount < this.minClusterSize) {\n      // Min cluster size not reached so show the marker.\n      if (marker.getMap() !== this.map) {\n        marker.setMap(this.map)\n      }\n    } else if (mCount === this.minClusterSize) {\n      // Hide the markers that were showing.\n      for (const markerElement of this.markers) {\n        markerElement.setMap(null)\n      }\n    } else {\n      marker.setMap(null)\n    }\n\n    return true\n  }\n\n  isMarkerInClusterBounds(marker: MarkerExtended): boolean {\n    if (this.bounds !== null) {\n      const position = marker.getPosition()\n\n      if (position) {\n        return this.bounds.contains(position)\n      }\n    }\n\n    return false\n  }\n\n  calculateBounds() {\n    this.bounds = this.markerClusterer.getExtendedBounds(\n      new google.maps.LatLngBounds(this.center, this.center)\n    )\n  }\n\n  updateIcon() {\n    const mCount = this.markers.length\n\n    const maxZoom = this.markerClusterer.getMaxZoom()\n\n    const zoom = this.map?.getZoom()\n\n    if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {\n      this.clusterIcon.hide()\n\n      return\n    }\n\n    if (mCount < this.minClusterSize) {\n      // Min cluster size not yet reached.\n      this.clusterIcon.hide()\n\n      return\n    }\n\n    if (this.center) {\n      this.clusterIcon.setCenter(this.center)\n    }\n\n    this.clusterIcon.useStyle(\n      this.markerClusterer.getCalculator()(this.markers, this.markerClusterer.getStyles().length)\n    )\n\n    this.clusterIcon.show()\n  }\n\n  isMarkerAlreadyAdded(marker: MarkerExtended): boolean {\n    if (this.markers.includes) {\n      return this.markers.includes(marker)\n    }\n\n    for (let i = 0; i < this.markers.length; i++) {\n      if (marker === this.markers[i]) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n", "/* global google */\n/* eslint-disable filenames/match-regex */\nimport { Cluster } from './Cluster'\nimport type { ClusterIcon } from './ClusterIcon'\n\nimport type {\n  MarkerExtended,\n  ClustererOptions,\n  ClusterIconStyle,\n  TCalculator,\n  ClusterIconInfo,\n} from './types'\n\n/**\n * Supports up to 9007199254740991 (Number.MAX_SAFE_INTEGER) markers\n * which is not a problem as max array length is 4294967296 (2**32)\n */\nfunction CALCULATOR(\n  markers: MarkerExtended[],\n  numStyles: number\n): ClusterIconInfo {\n  const count = markers.length\n\n  const numberOfDigits = count.toString().length\n\n  const index = Math.min(numberOfDigits, numStyles)\n\n  return {\n    text: count.toString(),\n    index,\n    title: '',\n  }\n}\n\nconst BATCH_SIZE = 2000\n\nconst BATCH_SIZE_IE = 500\n\nconst IMAGE_PATH =\n  'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'\n\nconst IMAGE_EXTENSION = 'png'\n\nconst IMAGE_SIZES = [53, 56, 66, 78, 90]\n\nconst CLUSTERER_CLASS = 'cluster'\n\nexport class Clusterer implements google.maps.OverlayView {\n  markers: MarkerExtended[]\n  clusters: Cluster[]\n  listeners: google.maps.MapsEventListener[]\n  activeMap: google.maps.Map | google.maps.StreetViewPanorama | null\n  ready: boolean\n  gridSize: number\n  minClusterSize: number\n  maxZoom: number | null\n  styles: ClusterIconStyle[]\n  title: string\n  zoomOnClick: boolean\n  averageCenter: boolean\n  ignoreHidden: boolean\n  enableRetinaIcons: boolean\n  imagePath: string\n  imageExtension: string\n  imageSizes: number[]\n  calculator: TCalculator\n  batchSize: number\n  batchSizeIE: number\n  clusterClass: string\n  timerRefStatic: number | null\n\n  constructor(\n    map: google.maps.Map,\n    optMarkers: MarkerExtended[] = [],\n    optOptions: ClustererOptions = {}\n  ) {\n    this.getMinimumClusterSize = this.getMinimumClusterSize.bind(this)\n    this.setMinimumClusterSize = this.setMinimumClusterSize.bind(this)\n    this.getEnableRetinaIcons = this.getEnableRetinaIcons.bind(this)\n    this.setEnableRetinaIcons = this.setEnableRetinaIcons.bind(this)\n    this.addToClosestCluster = this.addToClosestCluster.bind(this)\n    this.getImageExtension = this.getImageExtension.bind(this)\n    this.setImageExtension = this.setImageExtension.bind(this)\n    this.getExtendedBounds = this.getExtendedBounds.bind(this)\n    this.getAverageCenter = this.getAverageCenter.bind(this)\n    this.setAverageCenter = this.setAverageCenter.bind(this)\n    this.getTotalClusters = this.getTotalClusters.bind(this)\n    this.fitMapToMarkers = this.fitMapToMarkers.bind(this)\n    this.getIgnoreHidden = this.getIgnoreHidden.bind(this)\n    this.setIgnoreHidden = this.setIgnoreHidden.bind(this)\n    this.getClusterClass = this.getClusterClass.bind(this)\n    this.setClusterClass = this.setClusterClass.bind(this)\n    this.getTotalMarkers = this.getTotalMarkers.bind(this)\n    this.getZoomOnClick = this.getZoomOnClick.bind(this)\n    this.setZoomOnClick = this.setZoomOnClick.bind(this)\n    this.getBatchSizeIE = this.getBatchSizeIE.bind(this)\n    this.setBatchSizeIE = this.setBatchSizeIE.bind(this)\n    this.createClusters = this.createClusters.bind(this)\n    this.onZoomChanged = this.onZoomChanged.bind(this)\n    this.getImageSizes = this.getImageSizes.bind(this)\n    this.setImageSizes = this.setImageSizes.bind(this)\n    this.getCalculator = this.getCalculator.bind(this)\n    this.setCalculator = this.setCalculator.bind(this)\n    this.removeMarkers = this.removeMarkers.bind(this)\n    this.resetViewport = this.resetViewport.bind(this)\n    this.getImagePath = this.getImagePath.bind(this)\n    this.setImagePath = this.setImagePath.bind(this)\n    this.pushMarkerTo = this.pushMarkerTo.bind(this)\n    this.removeMarker = this.removeMarker.bind(this)\n    this.clearMarkers = this.clearMarkers.bind(this)\n    this.setupStyles = this.setupStyles.bind(this)\n    this.getGridSize = this.getGridSize.bind(this)\n    this.setGridSize = this.setGridSize.bind(this)\n    this.getClusters = this.getClusters.bind(this)\n    this.getMaxZoom = this.getMaxZoom.bind(this)\n    this.setMaxZoom = this.setMaxZoom.bind(this)\n    this.getMarkers = this.getMarkers.bind(this)\n    this.addMarkers = this.addMarkers.bind(this)\n    this.getStyles = this.getStyles.bind(this)\n    this.setStyles = this.setStyles.bind(this)\n    this.addMarker = this.addMarker.bind(this)\n    this.onRemove = this.onRemove.bind(this)\n    this.getTitle = this.getTitle.bind(this)\n    this.setTitle = this.setTitle.bind(this)\n    this.repaint = this.repaint.bind(this)\n    this.onIdle = this.onIdle.bind(this)\n    this.redraw = this.redraw.bind(this)\n    this.onAdd = this.onAdd.bind(this)\n    this.draw = this.draw.bind(this)\n\n    this.extend = this.extend.bind(this)\n    this.extend(Clusterer, google.maps.OverlayView)\n\n    this.markers = []\n    this.clusters = []\n    this.listeners = []\n    this.activeMap = null\n    this.ready = false\n    this.gridSize = optOptions.gridSize || 60\n    this.minClusterSize = optOptions.minimumClusterSize || 2\n    this.maxZoom = optOptions.maxZoom || null\n    this.styles = optOptions.styles || []\n\n    this.title = optOptions.title || ''\n\n    this.zoomOnClick = true\n\n    if (optOptions.zoomOnClick !== undefined) {\n      this.zoomOnClick = optOptions.zoomOnClick\n    }\n\n    this.averageCenter = false\n\n    if (optOptions.averageCenter !== undefined) {\n      this.averageCenter = optOptions.averageCenter\n    }\n\n    this.ignoreHidden = false\n\n    if (optOptions.ignoreHidden !== undefined) {\n      this.ignoreHidden = optOptions.ignoreHidden\n    }\n\n    this.enableRetinaIcons = false\n\n    if (optOptions.enableRetinaIcons !== undefined) {\n      this.enableRetinaIcons = optOptions.enableRetinaIcons\n    }\n    this.imagePath = optOptions.imagePath || IMAGE_PATH\n\n    this.imageExtension = optOptions.imageExtension || IMAGE_EXTENSION\n\n    this.imageSizes = optOptions.imageSizes || IMAGE_SIZES\n\n    this.calculator = optOptions.calculator || CALCULATOR\n\n    this.batchSize = optOptions.batchSize || BATCH_SIZE\n\n    this.batchSizeIE = optOptions.batchSizeIE || BATCH_SIZE_IE\n\n    this.clusterClass = optOptions.clusterClass || CLUSTERER_CLASS\n\n    if (navigator.userAgent.toLowerCase().indexOf('msie') !== -1) {\n      // Try to avoid IE timeout when processing a huge number of markers:\n      this.batchSize = this.batchSizeIE\n    }\n\n    this.timerRefStatic = null\n\n    this.setupStyles()\n\n    this.addMarkers(optMarkers, true);\n\n    (this as unknown as google.maps.OverlayView).setMap(map) // Note: this causes onAdd to be called\n  }\n\n  onZoomChanged(): void {\n    this.resetViewport(false)\n\n    // Workaround for this Google bug: when map is at level 0 and \"-\" of\n    // zoom slider is clicked, a \"zoom_changed\" event is fired even though\n    // the map doesn't zoom out any further. In this situation, no \"idle\"\n    // event is triggered so the cluster markers that have been removed\n    // do not get redrawn. Same goes for a zoom in at maxZoom.\n    if (\n      (this as unknown as google.maps.OverlayView).getMap()?.getZoom() === ((this as unknown as google.maps.OverlayView).get('minZoom') || 0) ||\n      (this as unknown as google.maps.OverlayView).getMap()?.getZoom() === (this as unknown as google.maps.OverlayView).get('maxZoom')\n    ) {\n      google.maps.event.trigger(this, 'idle')\n    }\n  }\n\n  onIdle(): void {\n    this.redraw()\n  }\n\n  onAdd(): void {\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    this.activeMap = map\n\n    this.ready = true\n\n    this.repaint()\n\n    if (map !== null) {\n      // Add the map event listeners\n      this.listeners = [\n        google.maps.event.addListener(\n          map,\n          'zoom_changed',\n          this.onZoomChanged\n        ),\n        google.maps.event.addListener(\n          map,\n          'idle',\n          this.onIdle\n        ),\n      ]\n    }\n  }\n\n  onRemove(): void {\n    // Put all the managed markers back on the map:\n    for (const marker of this.markers) {\n      if (marker.getMap() !== this.activeMap) {\n        marker.setMap(this.activeMap)\n      }\n    }\n\n    // Remove all clusters:\n    for (const cluster of this.clusters) {\n      cluster.remove()\n    }\n\n    this.clusters = []\n\n    // Remove map event listeners:\n    for (const listener of this.listeners) {\n      google.maps.event.removeListener(listener)\n    }\n\n    this.listeners = []\n\n    this.activeMap = null\n\n    this.ready = false\n  }\n\n  draw(): void { return }\n\n  getMap(): null { return null }\n\n  getPanes(): null { return null }\n\n  getProjection()  {\n    return {\n      fromContainerPixelToLatLng(): null { return null },\n      fromDivPixelToLatLng(): null { return null},\n      fromLatLngToContainerPixel(): null { return null},\n      fromLatLngToDivPixel(): null { return null},\n      getVisibleRegion(): null { return null },\n      getWorldWidth(): number { return 0 }\n    }\n  }\n\n  setMap(): void { return }\n\n  addListener() {\n    return {\n      remove() { return }\n    }\n  }\n\n  bindTo(): void { return }\n\n  get(): void { return }\n\n  notify(): void { return }\n\n  set(): void { return }\n  setValues(): void { return }\n  unbind(): void { return }\n  unbindAll(): void { return }\n\n  setupStyles(): void {\n    if (this.styles.length > 0) {\n      return\n    }\n\n    for (let i = 0; i < this.imageSizes.length; i++) {\n      this.styles.push({\n        url: `${this.imagePath + (i + 1)}.${this.imageExtension}`,\n        height: this.imageSizes[i] || 0,\n        width: this.imageSizes[i] || 0,\n      })\n    }\n  }\n\n  fitMapToMarkers(): void {\n    const markers = this.getMarkers()\n\n    const bounds = new google.maps.LatLngBounds()\n\n    for (const marker of markers) {\n      const position = marker.getPosition()\n\n      if (position) {\n        bounds.extend(position)\n      }\n    }\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    if (map !== null && 'fitBounds' in map) {\n      map.fitBounds(bounds)\n    }\n\n  }\n\n  getGridSize(): number {\n    return this.gridSize\n  }\n\n  setGridSize(gridSize: number) {\n    this.gridSize = gridSize\n  }\n\n  getMinimumClusterSize(): number {\n    return this.minClusterSize\n  }\n\n  setMinimumClusterSize(minimumClusterSize: number) {\n    this.minClusterSize = minimumClusterSize\n  }\n\n  getMaxZoom(): number | null {\n    return this.maxZoom\n  }\n\n  setMaxZoom(maxZoom: number) {\n    this.maxZoom = maxZoom\n  }\n\n  getStyles(): ClusterIconStyle[] {\n    return this.styles\n  }\n\n  setStyles(styles: ClusterIconStyle[]) {\n    this.styles = styles\n  }\n\n  getTitle(): string {\n    return this.title\n  }\n\n  setTitle(title: string) {\n    this.title = title\n  }\n\n  getZoomOnClick(): boolean {\n    return this.zoomOnClick\n  }\n\n  setZoomOnClick(zoomOnClick: boolean) {\n    this.zoomOnClick = zoomOnClick\n  }\n\n  getAverageCenter(): boolean {\n    return this.averageCenter\n  }\n\n  setAverageCenter(averageCenter: boolean) {\n    this.averageCenter = averageCenter\n  }\n\n  getIgnoreHidden(): boolean {\n    return this.ignoreHidden\n  }\n\n  setIgnoreHidden(ignoreHidden: boolean) {\n    this.ignoreHidden = ignoreHidden\n  }\n\n  getEnableRetinaIcons(): boolean {\n    return this.enableRetinaIcons\n  }\n\n  setEnableRetinaIcons(enableRetinaIcons: boolean) {\n    this.enableRetinaIcons = enableRetinaIcons\n  }\n\n  getImageExtension(): string {\n    return this.imageExtension\n  }\n\n  setImageExtension(imageExtension: string) {\n    this.imageExtension = imageExtension\n  }\n\n  getImagePath(): string {\n    return this.imagePath\n  }\n\n  setImagePath(imagePath: string) {\n    this.imagePath = imagePath\n  }\n\n  getImageSizes(): number[] {\n    return this.imageSizes\n  }\n\n  setImageSizes(imageSizes: number[]) {\n    this.imageSizes = imageSizes\n  }\n\n  getCalculator(): TCalculator {\n    return this.calculator\n  }\n\n  setCalculator(calculator: TCalculator) {\n    this.calculator = calculator\n  }\n\n  getBatchSizeIE(): number {\n    return this.batchSizeIE\n  }\n\n  setBatchSizeIE(batchSizeIE: number) {\n    this.batchSizeIE = batchSizeIE\n  }\n\n  getClusterClass(): string {\n    return this.clusterClass\n  }\n\n  setClusterClass(clusterClass: string) {\n    this.clusterClass = clusterClass\n  }\n\n  getMarkers(): MarkerExtended[] {\n    return this.markers\n  }\n\n  getTotalMarkers(): number {\n    return this.markers.length\n  }\n\n  getClusters(): Cluster[] {\n    return this.clusters\n  }\n\n  getTotalClusters(): number {\n    return this.clusters.length\n  }\n\n  addMarker(marker: MarkerExtended, optNoDraw: boolean) {\n    this.pushMarkerTo(marker)\n\n    if (!optNoDraw) {\n      this.redraw()\n    }\n  }\n\n  addMarkers(markers: MarkerExtended[], optNoDraw: boolean) {\n    for (const key in markers) {\n      if (Object.prototype.hasOwnProperty.call(markers, key)) {\n        const marker = markers[key]\n\n        if (marker) {\n          this.pushMarkerTo(marker)\n        }\n      }\n    }\n\n    if (!optNoDraw) {\n      this.redraw()\n    }\n  }\n\n  pushMarkerTo(marker: MarkerExtended) {\n    // If the marker is draggable add a listener so we can update the clusters on the dragend:\n    if (marker.getDraggable()) {\n      google.maps.event.addListener(marker, 'dragend', () => {\n        if (this.ready) {\n          marker.isAdded = false\n\n          this.repaint()\n        }\n      })\n    }\n\n    marker.isAdded = false\n\n    this.markers.push(marker)\n  }\n\n  removeMarker_(marker: MarkerExtended): boolean {\n    let index = -1\n\n    if (this.markers.indexOf) {\n      index = this.markers.indexOf(marker)\n    } else {\n      for (let i = 0; i < this.markers.length; i++) {\n        if (marker === this.markers[i]) {\n          index = i\n\n          break\n        }\n      }\n    }\n\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false\n    }\n\n    marker.setMap(null)\n\n    this.markers.splice(index, 1) // Remove the marker from the list of managed markers\n\n    return true\n  }\n\n  removeMarker(marker: MarkerExtended, optNoDraw: boolean): boolean {\n    const removed = this.removeMarker_(marker)\n\n    if (!optNoDraw && removed) {\n      this.repaint()\n    }\n\n    return removed\n  }\n\n  removeMarkers(markers: MarkerExtended[], optNoDraw: boolean): boolean {\n    let removed = false\n\n    for (const marker of markers) {\n      removed = removed || this.removeMarker_(marker)\n    }\n\n    if (!optNoDraw && removed) {\n      this.repaint()\n    }\n\n    return removed\n  }\n\n  clearMarkers() {\n    this.resetViewport(true)\n\n    this.markers = []\n  }\n\n  repaint() {\n    const oldClusters = this.clusters.slice()\n\n    this.clusters = []\n\n    this.resetViewport(false)\n\n    this.redraw()\n\n    // Remove the old clusters.\n    // Do it in a timeout to prevent blinking effect.\n    setTimeout(function timeout() {\n      for (const oldCluster of oldClusters) {\n        oldCluster.remove()\n      }\n    }, 0)\n  }\n\n  getExtendedBounds(bounds: google.maps.LatLngBounds): google.maps.LatLngBounds {\n    const projection = (this as unknown as google.maps.OverlayView).getProjection()\n\n    // Convert the points to pixels and the extend out by the grid size.\n    const trPix = projection.fromLatLngToDivPixel(\n      // Turn the bounds into latlng.\n      new google.maps.LatLng(bounds.getNorthEast().lat(), bounds.getNorthEast().lng())\n    )\n\n    if (trPix !== null) {\n      trPix.x += this.gridSize\n      trPix.y -= this.gridSize\n    }\n\n    const blPix = projection.fromLatLngToDivPixel(\n      // Turn the bounds into latlng.\n      new google.maps.LatLng(bounds.getSouthWest().lat(), bounds.getSouthWest().lng())\n    )\n\n    if (blPix !== null) {\n      blPix.x -= this.gridSize\n      blPix.y += this.gridSize\n    }\n\n\n    // Extend the bounds to contain the new bounds.\n    if (trPix !== null) {\n      // Convert the pixel points back to LatLng nw\n      const point1 = projection.fromDivPixelToLatLng(trPix)\n\n      if (point1 !== null) {\n        bounds.extend(point1)\n      }\n    }\n\n    if (blPix !== null) {\n      // Convert the pixel points back to LatLng sw\n      const point2 =  projection.fromDivPixelToLatLng(blPix)\n\n      if (point2 !== null) {\n        bounds.extend(\n          point2\n        )\n      }\n    }\n\n\n    return bounds\n  }\n\n  redraw() {\n    // Redraws all the clusters.\n    this.createClusters(0)\n  }\n\n  resetViewport(optHide: boolean) {\n    // Remove all the clusters\n    for (const cluster of this.clusters) {\n      cluster.remove()\n    }\n\n    this.clusters = []\n\n    // Reset the markers to not be added and to be removed from the map.\n    for (const marker of this.markers) {\n      marker.isAdded = false\n\n      if (optHide) {\n        marker.setMap(null)\n      }\n    }\n  }\n\n  distanceBetweenPoints(p1: google.maps.LatLng, p2: google.maps.LatLng): number {\n    const R = 6371 // Radius of the Earth in km\n\n    const dLat = ((p2.lat() - p1.lat()) * Math.PI) / 180\n    const dLon = ((p2.lng() - p1.lng()) * Math.PI) / 180\n\n    const a =\n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n      Math.cos((p1.lat() * Math.PI) / 180) *\n        Math.cos((p2.lat() * Math.PI) / 180) *\n        Math.sin(dLon / 2) *\n        Math.sin(dLon / 2)\n\n    return R * (2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)))\n  }\n\n  isMarkerInBounds(marker: MarkerExtended, bounds: google.maps.LatLngBounds): boolean {\n    const position = marker.getPosition()\n\n    if (position) {\n      return bounds.contains(position)\n    }\n\n    return false\n  }\n\n  addToClosestCluster(marker: MarkerExtended) {\n    let cluster\n\n    let distance = 40000 // Some large number\n\n    let clusterToAddTo = null\n\n    for (const clusterElement of this.clusters) {\n      cluster = clusterElement\n\n      const center = cluster.getCenter()\n\n      const position = marker.getPosition()\n\n      if (center && position) {\n        const d = this.distanceBetweenPoints(center, position)\n\n        if (d < distance) {\n          distance = d\n\n          clusterToAddTo = cluster\n        }\n      }\n    }\n\n    if (clusterToAddTo && clusterToAddTo.isMarkerInClusterBounds(marker)) {\n      clusterToAddTo.addMarker(marker)\n    } else {\n      cluster = new Cluster(this)\n\n      cluster.addMarker(marker)\n\n      this.clusters.push(cluster)\n    }\n  }\n\n  createClusters(iFirst: number) {\n    if (!this.ready) {\n      return\n    }\n\n    // Cancel previous batch processing if we're working on the first batch:\n    if (iFirst === 0) {\n      /**\n       * This event is fired when the <code>Clusterer</code> begins\n       *  clustering markers.\n       * @name Clusterer#clusteringbegin\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringbegin', this)\n\n      if (this.timerRefStatic !== null) {\n        window.clearTimeout(this.timerRefStatic)\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        delete this.timerRefStatic\n      }\n    }\n\n    const map = (this as unknown as google.maps.OverlayView).getMap()\n\n    const bounds = map !== null && 'getBounds' in map ? map.getBounds() : null\n\n    const zoom =  map?.getZoom() || 0\n    // Get our current map view bounds.\n    // Create a new bounds object so we don't affect the map.\n    //\n    // See Comments 9 & 11 on Issue 3651 relating to this workaround for a Google Maps bug:\n    const mapBounds = zoom > 3\n        ? new google.maps.LatLngBounds(\n            bounds?.getSouthWest(),\n            bounds?.getNorthEast()\n          )\n        : new google.maps.LatLngBounds(\n            new google.maps.LatLng(85.02070771743472, -178.48388434375),\n            new google.maps.LatLng(-85.08136444384544, 178.00048865625)\n          )\n\n    const extendedMapBounds = this.getExtendedBounds(mapBounds)\n\n    const iLast = Math.min(iFirst + this.batchSize, this.markers.length)\n\n    for (let i = iFirst; i < iLast; i++) {\n      const marker = this.markers[i]\n\n      if (marker && !marker.isAdded && this.isMarkerInBounds(marker, extendedMapBounds) && (!this.ignoreHidden || (this.ignoreHidden && marker.getVisible()))) {\n        this.addToClosestCluster(marker)\n      }\n    }\n\n    if (iLast < this.markers.length) {\n      this.timerRefStatic = window.setTimeout(\n        () => {\n          this.createClusters(iLast)\n        },\n        0\n      )\n    } else {\n      this.timerRefStatic = null\n\n      /**\n       * This event is fired when the <code>Clusterer</code> stops\n       *  clustering markers.\n       * @name Clusterer#clusteringend\n       * @param {Clusterer} mc The Clusterer whose markers are being clustered.\n       * @event\n       */\n      google.maps.event.trigger(this, 'clusteringend', this)\n\n      for (const cluster of this.clusters) {\n        cluster.updateIcon()\n      }\n    }\n  }\n\n  extend<A extends typeof Clusterer | typeof ClusterIcon>(obj1: A, obj2: typeof google.maps.OverlayView): A {\n    return function applyExtend(this: A, object: typeof google.maps.OverlayView): A {\n      for (const property in object.prototype) {\n\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const prop = property as keyof google.maps.OverlayView & (string & {})\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        this.prototype[prop] = object.prototype[prop]\n      }\n\n      return this\n    }.apply<A, [typeof google.maps.OverlayView], A>(obj1, [obj2])\n  }\n}\n"], "names": [], "mappings": ";;AAMA,IAAA,WAAA,kBAAA,YAAA;IA2BE,SAAY,WAAA,CAAA,OAAgB,EAAE,MAA0B,EAAA;AACtD,QAAA,OAAO,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAEnE,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;AAEtB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,eAAe,EAAE,CAAA;AAErE,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAA;AAEtC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;AAEpB,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;AAEvB,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;AAEf,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;AAEhB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;AAEpB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;AAEjC,QAAA,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;AAEb,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;AACf,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QAEd,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACxB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAExB,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;AAClB,QAAA,IAAI,CAAC,cAAc,GAAG,MAAM,CAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAA;AAEpC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;AAE/B,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;AAC/B,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;AACjC,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEnB,IAA2C,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;QAErE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;KACzD;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AACE,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,mBAAmB,CAAA;KACtD,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;AACE,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;AAE/B,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAA;KACnC,CAAA;IAED,WAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAY,EAAA;AAClB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;AAEhC,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC/B,IAAM,iBAAe,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAA;AAEnD;;;;;AAKG;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAe,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;AACjE,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAe,EAAE,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;;;AAIxE,YAAA,IAAI,iBAAe,CAAC,cAAc,EAAE,EAAE;;AAEpC,gBAAA,IAAM,SAAO,GAAG,iBAAe,CAAC,UAAU,EAAE,CAAA;gBAE5C,IAAM,QAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAA;AAEvC,gBAAA,IAAM,GAAG,GAAI,iBAAsD,CAAC,MAAM,EAAE,CAAA;gBAE5E,IAAI,GAAG,KAAK,IAAI,IAAI,WAAW,IAAI,GAAG,EAAE;AACtC,oBAAA,GAAG,CAAC,SAAS,CAAC,QAAM,CAAC,CAAA;iBACtB;;AAID,gBAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,YAAA;AAC/B,oBAAA,IAAM,GAAG,GAAI,iBAAsD,CAAC,MAAM,EAAE,CAAA;AAE5E,oBAAA,IAAI,GAAG,KAAK,IAAI,EAAE;AAChB,wBAAA,IAAI,WAAW,IAAI,GAAG,EAAE;AACtB,4BAAA,GAAG,CAAC,SAAS,CAAC,QAAM,CAAC,CAAA;yBACtB;wBAED,IAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;;wBAG/B,IACE,SAAO,KAAK,IAAI;4BAChB,IAAI,GAAG,SAAO,EACd;AACA,4BAAA,GAAG,CAAC,OAAO,CAAC,SAAO,GAAG,CAAC,CAAC,CAAA;yBACzB;qBACF;iBACF,EAAE,GAAG,CAAC,CAAA;aACR;;AAGD,YAAA,KAAK,CAAC,YAAY,GAAG,IAAI,CAAA;AAEzB,YAAA,IAAI,KAAK,CAAC,eAAe,EAAE;gBACzB,KAAK,CAAC,eAAe,EAAE,CAAA;aACxB;SACF;KACF,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;AACE;;;;;AAKG;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CACvB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAC3B,WAAW,EACX,IAAI,CAAC,OAAO,CACb,CAAA;KACF,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;AACE;;;;;AAKG;QACH,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CACvB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAC3B,UAAU,EACV,IAAI,CAAC,OAAO,CACb,CAAA;KACF,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;;QACE,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QAExC,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;AAEnC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,IAAI,EAAE,CAAA;SACZ;AAEA,QAAA,CAAA,EAAA,GAAC,IAA2C,CAAC,QAAQ,EAAE,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAElG,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;AAEjE,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;;AAEhB,YAAA,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CACxD,GAAG,EACH,gBAAgB,EAChB,IAAI,CAAC,eAAe,CACrB,CAAA;YAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;YAExD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAEhD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;YAExD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;SACvD;KACF,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QACE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;YACnC,IAAI,CAAC,IAAI,EAAE,CAAA;AAEX,YAAA,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;gBACvC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;aAC7D;YAED,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;YAE3D,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAEnD,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;YAE3D,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;YAEzD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAEzC,YAAA,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;AACzB,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;AAEjC,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;aACpB;AAED,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;SAChB;KACF,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AACE,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;YACpD,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAE9C,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG,EAAA,CAAA,MAAA,CAAG,GAAG,CAAC,CAAC,OAAI,GAAG,GAAG,CAAA;YACtD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,EAAA,CAAA,MAAA,CAAG,GAAG,CAAC,CAAC,OAAI,GAAG,GAAG,CAAA;SACxD;KACF,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AACE,QAAA,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAA;SAChC;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;KACrB,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;;QACE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3B,YAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI;AACnC,gBAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW;gBACtC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,GAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;;YAGlF,IAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAE7C,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAA,CAAA,EAAA,GAAA,EAAE,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,KAAI,GAAG,EAAE,EAAE,CAAC,CAAA;YACrE,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAA,CAAA,EAAA,GAAA,EAAE,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,KAAI,GAAG,EAAE,EAAE,CAAC,CAAA;YAErE,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAE9C,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;YACnC,IAAI,CAAC,GAAG,CAAE,YAAY,CAAC,OAAO,EAAE,4CAA6C,CAAA,MAAA,CAAA,GAAG,KAAK,IAAI,GAAG,UAAG,GAAG,CAAC,CAAC,EAAI,IAAA,CAAA,GAAG,GAAG,EAAA,UAAA,CAAA,CAAA,MAAA,CAAW,GAAG,KAAK,IAAI,GAAG,UAAG,GAAG,CAAC,CAAC,EAAA,IAAA,CAAI,GAAG,GAAG,EAAA,WAAA,CAAA,CAAA,MAAA,CAAY,IAAI,CAAC,KAAK,EAAA,cAAA,CAAA,CAAA,MAAA,CAAe,IAAI,CAAC,MAAM,EAAM,MAAA,CAAA,CAAC,CAAA;YAEjN,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;AAEzC,YAAA,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAA;AAClB,YAAA,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AAClB,YAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;AACtB,YAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;YACxB,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,2BAA4B,CAAA,MAAA,CAAA,OAAO,EAAa,YAAA,CAAA,CAAA,MAAA,CAAA,OAAO,EAAI,IAAA,CAAA,CAAC,CAAA;YAEtF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,iBAAiB,EAAE;gBAClD,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,QAAS,CAAA,MAAA,CAAA,OAAO,EAAQ,OAAA,CAAA,CAAA,MAAA,CAAA,OAAO,GAAG,IAAI,CAAC,KAAK,EAAA,OAAA,CAAA,CAAA,MAAA,CAC3D,OAAO,GAAG,IAAI,CAAC,MAAM,EAAA,KAAA,CAAA,CAAA,MAAA,CACjB,OAAO,EAAA,GAAA,CAAG,CAAA;aACjB;YAED,IAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YAE7C,OAAO,CAAE,YAAY,CAAC,OAAO,EAAE,mCAA4B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,wBAAc,IAAI,CAAC,SAAS,EAAA,eAAA,CAAA,CAAA,MAAA,CAAgB,IAAI,CAAC,QAAQ,EAAoB,mBAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,UAAU,EAAkB,iBAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,UAAU,EAAA,eAAA,CAAA,CAAA,MAAA,CAAgB,IAAI,CAAC,SAAS,EAAA,qBAAA,CAAA,CAAA,MAAA,CAAsB,IAAI,CAAC,cAAc,EAAgC,+BAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,KAAK,EAAoB,mBAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,MAAM,EAAI,IAAA,CAAA,CAAC,CAAA;AAE9X,YAAA,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,0CAAE,IAAI;gBAAE,OAAO,CAAC,SAAS,GAAG,EAAG,CAAA,MAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAE,CAAA;AAC7D,YAAA,IAAI,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,0CAAE,IAAI;gBAAE,OAAO,CAAC,SAAS,GAAG,EAAG,CAAA,MAAA,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAE,CAAA;AAE7D,YAAA,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,EAAE,CAAA;AAEvB,YAAA,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;AACzB,YAAA,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;AAE7B,YAAA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAA;YAEzB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAA;SAC5B;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;KACpB,CAAA;IAED,WAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,IAAqB,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAEhB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,SAAS,EAAE,CAAA;AAEtD,QAAA,IAAM,KAAK,GACT,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAElE,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;AACpB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;AAC1B,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;AAExB,YAAA,IAAI,KAAK,CAAC,SAAS,EAAE;AACnB,gBAAA,IAAI,CAAC,SAAS,GAAG,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,gBAAgB,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,KAAK,CAAC,SAAS,CAAE,CAAA;aAC/D;AAED,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAC5C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;YAEvE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,OAAO,CAAA;YAE3C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAA;YAEpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,MAAM,CAAA;YAEpD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,MAAM,CAAA;YAE5C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,QAAQ,CAAA;YAE5C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,kBAAkB,CAAA;YAExD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAA;SAC5D;KACF,CAAA;IAED,WAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAA0B,EAAA;AAClC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;KACrB,CAAA;IAED,WAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,MAA0B,EAAA;QACzC,IAAM,GAAG,GAAI,IAA2C,CAAC,aAAa,EAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;AAErG,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;YAChB,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;YAE3B,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;SAC5B;AAED,QAAA,OAAO,GAAG,CAAA;KACX,CAAA;IACH,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA;;ACjXD;AAQA,IAAA,OAAA,kBAAA,YAAA;AAWE,IAAA,SAAA,OAAA,CAAY,eAA0B,EAAA;AACpC,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAA;QAEtC,IAAI,CAAC,GAAG,GAAI,IAAI,CAAC,eAAsD,CAAC,MAAM,EAAE,CAAA;QAEhF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAA;QAElD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAA;QAElE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAA;AAE5D,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;AAEjB,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;AAEvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;AAElB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAA;QAE1E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;KACjE;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;AACE,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;KAC3B,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QACE,OAAO,IAAI,CAAC,OAAO,CAAA;KACpB,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;QACE,OAAO,IAAI,CAAC,MAAM,CAAA;KACnB,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,OAAO,IAAI,CAAC,GAAG,CAAA;KAChB,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACE,OAAO,IAAI,CAAC,eAAe,CAAA;KAC5B,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;AACE,QAAA,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;AAErE,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QAEjC,KAAqB,IAAA,EAAA,GAAA,CAAO,EAAP,SAAO,GAAA,OAAA,EAAP,qBAAO,EAAP,EAAA,EAAO,EAAE;AAAzB,YAAA,IAAM,MAAM,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AACf,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;YAErC,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;aACxB;SACF;AAED,QAAA,OAAO,MAAM,CAAA;KACd,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;AACG,QAAA,IAAI,CAAC,WAAkD,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;AAErE,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;;;QAIjB,OAAO,IAAI,CAAC,OAAO,CAAA;KACpB,CAAA;IAED,OAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAAsB,EAAA;;AAC9B,QAAA,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;AACrC,YAAA,OAAO,KAAK,CAAA;SACb;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;YAErC,IAAI,QAAQ,EAAE;AACZ,gBAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAA;gBAEtB,IAAI,CAAC,eAAe,EAAE,CAAA;aACvB;SACF;aAAM;AACL,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,gBAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;gBAErC,IAAI,QAAQ,EAAE;oBACZ,IAAM,QAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA;oBAEtC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAClC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,QAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,QAAM,EAC5D,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,QAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,QAAM,CAC7D,CAAA;oBAED,IAAI,CAAC,eAAe,EAAE,CAAA;iBACvB;aACF;SACF;AAED,QAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAA;AAErB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;AAEzB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAElC,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAA;QAEjD,IAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE,CAAA;AAEhC,QAAA,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,GAAG,OAAO,EAAE;;YAErE,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE;AAChC,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aACxB;SACF;AAAM,aAAA,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;;YAEvC,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE;AAChC,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aACxB;SACF;AAAM,aAAA,IAAI,MAAM,KAAK,IAAI,CAAC,cAAc,EAAE;;YAEzC,KAA4B,IAAA,EAAA,GAAA,CAAY,EAAZ,EAAA,GAAA,IAAI,CAAC,OAAO,EAAZ,EAAY,GAAA,EAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAE;AAArC,gBAAA,IAAM,aAAa,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACtB,gBAAA,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;aAC3B;SACF;aAAM;AACL,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;AAED,QAAA,OAAO,IAAI,CAAA;KACZ,CAAA;IAED,OAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,MAAsB,EAAA;AAC5C,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;AACxB,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;YAErC,IAAI,QAAQ,EAAE;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;aACtC;SACF;AAED,QAAA,OAAO,KAAK,CAAA;KACb,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAClD,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CACvD,CAAA;KACF,CAAA;AAED,IAAA,OAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;;AACE,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAElC,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAA;QAEjD,IAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE,CAAA;AAEhC,QAAA,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,GAAG,OAAO,EAAE;AACrE,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;YAEvB,OAAM;SACP;AAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;;AAEhC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;YAEvB,OAAM;SACP;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACxC;QAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CACvB,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAC5F,CAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;KACxB,CAAA;IAED,OAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,MAAsB,EAAA;AACzC,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;SACrC;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AAC9B,gBAAA,OAAO,IAAI,CAAA;aACZ;SACF;AAED,QAAA,OAAO,KAAK,CAAA;KACb,CAAA;IACH,OAAC,OAAA,CAAA;AAAD,CAAC,EAAA;;AC9ND;AACA;AAYA;;;AAGG;AACH,SAAS,UAAU,CACjB,OAAyB,EACzB,SAAiB,EAAA;AAEjB,IAAA,IAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAA;IAE5B,IAAM,cAAc,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAA;IAE9C,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;IAEjD,OAAO;AACL,QAAA,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE;AACtB,QAAA,KAAK,EAAA,KAAA;AACL,QAAA,KAAK,EAAE,EAAE;KACV,CAAA;AACH,CAAC;AAED,IAAM,UAAU,GAAG,IAAI,CAAA;AAEvB,IAAM,aAAa,GAAG,GAAG,CAAA;AAEzB,IAAM,UAAU,GACd,wFAAwF,CAAA;AAE1F,IAAM,eAAe,GAAG,KAAK,CAAA;AAE7B,IAAM,WAAW,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAExC,IAAM,eAAe,GAAG,SAAS,CAAA;AAEjC,IAAA,SAAA,kBAAA,YAAA;AAwBE,IAAA,SAAA,SAAA,CACE,GAAoB,EACpB,UAAiC,EACjC,UAAiC,EAAA;AADjC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAiC,GAAA,EAAA,CAAA,EAAA;AACjC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAiC,GAAA,EAAA,CAAA,EAAA;QAEjC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEhC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAE/C,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;AAClB,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAA;QACzC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,kBAAkB,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,IAAI,IAAI,CAAA;QACzC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,EAAE,CAAA;QAErC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE,CAAA;AAEnC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;AAEvB,QAAA,IAAI,UAAU,CAAC,WAAW,KAAK,SAAS,EAAE;AACxC,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAA;SAC1C;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;AAE1B,QAAA,IAAI,UAAU,CAAC,aAAa,KAAK,SAAS,EAAE;AAC1C,YAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAA;SAC9C;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;AAEzB,QAAA,IAAI,UAAU,CAAC,YAAY,KAAK,SAAS,EAAE;AACzC,YAAA,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAA;SAC5C;AAED,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;AAE9B,QAAA,IAAI,UAAU,CAAC,iBAAiB,KAAK,SAAS,EAAE;AAC9C,YAAA,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAA;SACtD;QACD,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,UAAU,CAAA;QAEnD,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,IAAI,eAAe,CAAA;QAElE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,WAAW,CAAA;QAEtD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,UAAU,CAAA;QAErD,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,UAAU,CAAA;QAEnD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,aAAa,CAAA;QAE1D,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,eAAe,CAAA;AAE9D,QAAA,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;;AAE5D,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAA;SAClC;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QAE1B,IAAI,CAAC,WAAW,EAAE,CAAA;AAElB,QAAA,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAEjC,QAAA,IAA2C,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;KACzD;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;;AACE,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;;;;;;AAOzB,QAAA,IACE,CAAA,CAAC,EAAA,GAAA,IAA2C,CAAC,MAAM,EAAE,0CAAE,OAAO,EAAE,OAAO,IAA2C,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACvI,YAAA,CAAA,MAAC,IAA2C,CAAC,MAAM,EAAE,0CAAE,OAAO,EAAE,MAAM,IAA2C,CAAC,GAAG,CAAC,SAAS,CAAC,EAChI;YACA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;SACxC;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,IAAI,CAAC,MAAM,EAAE,CAAA;KACd,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;AAEjE,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;AAEpB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QAEjB,IAAI,CAAC,OAAO,EAAE,CAAA;AAEd,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;;YAEhB,IAAI,CAAC,SAAS,GAAG;AACf,gBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC3B,GAAG,EACH,cAAc,EACd,IAAI,CAAC,aAAa,CACnB;AACD,gBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAC3B,GAAG,EACH,MAAM,EACN,IAAI,CAAC,MAAM,CACZ;aACF,CAAA;SACF;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;;QAEE,KAAqB,IAAA,EAAA,GAAA,CAAY,EAAZ,EAAA,GAAA,IAAI,CAAC,OAAO,EAAZ,EAAY,GAAA,EAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAE;AAA9B,YAAA,IAAM,MAAM,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YACf,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,SAAS,EAAE;AACtC,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aAC9B;SACF;;QAGD,KAAsB,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;AAAhC,YAAA,IAAM,OAAO,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YAChB,OAAO,CAAC,MAAM,EAAE,CAAA;SACjB;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;;QAGlB,KAAuB,IAAA,EAAA,GAAA,CAAc,EAAd,EAAA,GAAA,IAAI,CAAC,SAAS,EAAd,EAAc,GAAA,EAAA,CAAA,MAAA,EAAd,EAAc,EAAA,EAAE;AAAlC,YAAA,IAAM,QAAQ,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YACjB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;SAC3C;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;AAEnB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;AAErB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;KACnB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA,EAAe,OAAM,EAAE,CAAA;AAEvB,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAiB,EAAA,OAAO,IAAI,CAAA,EAAE,CAAA;AAE9B,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAmB,EAAA,OAAO,IAAI,CAAA,EAAE,CAAA;AAEhC,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;QACE,OAAO;AACL,YAAA,0BAA0B,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAE;AAClD,YAAA,oBAAoB,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAC;AAC3C,YAAA,0BAA0B,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAC;AACjD,YAAA,oBAAoB,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAC;AAC3C,YAAA,gBAAgB,EAAW,YAAA,EAAA,OAAO,IAAI,CAAA,EAAE;AACxC,YAAA,aAAa,EAAa,YAAA,EAAA,OAAO,CAAC,CAAA,EAAE;SACrC,CAAA;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;AAEzB,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACE,OAAO;YACL,MAAM,EAAA,YAAA,EAAK,OAAM,EAAE;SACpB,CAAA;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;AAEzB,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAH,YAAA,EAAc,OAAM,EAAE,CAAA;AAEtB,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;AAEzB,IAAA,SAAA,CAAA,SAAA,CAAA,GAAG,GAAH,YAAA,EAAc,OAAM,EAAE,CAAA;AACtB,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA,EAAoB,OAAM,EAAE,CAAA;AAC5B,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA,EAAiB,OAAM,EAAE,CAAA;AACzB,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA,EAAoB,OAAM,EAAE,CAAA;AAE5B,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,OAAM;SACP;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,gBAAA,GAAG,EAAE,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,IAAI,CAAC,cAAc,CAAE;gBACzD,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC/B,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AAC/B,aAAA,CAAC,CAAA;SACH;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AACE,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QAEjC,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAA;QAE7C,KAAqB,IAAA,EAAA,GAAA,CAAO,EAAP,SAAO,GAAA,OAAA,EAAP,qBAAO,EAAP,EAAA,EAAO,EAAE;AAAzB,YAAA,IAAM,MAAM,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AACf,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;YAErC,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;aACxB;SACF;AAED,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;QAEjE,IAAI,GAAG,KAAK,IAAI,IAAI,WAAW,IAAI,GAAG,EAAE;AACtC,YAAA,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;SACtB;KAEF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACE,OAAO,IAAI,CAAC,QAAQ,CAAA;KACrB,CAAA;IAED,SAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,QAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;KACzB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,YAAA;QACE,OAAO,IAAI,CAAC,cAAc,CAAA;KAC3B,CAAA;IAED,SAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,kBAA0B,EAAA;AAC9C,QAAA,IAAI,CAAC,cAAc,GAAG,kBAAkB,CAAA;KACzC,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QACE,OAAO,IAAI,CAAC,OAAO,CAAA;KACpB,CAAA;IAED,SAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,OAAe,EAAA;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;KACvB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;QACE,OAAO,IAAI,CAAC,MAAM,CAAA;KACnB,CAAA;IAED,SAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAA0B,EAAA;AAClC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;KACrB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;QACE,OAAO,IAAI,CAAC,KAAK,CAAA;KAClB,CAAA;IAED,SAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;KACnB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACE,OAAO,IAAI,CAAC,WAAW,CAAA;KACxB,CAAA;IAED,SAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAoB,EAAA;AACjC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;KAC/B,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;QACE,OAAO,IAAI,CAAC,aAAa,CAAA;KAC1B,CAAA;IAED,SAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,aAAsB,EAAA;AACrC,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAA;KACnC,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;QACE,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB,CAAA;IAED,SAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAqB,EAAA;AACnC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;KACjC,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;QACE,OAAO,IAAI,CAAC,iBAAiB,CAAA;KAC9B,CAAA;IAED,SAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,iBAA0B,EAAA;AAC7C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAA;KAC3C,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;QACE,OAAO,IAAI,CAAC,cAAc,CAAA;KAC3B,CAAA;IAED,SAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,cAAsB,EAAA;AACtC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;KACrC,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACE,OAAO,IAAI,CAAC,SAAS,CAAA;KACtB,CAAA;IAED,SAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,SAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;KAC3B,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;QACE,OAAO,IAAI,CAAC,UAAU,CAAA;KACvB,CAAA;IAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;KAC7B,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;QACE,OAAO,IAAI,CAAC,UAAU,CAAA;KACvB,CAAA;IAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAuB,EAAA;AACnC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;KAC7B,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACE,OAAO,IAAI,CAAC,WAAW,CAAA;KACxB,CAAA;IAED,SAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAmB,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;KAC/B,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;QACE,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB,CAAA;IAED,SAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAoB,EAAA;AAClC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;KACjC,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QACE,OAAO,IAAI,CAAC,OAAO,CAAA;KACpB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AACE,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;KAC3B,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACE,OAAO,IAAI,CAAC,QAAQ,CAAA;KACrB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AACE,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;KAC5B,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,MAAsB,EAAE,SAAkB,EAAA;AAClD,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAEzB,IAAI,CAAC,SAAS,EAAE;YACd,IAAI,CAAC,MAAM,EAAE,CAAA;SACd;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,OAAyB,EAAE,SAAkB,EAAA;AACtD,QAAA,KAAK,IAAM,GAAG,IAAI,OAAO,EAAE;AACzB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;AACtD,gBAAA,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;gBAE3B,IAAI,MAAM,EAAE;AACV,oBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;iBAC1B;aACF;SACF;QAED,IAAI,CAAC,SAAS,EAAE;YACd,IAAI,CAAC,MAAM,EAAE,CAAA;SACd;KACF,CAAA;IAED,SAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,MAAsB,EAAA;QAAnC,IAeC,KAAA,GAAA,IAAA,CAAA;;AAbC,QAAA,IAAI,MAAM,CAAC,YAAY,EAAE,EAAE;YACzB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,YAAA;AAC/C,gBAAA,IAAI,KAAI,CAAC,KAAK,EAAE;AACd,oBAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAA;oBAEtB,KAAI,CAAC,OAAO,EAAE,CAAA;iBACf;AACH,aAAC,CAAC,CAAA;SACH;AAED,QAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAA;AAEtB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;KAC1B,CAAA;IAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,MAAsB,EAAA;AAClC,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,CAAA;AAEd,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACxB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;SACrC;aAAM;AACL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBAC9B,KAAK,GAAG,CAAC,CAAA;oBAET,MAAK;iBACN;aACF;SACF;AAED,QAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;;AAEhB,YAAA,OAAO,KAAK,CAAA;SACb;AAED,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAEnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;AAE7B,QAAA,OAAO,IAAI,CAAA;KACZ,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,MAAsB,EAAE,SAAkB,EAAA;QACrD,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;AAE1C,QAAA,IAAI,CAAC,SAAS,IAAI,OAAO,EAAE;YACzB,IAAI,CAAC,OAAO,EAAE,CAAA;SACf;AAED,QAAA,OAAO,OAAO,CAAA;KACf,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,UAAc,OAAyB,EAAE,SAAkB,EAAA;QACzD,IAAI,OAAO,GAAG,KAAK,CAAA;QAEnB,KAAqB,IAAA,EAAA,GAAA,CAAO,EAAP,SAAO,GAAA,OAAA,EAAP,qBAAO,EAAP,EAAA,EAAO,EAAE;AAAzB,YAAA,IAAM,MAAM,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;YACf,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;SAChD;AAED,QAAA,IAAI,CAAC,SAAS,IAAI,OAAO,EAAE;YACzB,IAAI,CAAC,OAAO,EAAE,CAAA;SACf;AAED,QAAA,OAAO,OAAO,CAAA;KACf,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;AACE,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;AAExB,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;KAClB,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;AAEzC,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;AAElB,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QAEzB,IAAI,CAAC,MAAM,EAAE,CAAA;;;QAIb,UAAU,CAAC,SAAS,OAAO,GAAA;YACzB,KAAyB,IAAA,EAAA,GAAA,CAAW,EAAX,aAAW,GAAA,WAAA,EAAX,yBAAW,EAAX,EAAA,EAAW,EAAE;AAAjC,gBAAA,IAAM,UAAU,GAAA,aAAA,CAAA,EAAA,CAAA,CAAA;gBACnB,UAAU,CAAC,MAAM,EAAE,CAAA;aACpB;SACF,EAAE,CAAC,CAAC,CAAA;KACN,CAAA;IAED,SAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,MAAgC,EAAA;AAChD,QAAA,IAAM,UAAU,GAAI,IAA2C,CAAC,aAAa,EAAE,CAAA;;AAG/E,QAAA,IAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB;;QAE3C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CACjF,CAAA;AAED,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;AACxB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;SACzB;AAED,QAAA,IAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB;;QAE3C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CACjF,CAAA;AAED,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;AACxB,YAAA,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;SACzB;;AAID,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;;YAElB,IAAM,MAAM,GAAG,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAA;AAErD,YAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,gBAAA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;aACtB;SACF;AAED,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;;YAElB,IAAM,MAAM,GAAI,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAA;AAEtD,YAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,gBAAA,MAAM,CAAC,MAAM,CACX,MAAM,CACP,CAAA;aACF;SACF;AAGD,QAAA,OAAO,MAAM,CAAA;KACd,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;;AAEE,QAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;KACvB,CAAA;IAED,SAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,OAAgB,EAAA;;QAE5B,KAAsB,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;AAAhC,YAAA,IAAM,OAAO,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YAChB,OAAO,CAAC,MAAM,EAAE,CAAA;SACjB;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;;QAGlB,KAAqB,IAAA,EAAA,GAAA,CAAY,EAAZ,EAAA,GAAA,IAAI,CAAC,OAAO,EAAZ,EAAY,GAAA,EAAA,CAAA,MAAA,EAAZ,EAAY,EAAA,EAAE;AAA9B,YAAA,IAAM,MAAM,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACf,YAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAA;YAEtB,IAAI,OAAO,EAAE;AACX,gBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;aACpB;SACF;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,UAAsB,EAAsB,EAAE,EAAsB,EAAA;AAClE,QAAA,IAAM,CAAC,GAAG,IAAI,CAAA;QAEd,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,CAAA;QACpD,IAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,CAAA;AAEpD,QAAA,IAAM,CAAC,GACL,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AACvC,YAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;AAClC,gBAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;AACpC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAA;QAEtB,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;KAC5D,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,MAAsB,EAAE,MAAgC,EAAA;AACvE,QAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;QAErC,IAAI,QAAQ,EAAE;AACZ,YAAA,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;SACjC;AAED,QAAA,OAAO,KAAK,CAAA;KACb,CAAA;IAED,SAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,MAAsB,EAAA;AACxC,QAAA,IAAI,OAAO,CAAA;AAEX,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAA;QAEpB,IAAI,cAAc,GAAG,IAAI,CAAA;QAEzB,KAA6B,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;AAAvC,YAAA,IAAM,cAAc,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;YACvB,OAAO,GAAG,cAAc,CAAA;AAExB,YAAA,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAA;AAElC,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;AAErC,YAAA,IAAI,MAAM,IAAI,QAAQ,EAAE;gBACtB,IAAM,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;AAEtD,gBAAA,IAAI,CAAC,GAAG,QAAQ,EAAE;oBAChB,QAAQ,GAAG,CAAC,CAAA;oBAEZ,cAAc,GAAG,OAAO,CAAA;iBACzB;aACF;SACF;QAED,IAAI,cAAc,IAAI,cAAc,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE;AACpE,YAAA,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;SACjC;aAAM;AACL,YAAA,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;AAE3B,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AAEzB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SAC5B;KACF,CAAA;IAED,SAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,MAAc,EAAA;QAA7B,IA+EC,KAAA,GAAA,IAAA,CAAA;AA9EC,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,OAAM;SACP;;AAGD,QAAA,IAAI,MAAM,KAAK,CAAC,EAAE;AAChB;;;;;;AAMG;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAA;AAExD,YAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;;;gBAIxC,OAAO,IAAI,CAAC,cAAc,CAAA;aAC3B;SACF;AAED,QAAA,IAAM,GAAG,GAAI,IAA2C,CAAC,MAAM,EAAE,CAAA;QAEjE,IAAM,MAAM,GAAG,GAAG,KAAK,IAAI,IAAI,WAAW,IAAI,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAA;AAE1E,QAAA,IAAM,IAAI,GAAI,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,OAAO,EAAE,KAAI,CAAC,CAAA;;;;;AAKjC,QAAA,IAAM,SAAS,GAAG,IAAI,GAAG,CAAC;cACpB,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAC1B,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,YAAY,EAAE,EACtB,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,YAAY,EAAE,CACvB;AACH,cAAE,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAC1B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,eAAe,CAAC,EAC3D,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAC5D,CAAA;QAEP,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;AAE3D,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;AAEpE,QAAA,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;AAE9B,YAAA,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE;AACvJ,gBAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;aACjC;SACF;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AAC/B,YAAA,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CACrC,YAAA;AACE,gBAAA,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;aAC3B,EACD,CAAC,CACF,CAAA;SACF;aAAM;AACL,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;AAE1B;;;;;;AAMG;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,CAAA;YAEtD,KAAsB,IAAA,EAAA,GAAA,CAAa,EAAb,EAAA,GAAA,IAAI,CAAC,QAAQ,EAAb,EAAa,GAAA,EAAA,CAAA,MAAA,EAAb,EAAa,EAAA,EAAE;AAAhC,gBAAA,IAAM,OAAO,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;gBAChB,OAAO,CAAC,UAAU,EAAE,CAAA;aACrB;SACF;KACF,CAAA;AAED,IAAA,SAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAwD,IAAO,EAAE,IAAoC,EAAA;QACnG,OAAO,SAAS,WAAW,CAAU,MAAsC,EAAA;AACzE,YAAA,KAAK,IAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;;gBAGvC,IAAM,IAAI,GAAG,QAAyD,CAAA;;;AAItE,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;aAC9C;AAED,YAAA,OAAO,IAAI,CAAA;SACZ,CAAC,KAAK,CAAyC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;KAC9D,CAAA;IACH,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;;;;;"}