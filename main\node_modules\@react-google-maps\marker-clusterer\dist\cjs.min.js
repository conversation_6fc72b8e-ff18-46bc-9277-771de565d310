"use strict";var t=function(){function t(e,i){e.getClusterer().extend(t,google.maps.OverlayView),this.cluster=e,this.clusterClassName=this.cluster.getClusterer().getClusterClass(),this.className=this.clusterClassName,this.styles=i,this.center=void 0,this.div=null,this.sums=null,this.visible=!1,this.boundsChangedListener=null,this.url="",this.height=0,this.width=0,this.anchorText=[0,0],this.anchorIcon=[0,0],this.textColor="black",this.textSize=11,this.textDecoration="none",this.fontWeight="bold",this.fontStyle="normal",this.fontFamily="Arial,sans-serif",this.backgroundPosition="0 0",this.cMouseDownInCluster=null,this.cDraggingMapByCluster=null,this.timeOut=null,this.setMap(e.getMap()),this.onBoundsChanged=this.onBoundsChanged.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onClick=this.onClick.bind(this),this.onMouseOver=this.onMouseOver.bind(this),this.onMouseOut=this.onMouseOut.bind(this),this.onAdd=this.onAdd.bind(this),this.onRemove=this.onRemove.bind(this),this.draw=this.draw.bind(this),this.hide=this.hide.bind(this),this.show=this.show.bind(this),this.useStyle=this.useStyle.bind(this),this.setCenter=this.setCenter.bind(this),this.getPosFromLatLng=this.getPosFromLatLng.bind(this)}return t.prototype.onBoundsChanged=function(){this.cDraggingMapByCluster=this.cMouseDownInCluster},t.prototype.onMouseDown=function(){this.cMouseDownInCluster=!0,this.cDraggingMapByCluster=!1},t.prototype.onClick=function(t){if(this.cMouseDownInCluster=!1,!this.cDraggingMapByCluster){var e=this.cluster.getClusterer();if(google.maps.event.trigger(e,"click",this.cluster),google.maps.event.trigger(e,"clusterclick",this.cluster),e.getZoomOnClick()){var i=e.getMaxZoom(),s=this.cluster.getBounds(),n=e.getMap();null!==n&&"fitBounds"in n&&n.fitBounds(s),this.timeOut=window.setTimeout((function(){var t=e.getMap();if(null!==t){"fitBounds"in t&&t.fitBounds(s);var n=t.getZoom()||0;null!==i&&n>i&&t.setZoom(i+1)}}),100)}t.cancelBubble=!0,t.stopPropagation&&t.stopPropagation()}},t.prototype.onMouseOver=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseover",this.cluster)},t.prototype.onMouseOut=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseout",this.cluster)},t.prototype.onAdd=function(){var t;this.div=document.createElement("div"),this.div.className=this.className,this.visible&&this.show(),null===(t=this.getPanes())||void 0===t||t.overlayMouseTarget.appendChild(this.div);var e=this.getMap();null!==e&&(this.boundsChangedListener=google.maps.event.addListener(e,"bounds_changed",this.onBoundsChanged),this.div.addEventListener("mousedown",this.onMouseDown),this.div.addEventListener("click",this.onClick),this.div.addEventListener("mouseover",this.onMouseOver),this.div.addEventListener("mouseout",this.onMouseOut))},t.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.hide(),null!==this.boundsChangedListener&&google.maps.event.removeListener(this.boundsChangedListener),this.div.removeEventListener("mousedown",this.onMouseDown),this.div.removeEventListener("click",this.onClick),this.div.removeEventListener("mouseover",this.onMouseOver),this.div.removeEventListener("mouseout",this.onMouseOut),this.div.parentNode.removeChild(this.div),null!==this.timeOut&&(window.clearTimeout(this.timeOut),this.timeOut=null),this.div=null)},t.prototype.draw=function(){if(this.visible&&null!==this.div&&this.center){var t=this.getPosFromLatLng(this.center);this.div.style.top=null!==t?"".concat(t.y,"px"):"0",this.div.style.left=null!==t?"".concat(t.x,"px"):"0"}},t.prototype.hide=function(){this.div&&(this.div.style.display="none"),this.visible=!1},t.prototype.show=function(){var t,e,i,s,n,o;if(this.div&&this.center){var r=null===this.sums||void 0===this.sums.title||""===this.sums.title?this.cluster.getClusterer().getTitle():this.sums.title,h=this.backgroundPosition.split(" "),a=parseInt((null===(t=h[0])||void 0===t?void 0:t.replace(/^\s+|\s+$/g,""))||"0",10),l=parseInt((null===(e=h[1])||void 0===e?void 0:e.replace(/^\s+|\s+$/g,""))||"0",10),u=this.getPosFromLatLng(this.center);this.div.className=this.className,this.div.setAttribute("style","cursor: pointer; position: absolute; top: ".concat(null!==u?"".concat(u.y,"px"):"0","; left: ").concat(null!==u?"".concat(u.x,"px"):"0","; width: ").concat(this.width,"px; height: ").concat(this.height,"px; "));var d=document.createElement("img");d.alt=r,d.src=this.url,d.width=this.width,d.height=this.height,d.setAttribute("style","position: absolute; top: ".concat(l,"px; left: ").concat(a,"px")),this.cluster.getClusterer().enableRetinaIcons||(d.style.clip="rect(-".concat(l,"px, -").concat(a+this.width,"px, -").concat(l+this.height,", -").concat(a,")"));var c=document.createElement("div");c.setAttribute("style","position: absolute; top: ".concat(this.anchorText[0],"px; left: ").concat(this.anchorText[1],"px; color: ").concat(this.textColor,"; font-size: ").concat(this.textSize,"px; font-family: ").concat(this.fontFamily,"; font-weight: ").concat(this.fontWeight,"; fontStyle: ").concat(this.fontStyle,"; text-decoration: ").concat(this.textDecoration,"; text-align: center; width: ").concat(this.width,"px; line-height: ").concat(this.height,"px")),(null===(i=this.sums)||void 0===i?void 0:i.text)&&(c.innerText="".concat(null===(s=this.sums)||void 0===s?void 0:s.text)),(null===(n=this.sums)||void 0===n?void 0:n.html)&&(c.innerHTML="".concat(null===(o=this.sums)||void 0===o?void 0:o.html)),this.div.innerHTML="",this.div.appendChild(d),this.div.appendChild(c),this.div.title=r,this.div.style.display=""}this.visible=!0},t.prototype.useStyle=function(t){this.sums=t;var e=this.cluster.getClusterer().getStyles(),i=e[Math.min(e.length-1,Math.max(0,t.index-1))];i&&(this.url=i.url,this.height=i.height,this.width=i.width,i.className&&(this.className="".concat(this.clusterClassName," ").concat(i.className)),this.anchorText=i.anchorText||[0,0],this.anchorIcon=i.anchorIcon||[this.height/2,this.width/2],this.textColor=i.textColor||"black",this.textSize=i.textSize||11,this.textDecoration=i.textDecoration||"none",this.fontWeight=i.fontWeight||"bold",this.fontStyle=i.fontStyle||"normal",this.fontFamily=i.fontFamily||"Arial,sans-serif",this.backgroundPosition=i.backgroundPosition||"0 0")},t.prototype.setCenter=function(t){this.center=t},t.prototype.getPosFromLatLng=function(t){var e=this.getProjection().fromLatLngToDivPixel(t);return null!==e&&(e.x-=this.anchorIcon[1],e.y-=this.anchorIcon[0]),e},t}(),e=function(){function e(e){this.markerClusterer=e,this.map=this.markerClusterer.getMap(),this.gridSize=this.markerClusterer.getGridSize(),this.minClusterSize=this.markerClusterer.getMinimumClusterSize(),this.averageCenter=this.markerClusterer.getAverageCenter(),this.markers=[],this.center=void 0,this.bounds=null,this.clusterIcon=new t(this,this.markerClusterer.getStyles()),this.getSize=this.getSize.bind(this),this.getMarkers=this.getMarkers.bind(this),this.getCenter=this.getCenter.bind(this),this.getMap=this.getMap.bind(this),this.getClusterer=this.getClusterer.bind(this),this.getBounds=this.getBounds.bind(this),this.remove=this.remove.bind(this),this.addMarker=this.addMarker.bind(this),this.isMarkerInClusterBounds=this.isMarkerInClusterBounds.bind(this),this.calculateBounds=this.calculateBounds.bind(this),this.updateIcon=this.updateIcon.bind(this),this.isMarkerAlreadyAdded=this.isMarkerAlreadyAdded.bind(this)}return e.prototype.getSize=function(){return this.markers.length},e.prototype.getMarkers=function(){return this.markers},e.prototype.getCenter=function(){return this.center},e.prototype.getMap=function(){return this.map},e.prototype.getClusterer=function(){return this.markerClusterer},e.prototype.getBounds=function(){for(var t=new google.maps.LatLngBounds(this.center,this.center),e=0,i=this.getMarkers();e<i.length;e++){var s=i[e].getPosition();s&&t.extend(s)}return t},e.prototype.remove=function(){this.clusterIcon.setMap(null),this.markers=[],delete this.markers},e.prototype.addMarker=function(t){var e,i;if(this.isMarkerAlreadyAdded(t))return!1;if(this.center){if(this.averageCenter&&(i=t.getPosition())){var s=this.markers.length+1;this.center=new google.maps.LatLng((this.center.lat()*(s-1)+i.lat())/s,(this.center.lng()*(s-1)+i.lng())/s),this.calculateBounds()}}else(i=t.getPosition())&&(this.center=i,this.calculateBounds());t.isAdded=!0,this.markers.push(t);var n=this.markers.length,o=this.markerClusterer.getMaxZoom(),r=null===(e=this.map)||void 0===e?void 0:e.getZoom();if(null!==o&&void 0!==r&&r>o)t.getMap()!==this.map&&t.setMap(this.map);else if(n<this.minClusterSize)t.getMap()!==this.map&&t.setMap(this.map);else if(n===this.minClusterSize)for(var h=0,a=this.markers;h<a.length;h++){a[h].setMap(null)}else t.setMap(null);return!0},e.prototype.isMarkerInClusterBounds=function(t){if(null!==this.bounds){var e=t.getPosition();if(e)return this.bounds.contains(e)}return!1},e.prototype.calculateBounds=function(){this.bounds=this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center,this.center))},e.prototype.updateIcon=function(){var t,e=this.markers.length,i=this.markerClusterer.getMaxZoom(),s=null===(t=this.map)||void 0===t?void 0:t.getZoom();null!==i&&void 0!==s&&s>i||e<this.minClusterSize?this.clusterIcon.hide():(this.center&&this.clusterIcon.setCenter(this.center),this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers,this.markerClusterer.getStyles().length)),this.clusterIcon.show())},e.prototype.isMarkerAlreadyAdded=function(t){if(this.markers.includes)return this.markers.includes(t);for(var e=0;e<this.markers.length;e++)if(t===this.markers[e])return!0;return!1},e}();function i(t,e){var i=t.length,s=i.toString().length,n=Math.min(s,e);return{text:i.toString(),index:n,title:""}}var s=[53,56,66,78,90],n=function(){function t(e,n,o){void 0===n&&(n=[]),void 0===o&&(o={}),this.getMinimumClusterSize=this.getMinimumClusterSize.bind(this),this.setMinimumClusterSize=this.setMinimumClusterSize.bind(this),this.getEnableRetinaIcons=this.getEnableRetinaIcons.bind(this),this.setEnableRetinaIcons=this.setEnableRetinaIcons.bind(this),this.addToClosestCluster=this.addToClosestCluster.bind(this),this.getImageExtension=this.getImageExtension.bind(this),this.setImageExtension=this.setImageExtension.bind(this),this.getExtendedBounds=this.getExtendedBounds.bind(this),this.getAverageCenter=this.getAverageCenter.bind(this),this.setAverageCenter=this.setAverageCenter.bind(this),this.getTotalClusters=this.getTotalClusters.bind(this),this.fitMapToMarkers=this.fitMapToMarkers.bind(this),this.getIgnoreHidden=this.getIgnoreHidden.bind(this),this.setIgnoreHidden=this.setIgnoreHidden.bind(this),this.getClusterClass=this.getClusterClass.bind(this),this.setClusterClass=this.setClusterClass.bind(this),this.getTotalMarkers=this.getTotalMarkers.bind(this),this.getZoomOnClick=this.getZoomOnClick.bind(this),this.setZoomOnClick=this.setZoomOnClick.bind(this),this.getBatchSizeIE=this.getBatchSizeIE.bind(this),this.setBatchSizeIE=this.setBatchSizeIE.bind(this),this.createClusters=this.createClusters.bind(this),this.onZoomChanged=this.onZoomChanged.bind(this),this.getImageSizes=this.getImageSizes.bind(this),this.setImageSizes=this.setImageSizes.bind(this),this.getCalculator=this.getCalculator.bind(this),this.setCalculator=this.setCalculator.bind(this),this.removeMarkers=this.removeMarkers.bind(this),this.resetViewport=this.resetViewport.bind(this),this.getImagePath=this.getImagePath.bind(this),this.setImagePath=this.setImagePath.bind(this),this.pushMarkerTo=this.pushMarkerTo.bind(this),this.removeMarker=this.removeMarker.bind(this),this.clearMarkers=this.clearMarkers.bind(this),this.setupStyles=this.setupStyles.bind(this),this.getGridSize=this.getGridSize.bind(this),this.setGridSize=this.setGridSize.bind(this),this.getClusters=this.getClusters.bind(this),this.getMaxZoom=this.getMaxZoom.bind(this),this.setMaxZoom=this.setMaxZoom.bind(this),this.getMarkers=this.getMarkers.bind(this),this.addMarkers=this.addMarkers.bind(this),this.getStyles=this.getStyles.bind(this),this.setStyles=this.setStyles.bind(this),this.addMarker=this.addMarker.bind(this),this.onRemove=this.onRemove.bind(this),this.getTitle=this.getTitle.bind(this),this.setTitle=this.setTitle.bind(this),this.repaint=this.repaint.bind(this),this.onIdle=this.onIdle.bind(this),this.redraw=this.redraw.bind(this),this.onAdd=this.onAdd.bind(this),this.draw=this.draw.bind(this),this.extend=this.extend.bind(this),this.extend(t,google.maps.OverlayView),this.markers=[],this.clusters=[],this.listeners=[],this.activeMap=null,this.ready=!1,this.gridSize=o.gridSize||60,this.minClusterSize=o.minimumClusterSize||2,this.maxZoom=o.maxZoom||null,this.styles=o.styles||[],this.title=o.title||"",this.zoomOnClick=!0,void 0!==o.zoomOnClick&&(this.zoomOnClick=o.zoomOnClick),this.averageCenter=!1,void 0!==o.averageCenter&&(this.averageCenter=o.averageCenter),this.ignoreHidden=!1,void 0!==o.ignoreHidden&&(this.ignoreHidden=o.ignoreHidden),this.enableRetinaIcons=!1,void 0!==o.enableRetinaIcons&&(this.enableRetinaIcons=o.enableRetinaIcons),this.imagePath=o.imagePath||"https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",this.imageExtension=o.imageExtension||"png",this.imageSizes=o.imageSizes||s,this.calculator=o.calculator||i,this.batchSize=o.batchSize||2e3,this.batchSizeIE=o.batchSizeIE||500,this.clusterClass=o.clusterClass||"cluster",-1!==navigator.userAgent.toLowerCase().indexOf("msie")&&(this.batchSize=this.batchSizeIE),this.timerRefStatic=null,this.setupStyles(),this.addMarkers(n,!0),this.setMap(e)}return t.prototype.onZoomChanged=function(){var t,e;this.resetViewport(!1),(null===(t=this.getMap())||void 0===t?void 0:t.getZoom())!==(this.get("minZoom")||0)&&(null===(e=this.getMap())||void 0===e?void 0:e.getZoom())!==this.get("maxZoom")||google.maps.event.trigger(this,"idle")},t.prototype.onIdle=function(){this.redraw()},t.prototype.onAdd=function(){var t=this.getMap();this.activeMap=t,this.ready=!0,this.repaint(),null!==t&&(this.listeners=[google.maps.event.addListener(t,"zoom_changed",this.onZoomChanged),google.maps.event.addListener(t,"idle",this.onIdle)])},t.prototype.onRemove=function(){for(var t=0,e=this.markers;t<e.length;t++){var i=e[t];i.getMap()!==this.activeMap&&i.setMap(this.activeMap)}for(var s=0,n=this.clusters;s<n.length;s++){n[s].remove()}this.clusters=[];for(var o=0,r=this.listeners;o<r.length;o++){var h=r[o];google.maps.event.removeListener(h)}this.listeners=[],this.activeMap=null,this.ready=!1},t.prototype.draw=function(){},t.prototype.getMap=function(){return null},t.prototype.getPanes=function(){return null},t.prototype.getProjection=function(){return{fromContainerPixelToLatLng:function(){return null},fromDivPixelToLatLng:function(){return null},fromLatLngToContainerPixel:function(){return null},fromLatLngToDivPixel:function(){return null},getVisibleRegion:function(){return null},getWorldWidth:function(){return 0}}},t.prototype.setMap=function(){},t.prototype.addListener=function(){return{remove:function(){}}},t.prototype.bindTo=function(){},t.prototype.get=function(){},t.prototype.notify=function(){},t.prototype.set=function(){},t.prototype.setValues=function(){},t.prototype.unbind=function(){},t.prototype.unbindAll=function(){},t.prototype.setupStyles=function(){if(!(this.styles.length>0))for(var t=0;t<this.imageSizes.length;t++)this.styles.push({url:"".concat(this.imagePath+(t+1),".").concat(this.imageExtension),height:this.imageSizes[t]||0,width:this.imageSizes[t]||0})},t.prototype.fitMapToMarkers=function(){for(var t=this.getMarkers(),e=new google.maps.LatLngBounds,i=0,s=t;i<s.length;i++){var n=s[i].getPosition();n&&e.extend(n)}var o=this.getMap();null!==o&&"fitBounds"in o&&o.fitBounds(e)},t.prototype.getGridSize=function(){return this.gridSize},t.prototype.setGridSize=function(t){this.gridSize=t},t.prototype.getMinimumClusterSize=function(){return this.minClusterSize},t.prototype.setMinimumClusterSize=function(t){this.minClusterSize=t},t.prototype.getMaxZoom=function(){return this.maxZoom},t.prototype.setMaxZoom=function(t){this.maxZoom=t},t.prototype.getStyles=function(){return this.styles},t.prototype.setStyles=function(t){this.styles=t},t.prototype.getTitle=function(){return this.title},t.prototype.setTitle=function(t){this.title=t},t.prototype.getZoomOnClick=function(){return this.zoomOnClick},t.prototype.setZoomOnClick=function(t){this.zoomOnClick=t},t.prototype.getAverageCenter=function(){return this.averageCenter},t.prototype.setAverageCenter=function(t){this.averageCenter=t},t.prototype.getIgnoreHidden=function(){return this.ignoreHidden},t.prototype.setIgnoreHidden=function(t){this.ignoreHidden=t},t.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons},t.prototype.setEnableRetinaIcons=function(t){this.enableRetinaIcons=t},t.prototype.getImageExtension=function(){return this.imageExtension},t.prototype.setImageExtension=function(t){this.imageExtension=t},t.prototype.getImagePath=function(){return this.imagePath},t.prototype.setImagePath=function(t){this.imagePath=t},t.prototype.getImageSizes=function(){return this.imageSizes},t.prototype.setImageSizes=function(t){this.imageSizes=t},t.prototype.getCalculator=function(){return this.calculator},t.prototype.setCalculator=function(t){this.calculator=t},t.prototype.getBatchSizeIE=function(){return this.batchSizeIE},t.prototype.setBatchSizeIE=function(t){this.batchSizeIE=t},t.prototype.getClusterClass=function(){return this.clusterClass},t.prototype.setClusterClass=function(t){this.clusterClass=t},t.prototype.getMarkers=function(){return this.markers},t.prototype.getTotalMarkers=function(){return this.markers.length},t.prototype.getClusters=function(){return this.clusters},t.prototype.getTotalClusters=function(){return this.clusters.length},t.prototype.addMarker=function(t,e){this.pushMarkerTo(t),e||this.redraw()},t.prototype.addMarkers=function(t,e){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var s=t[i];s&&this.pushMarkerTo(s)}e||this.redraw()},t.prototype.pushMarkerTo=function(t){var e=this;t.getDraggable()&&google.maps.event.addListener(t,"dragend",(function(){e.ready&&(t.isAdded=!1,e.repaint())})),t.isAdded=!1,this.markers.push(t)},t.prototype.removeMarker_=function(t){var e=-1;if(this.markers.indexOf)e=this.markers.indexOf(t);else for(var i=0;i<this.markers.length;i++)if(t===this.markers[i]){e=i;break}return-1!==e&&(t.setMap(null),this.markers.splice(e,1),!0)},t.prototype.removeMarker=function(t,e){var i=this.removeMarker_(t);return!e&&i&&this.repaint(),i},t.prototype.removeMarkers=function(t,e){for(var i=!1,s=0,n=t;s<n.length;s++){var o=n[s];i=i||this.removeMarker_(o)}return!e&&i&&this.repaint(),i},t.prototype.clearMarkers=function(){this.resetViewport(!0),this.markers=[]},t.prototype.repaint=function(){var t=this.clusters.slice();this.clusters=[],this.resetViewport(!1),this.redraw(),setTimeout((function(){for(var e=0,i=t;e<i.length;e++){i[e].remove()}}),0)},t.prototype.getExtendedBounds=function(t){var e=this.getProjection(),i=e.fromLatLngToDivPixel(new google.maps.LatLng(t.getNorthEast().lat(),t.getNorthEast().lng()));null!==i&&(i.x+=this.gridSize,i.y-=this.gridSize);var s=e.fromLatLngToDivPixel(new google.maps.LatLng(t.getSouthWest().lat(),t.getSouthWest().lng()));if(null!==s&&(s.x-=this.gridSize,s.y+=this.gridSize),null!==i){var n=e.fromDivPixelToLatLng(i);null!==n&&t.extend(n)}if(null!==s){var o=e.fromDivPixelToLatLng(s);null!==o&&t.extend(o)}return t},t.prototype.redraw=function(){this.createClusters(0)},t.prototype.resetViewport=function(t){for(var e=0,i=this.clusters;e<i.length;e++){i[e].remove()}this.clusters=[];for(var s=0,n=this.markers;s<n.length;s++){var o=n[s];o.isAdded=!1,t&&o.setMap(null)}},t.prototype.distanceBetweenPoints=function(t,e){var i=(e.lat()-t.lat())*Math.PI/180,s=(e.lng()-t.lng())*Math.PI/180,n=Math.sin(i/2)*Math.sin(i/2)+Math.cos(t.lat()*Math.PI/180)*Math.cos(e.lat()*Math.PI/180)*Math.sin(s/2)*Math.sin(s/2);return 2*Math.atan2(Math.sqrt(n),Math.sqrt(1-n))*6371},t.prototype.isMarkerInBounds=function(t,e){var i=t.getPosition();return!!i&&e.contains(i)},t.prototype.addToClosestCluster=function(t){for(var i,s=4e4,n=null,o=0,r=this.clusters;o<r.length;o++){var h=(i=r[o]).getCenter(),a=t.getPosition();if(h&&a){var l=this.distanceBetweenPoints(h,a);l<s&&(s=l,n=i)}}n&&n.isMarkerInClusterBounds(t)?n.addMarker(t):((i=new e(this)).addMarker(t),this.clusters.push(i))},t.prototype.createClusters=function(t){var e=this;if(this.ready){0===t&&(google.maps.event.trigger(this,"clusteringbegin",this),null!==this.timerRefStatic&&(window.clearTimeout(this.timerRefStatic),delete this.timerRefStatic));for(var i=this.getMap(),s=(null!==i&&"getBounds"in i?i.getBounds():null),n=((null==i?void 0:i.getZoom())||0)>3?new google.maps.LatLngBounds(null==s?void 0:s.getSouthWest(),null==s?void 0:s.getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625)),o=this.getExtendedBounds(n),r=Math.min(t+this.batchSize,this.markers.length),h=t;h<r;h++){var a=this.markers[h];a&&!a.isAdded&&this.isMarkerInBounds(a,o)&&(!this.ignoreHidden||this.ignoreHidden&&a.getVisible())&&this.addToClosestCluster(a)}if(r<this.markers.length)this.timerRefStatic=window.setTimeout((function(){e.createClusters(r)}),0);else{this.timerRefStatic=null,google.maps.event.trigger(this,"clusteringend",this);for(var l=0,u=this.clusters;l<u.length;l++){u[l].updateIcon()}}}},t.prototype.extend=function(t,e){return function(t){for(var e in t.prototype){var i=e;this.prototype[i]=t.prototype[i]}return this}.apply(t,[e])},t}();exports.Cluster=e,exports.ClusterIcon=t,exports.Clusterer=n;
//# sourceMappingURL=cjs.min.js.map
