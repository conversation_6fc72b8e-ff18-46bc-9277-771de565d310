{"version": 3, "file": "index.umd.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../src/marker-utils.ts", "../src/cluster.ts", "../src/algorithms/utils.ts", "../src/algorithms/core.ts", "../node_modules/fast-deep-equal/index.js", "../node_modules/kdbush/index.js", "../node_modules/supercluster/index.js", "../src/algorithms/supercluster.ts", "../src/renderer.ts", "../src/overlay-view-safe.ts", "../src/markerclusterer.ts", "../src/algorithms/grid.ts", "../src/algorithms/noop.ts", "../src/algorithms/superviewport.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Supports markers of either either \"legacy\" or \"advanced\" types.\n */\nexport type Marker =\n  | google.maps.Marker\n  | google.maps.marker.AdvancedMarkerElement;\n\n/**\n * util class that creates a common set of convenience functions to wrap\n * shared behavior of Advanced Markers and Markers.\n */\nexport class MarkerUtils {\n  public static isAdvancedMarkerAvailable(map: google.maps.Map): boolean {\n    return (\n      google.maps.marker &&\n      map.getMapCapabilities().isAdvancedMarkersAvailable === true\n    );\n  }\n\n  public static isAdvancedMarker(\n    marker: Marker\n  ): marker is google.maps.marker.AdvancedMarkerElement {\n    return (\n      google.maps.marker &&\n      marker instanceof google.maps.marker.AdvancedMarkerElement\n    );\n  }\n\n  public static setMap(marker: Marker, map: google.maps.Map | null) {\n    if (this.isAdvancedMarker(marker)) {\n      marker.map = map;\n    } else {\n      marker.setMap(map);\n    }\n  }\n\n  public static getPosition(marker: Marker): google.maps.LatLng {\n    // SuperClusterAlgorithm.calculate expects a LatLng instance so we fake it for Adv Markers\n    if (this.isAdvancedMarker(marker)) {\n      if (marker.position) {\n        if (marker.position instanceof google.maps.LatLng) {\n          return marker.position;\n        }\n        // since we can't cast to LatLngLiteral for reasons =(\n        if (marker.position.lat && marker.position.lng) {\n          return new google.maps.LatLng(\n            marker.position.lat,\n            marker.position.lng\n          );\n        }\n      }\n      return new google.maps.LatLng(null);\n    }\n    return marker.getPosition();\n  }\n\n  public static getVisible(marker: Marker) {\n    if (this.isAdvancedMarker(marker)) {\n      /**\n       * Always return true for Advanced Markers because the clusterer\n       * uses getVisible as a way to count legacy markers not as an actual\n       * indicator of visibility for some reason. Even when markers are hidden\n       * Marker.getVisible returns `true` and this is used to set the marker count\n       * on the cluster. See the behavior of Cluster.count\n       */\n      return true;\n    }\n    return marker.getVisible();\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MarkerUtils, Marker } from \"./marker-utils\";\n\nexport interface ClusterOptions {\n  position?: google.maps.LatLng | google.maps.LatLngLiteral;\n  markers?: Marker[];\n}\n\nexport class Cluster {\n  public marker?: Marker;\n  public readonly markers?: Marker[];\n  protected _position: google.maps.LatLng;\n\n  constructor({ markers, position }: ClusterOptions) {\n    this.markers = markers;\n\n    if (position) {\n      if (position instanceof google.maps.LatLng) {\n        this._position = position;\n      } else {\n        this._position = new google.maps.LatLng(position);\n      }\n    }\n  }\n\n  public get bounds(): google.maps.LatLngBounds | undefined {\n    if (this.markers.length === 0 && !this._position) {\n      return;\n    }\n\n    const bounds = new google.maps.LatLngBounds(this._position, this._position);\n    for (const marker of this.markers) {\n      bounds.extend(MarkerUtils.getPosition(marker));\n    }\n    return bounds;\n  }\n\n  public get position(): google.maps.LatLng {\n    return this._position || this.bounds.getCenter();\n  }\n\n  /**\n   * Get the count of **visible** markers.\n   */\n  public get count(): number {\n    return this.markers.filter((m: Marker) => MarkerUtils.getVisible(m)).length;\n  }\n\n  /**\n   * Add a marker to the cluster.\n   */\n  public push(marker: Marker): void {\n    this.markers.push(marker);\n  }\n\n  /**\n   * Cleanup references and remove marker from map.\n   */\n  public delete(): void {\n    if (this.marker) {\n      MarkerUtils.setMap(this.marker, null);\n      this.marker = undefined;\n    }\n    this.markers.length = 0;\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\n/**\n * Returns the markers visible in a padded map viewport\n *\n * @param map\n * @param mapCanvasProjection\n * @param markers The list of marker to filter\n * @param viewportPaddingPixels The padding in pixel\n * @returns The list of markers in the padded viewport\n */\nexport const filterMarkersToPaddedViewport = (\n  map: google.maps.Map,\n  mapCanvasProjection: google.maps.MapCanvasProjection,\n  markers: Marker[],\n  viewportPaddingPixels: number\n): Marker[] => {\n  const extendedMapBounds = extendBoundsToPaddedViewport(\n    map.getBounds(),\n    mapCanvasProjection,\n    viewportPaddingPixels\n  );\n  return markers.filter((marker) =>\n    extendedMapBounds.contains(MarkerUtils.getPosition(marker))\n  );\n};\n\n/**\n * Extends a bounds by a number of pixels in each direction\n */\nexport const extendBoundsToPaddedViewport = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection,\n  numPixels: number\n): google.maps.LatLngBounds => {\n  const { northEast, southWest } = latLngBoundsToPixelBounds(\n    bounds,\n    projection\n  );\n  const extendedPixelBounds = extendPixelBounds(\n    { northEast, southWest },\n    numPixels\n  );\n  return pixelBoundsToLatLngBounds(extendedPixelBounds, projection);\n};\n\n/**\n * Gets the extended bounds as a bbox [westLng, southLat, eastLng, northLat]\n */\nexport const getPaddedViewport = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection,\n  pixels: number\n): [number, number, number, number] => {\n  const extended = extendBoundsToPaddedViewport(bounds, projection, pixels);\n  const ne = extended.getNorthEast();\n  const sw = extended.getSouthWest();\n\n  return [sw.lng(), sw.lat(), ne.lng(), ne.lat()];\n};\n\n/**\n * Returns the distance between 2 positions.\n *\n * @hidden\n */\nexport const distanceBetweenPoints = (\n  p1: google.maps.LatLngLiteral,\n  p2: google.maps.LatLngLiteral\n): number => {\n  const R = 6371; // Radius of the Earth in km\n  const dLat = ((p2.lat - p1.lat) * Math.PI) / 180;\n  const dLon = ((p2.lng - p1.lng) * Math.PI) / 180;\n  const sinDLat = Math.sin(dLat / 2);\n  const sinDLon = Math.sin(dLon / 2);\n  const a =\n    sinDLat * sinDLat +\n    Math.cos((p1.lat * Math.PI) / 180) *\n      Math.cos((p2.lat * Math.PI) / 180) *\n      sinDLon *\n      sinDLon;\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  return R * c;\n};\n\ntype PixelBounds = {\n  northEast: google.maps.Point;\n  southWest: google.maps.Point;\n};\n\n/**\n * Converts a LatLng bound to pixels.\n *\n * @hidden\n */\nconst latLngBoundsToPixelBounds = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection\n): PixelBounds => {\n  return {\n    northEast: projection.fromLatLngToDivPixel(bounds.getNorthEast()),\n    southWest: projection.fromLatLngToDivPixel(bounds.getSouthWest()),\n  };\n};\n\n/**\n * Extends a pixel bounds by numPixels in all directions.\n *\n * @hidden\n */\nexport const extendPixelBounds = (\n  { northEast, southWest }: PixelBounds,\n  numPixels: number\n): PixelBounds => {\n  northEast.x += numPixels;\n  northEast.y -= numPixels;\n\n  southWest.x -= numPixels;\n  southWest.y += numPixels;\n\n  return { northEast, southWest };\n};\n\n/**\n * @hidden\n */\nexport const pixelBoundsToLatLngBounds = (\n  { northEast, southWest }: PixelBounds,\n  projection: google.maps.MapCanvasProjection\n): google.maps.LatLngBounds => {\n  const sw = projection.fromDivPixelToLatLng(southWest);\n  const ne = projection.fromDivPixelToLatLng(northEast);\n  return new google.maps.LatLngBounds(sw, ne);\n};\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Cluster } from \"../cluster\";\nimport { filterMarkersToPaddedViewport } from \"./utils\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\nexport interface AlgorithmInput {\n  /**\n   * The map containing the markers and clusters.\n   */\n  map: google.maps.Map;\n  /**\n   * An array of markers to be clustered.\n   *\n   * There are some specific edge cases to be aware of including the following:\n   * * Markers that are not visible.\n   */\n  markers: Marker[];\n  /**\n   * The `mapCanvasProjection` enables easy conversion from lat/lng to pixel.\n   *\n   * @see [MapCanvasProjection](https://developers.google.com/maps/documentation/javascript/reference/overlay-view#MapCanvasProjection)\n   */\n  mapCanvasProjection: google.maps.MapCanvasProjection;\n}\n\nexport interface AlgorithmOutput {\n  /**\n   * The clusters returned based upon the {@link AlgorithmInput}.\n   */\n  clusters: Cluster[];\n  /**\n   * A boolean flag indicating that the clusters have not changed.\n   */\n  changed?: boolean;\n}\n\nexport interface Algorithm {\n  /**\n   * Calculates an array of {@link Cluster}.\n   */\n  calculate: ({ markers, map }: AlgorithmInput) => AlgorithmOutput;\n}\n\nexport interface AlgorithmOptions {\n  // Markers are not clustered at maxZoom and above.\n  maxZoom?: number;\n}\n\n/**\n * @hidden\n */\nexport abstract class AbstractAlgorithm implements Algorithm {\n  protected maxZoom: number;\n\n  constructor({ maxZoom = 16 }: AlgorithmOptions) {\n    this.maxZoom = maxZoom;\n  }\n  /**\n   * Helper function to bypass clustering based upon some map state such as\n   * zoom, number of markers, etc.\n   *\n   * ```typescript\n   *  cluster({markers, map}: AlgorithmInput): Cluster[] {\n   *    if (shouldBypassClustering(map)) {\n   *      return this.noop({markers})\n   *    }\n   * }\n   * ```\n   */\n  protected noop<T extends Pick<AlgorithmInput, \"markers\">>({\n    markers,\n  }: T): Cluster[] {\n    return noop(markers);\n  }\n  /**\n   * Calculates an array of {@link Cluster}. Calculate is separate from\n   * {@link cluster} as it does preprocessing on the markers such as filtering\n   * based upon the viewport as in {@link AbstractViewportAlgorithm}. Caching\n   * and other optimizations can also be done here.\n   */\n  public abstract calculate({ markers, map }: AlgorithmInput): AlgorithmOutput;\n\n  /**\n   * Clusters the markers and called from {@link calculate}.\n   */\n  protected abstract cluster({ markers, map }: AlgorithmInput): Cluster[];\n}\n\n/**\n * @hidden\n */\nexport interface ViewportAlgorithmOptions extends AlgorithmOptions {\n  /**\n   * The number of pixels to extend beyond the viewport bounds when filtering\n   * markers prior to clustering.\n   */\n  viewportPadding?: number;\n}\n\n/**\n * Abstract viewport algorithm proves a class to filter markers by a padded\n * viewport. This is a common optimization.\n *\n * @hidden\n */\nexport abstract class AbstractViewportAlgorithm extends AbstractAlgorithm {\n  protected viewportPadding = 60;\n\n  constructor({ viewportPadding = 60, ...options }: ViewportAlgorithmOptions) {\n    super(options);\n    this.viewportPadding = viewportPadding;\n  }\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers,\n        }),\n        changed: false,\n      };\n    }\n\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(\n          map,\n          mapCanvasProjection,\n          markers,\n          this.viewportPadding\n        ),\n        map,\n        mapCanvasProjection,\n      }),\n    };\n  }\n  protected abstract cluster({ markers, map }: AlgorithmInput): Cluster[];\n}\n\n/**\n * @hidden\n */\nexport const noop = (markers: Marker[]): Cluster[] => {\n  const clusters = markers.map(\n    (marker) =>\n      new Cluster({\n        position: MarkerUtils.getPosition(marker),\n        markers: [marker],\n      })\n  );\n  return clusters;\n};\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "\nconst ARRAY_TYPES = [\n    Int8Array, Uint8Array, Uint8ClampedArray, Int16Array, Uint16Array,\n    Int32Array, Uint32Array, Float32Array, Float64Array\n];\n\n/** @typedef {Int8ArrayConstructor | Uint8ArrayConstructor | Uint8ClampedArrayConstructor | Int16ArrayConstructor | Uint16ArrayConstructor | Int32ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor} TypedArrayConstructor */\n\nconst VERSION = 1; // serialized format version\nconst HEADER_SIZE = 8;\n\nexport default class KDBush {\n\n    /**\n     * Creates an index from raw `ArrayBuffer` data.\n     * @param {ArrayBuffer} data\n     */\n    static from(data) {\n        if (!(data instanceof ArrayBuffer)) {\n            throw new Error('Data must be an instance of ArrayBuffer.');\n        }\n        const [magic, versionAndType] = new Uint8Array(data, 0, 2);\n        if (magic !== 0xdb) {\n            throw new Error('Data does not appear to be in a KDBush format.');\n        }\n        const version = versionAndType >> 4;\n        if (version !== VERSION) {\n            throw new Error(`Got v${version} data when expected v${VERSION}.`);\n        }\n        const ArrayType = ARRAY_TYPES[versionAndType & 0x0f];\n        if (!ArrayType) {\n            throw new Error('Unrecognized array type.');\n        }\n        const [nodeSize] = new Uint16Array(data, 2, 1);\n        const [numItems] = new Uint32Array(data, 4, 1);\n\n        return new KDBush(numItems, nodeSize, ArrayType, data);\n    }\n\n    /**\n     * Creates an index that will hold a given number of items.\n     * @param {number} numItems\n     * @param {number} [nodeSize=64] Size of the KD-tree node (64 by default).\n     * @param {TypedArrayConstructor} [ArrayType=Float64Array] The array type used for coordinates storage (`Float64Array` by default).\n     * @param {ArrayBuffer} [data] (For internal use only)\n     */\n    constructor(numItems, nodeSize = 64, ArrayType = Float64Array, data) {\n        if (isNaN(numItems) || numItems < 0) throw new Error(`Unpexpected numItems value: ${numItems}.`);\n\n        this.numItems = +numItems;\n        this.nodeSize = Math.min(Math.max(+nodeSize, 2), 65535);\n        this.ArrayType = ArrayType;\n        this.IndexArrayType = numItems < 65536 ? Uint16Array : Uint32Array;\n\n        const arrayTypeIndex = ARRAY_TYPES.indexOf(this.ArrayType);\n        const coordsByteSize = numItems * 2 * this.ArrayType.BYTES_PER_ELEMENT;\n        const idsByteSize = numItems * this.IndexArrayType.BYTES_PER_ELEMENT;\n        const padCoords = (8 - idsByteSize % 8) % 8;\n\n        if (arrayTypeIndex < 0) {\n            throw new Error(`Unexpected typed array class: ${ArrayType}.`);\n        }\n\n        if (data && (data instanceof ArrayBuffer)) { // reconstruct an index from a buffer\n            this.data = data;\n            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n            this._pos = numItems * 2;\n            this._finished = true;\n        } else { // initialize a new index\n            this.data = new ArrayBuffer(HEADER_SIZE + coordsByteSize + idsByteSize + padCoords);\n            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n            this._pos = 0;\n            this._finished = false;\n\n            // set header\n            new Uint8Array(this.data, 0, 2).set([0xdb, (VERSION << 4) + arrayTypeIndex]);\n            new Uint16Array(this.data, 2, 1)[0] = nodeSize;\n            new Uint32Array(this.data, 4, 1)[0] = numItems;\n        }\n    }\n\n    /**\n     * Add a point to the index.\n     * @param {number} x\n     * @param {number} y\n     * @returns {number} An incremental index associated with the added item (starting from `0`).\n     */\n    add(x, y) {\n        const index = this._pos >> 1;\n        this.ids[index] = index;\n        this.coords[this._pos++] = x;\n        this.coords[this._pos++] = y;\n        return index;\n    }\n\n    /**\n     * Perform indexing of the added points.\n     */\n    finish() {\n        const numAdded = this._pos >> 1;\n        if (numAdded !== this.numItems) {\n            throw new Error(`Added ${numAdded} items when expected ${this.numItems}.`);\n        }\n        // kd-sort both arrays for efficient search\n        sort(this.ids, this.coords, this.nodeSize, 0, this.numItems - 1, 0);\n\n        this._finished = true;\n        return this;\n    }\n\n    /**\n     * Search the index for items within a given bounding box.\n     * @param {number} minX\n     * @param {number} minY\n     * @param {number} maxX\n     * @param {number} maxY\n     * @returns {number[]} An array of indices correponding to the found items.\n     */\n    range(minX, minY, maxX, maxY) {\n        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n\n        const {ids, coords, nodeSize} = this;\n        const stack = [0, ids.length - 1, 0];\n        const result = [];\n\n        // recursively search for items in range in the kd-sorted arrays\n        while (stack.length) {\n            const axis = stack.pop() || 0;\n            const right = stack.pop() || 0;\n            const left = stack.pop() || 0;\n\n            // if we reached \"tree node\", search linearly\n            if (right - left <= nodeSize) {\n                for (let i = left; i <= right; i++) {\n                    const x = coords[2 * i];\n                    const y = coords[2 * i + 1];\n                    if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[i]);\n                }\n                continue;\n            }\n\n            // otherwise find the middle index\n            const m = (left + right) >> 1;\n\n            // include the middle item if it's in range\n            const x = coords[2 * m];\n            const y = coords[2 * m + 1];\n            if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[m]);\n\n            // queue search in halves that intersect the query\n            if (axis === 0 ? minX <= x : minY <= y) {\n                stack.push(left);\n                stack.push(m - 1);\n                stack.push(1 - axis);\n            }\n            if (axis === 0 ? maxX >= x : maxY >= y) {\n                stack.push(m + 1);\n                stack.push(right);\n                stack.push(1 - axis);\n            }\n        }\n\n        return result;\n    }\n\n    /**\n     * Search the index for items within a given radius.\n     * @param {number} qx\n     * @param {number} qy\n     * @param {number} r Query radius.\n     * @returns {number[]} An array of indices correponding to the found items.\n     */\n    within(qx, qy, r) {\n        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n\n        const {ids, coords, nodeSize} = this;\n        const stack = [0, ids.length - 1, 0];\n        const result = [];\n        const r2 = r * r;\n\n        // recursively search for items within radius in the kd-sorted arrays\n        while (stack.length) {\n            const axis = stack.pop() || 0;\n            const right = stack.pop() || 0;\n            const left = stack.pop() || 0;\n\n            // if we reached \"tree node\", search linearly\n            if (right - left <= nodeSize) {\n                for (let i = left; i <= right; i++) {\n                    if (sqDist(coords[2 * i], coords[2 * i + 1], qx, qy) <= r2) result.push(ids[i]);\n                }\n                continue;\n            }\n\n            // otherwise find the middle index\n            const m = (left + right) >> 1;\n\n            // include the middle item if it's in range\n            const x = coords[2 * m];\n            const y = coords[2 * m + 1];\n            if (sqDist(x, y, qx, qy) <= r2) result.push(ids[m]);\n\n            // queue search in halves that intersect the query\n            if (axis === 0 ? qx - r <= x : qy - r <= y) {\n                stack.push(left);\n                stack.push(m - 1);\n                stack.push(1 - axis);\n            }\n            if (axis === 0 ? qx + r >= x : qy + r >= y) {\n                stack.push(m + 1);\n                stack.push(right);\n                stack.push(1 - axis);\n            }\n        }\n\n        return result;\n    }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} nodeSize\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction sort(ids, coords, nodeSize, left, right, axis) {\n    if (right - left <= nodeSize) return;\n\n    const m = (left + right) >> 1; // middle index\n\n    // sort ids and coords around the middle index so that the halves lie\n    // either left/right or top/bottom correspondingly (taking turns)\n    select(ids, coords, m, left, right, axis);\n\n    // recursively kd-sort first half and second half on the opposite axis\n    sort(ids, coords, nodeSize, left, m - 1, 1 - axis);\n    sort(ids, coords, nodeSize, m + 1, right, 1 - axis);\n}\n\n/**\n * Custom Floyd-Rivest selection algorithm: sort ids and coords so that\n * [left..k-1] items are smaller than k-th item (on either x or y axis)\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} k\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction select(ids, coords, k, left, right, axis) {\n\n    while (right > left) {\n        if (right - left > 600) {\n            const n = right - left + 1;\n            const m = k - left + 1;\n            const z = Math.log(n);\n            const s = 0.5 * Math.exp(2 * z / 3);\n            const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n            const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n            const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n            select(ids, coords, k, newLeft, newRight, axis);\n        }\n\n        const t = coords[2 * k + axis];\n        let i = left;\n        let j = right;\n\n        swapItem(ids, coords, left, k);\n        if (coords[2 * right + axis] > t) swapItem(ids, coords, left, right);\n\n        while (i < j) {\n            swapItem(ids, coords, i, j);\n            i++;\n            j--;\n            while (coords[2 * i + axis] < t) i++;\n            while (coords[2 * j + axis] > t) j--;\n        }\n\n        if (coords[2 * left + axis] === t) swapItem(ids, coords, left, j);\n        else {\n            j++;\n            swapItem(ids, coords, j, right);\n        }\n\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} i\n * @param {number} j\n */\nfunction swapItem(ids, coords, i, j) {\n    swap(ids, i, j);\n    swap(coords, 2 * i, 2 * j);\n    swap(coords, 2 * i + 1, 2 * j + 1);\n}\n\n/**\n * @param {InstanceType<TypedArrayConstructor>} arr\n * @param {number} i\n * @param {number} j\n */\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\n/**\n * @param {number} ax\n * @param {number} ay\n * @param {number} bx\n * @param {number} by\n */\nfunction sqDist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n", "\nimport KDBush from 'kdbush';\n\nconst defaultOptions = {\n    minZoom: 0,   // min zoom to generate clusters on\n    maxZoom: 16,  // max zoom level to cluster the points on\n    minPoints: 2, // minimum points to form a cluster\n    radius: 40,   // cluster radius in pixels\n    extent: 512,  // tile extent (radius is calculated relative to it)\n    nodeSize: 64, // size of the KD-tree leaf node, affects performance\n    log: false,   // whether to log timing info\n\n    // whether to generate numeric ids for input features (in vector tiles)\n    generateId: false,\n\n    // a reduce function for calculating custom cluster properties\n    reduce: null, // (accumulated, props) => { accumulated.sum += props.sum; }\n\n    // properties to use for individual points when running the reducer\n    map: props => props // props => ({sum: props.my_value})\n};\n\nconst fround = Math.fround || (tmp => ((x) => { tmp[0] = +x; return tmp[0]; }))(new Float32Array(1));\n\nconst OFFSET_ZOOM = 2;\nconst OFFSET_ID = 3;\nconst OFFSET_PARENT = 4;\nconst OFFSET_NUM = 5;\nconst OFFSET_PROP = 6;\n\nexport default class Supercluster {\n    constructor(options) {\n        this.options = Object.assign(Object.create(defaultOptions), options);\n        this.trees = new Array(this.options.maxZoom + 1);\n        this.stride = this.options.reduce ? 7 : 6;\n        this.clusterProps = [];\n    }\n\n    load(points) {\n        const {log, minZoom, maxZoom} = this.options;\n\n        if (log) console.time('total time');\n\n        const timerId = `prepare ${  points.length  } points`;\n        if (log) console.time(timerId);\n\n        this.points = points;\n\n        // generate a cluster object for each point and index input points into a KD-tree\n        const data = [];\n\n        for (let i = 0; i < points.length; i++) {\n            const p = points[i];\n            if (!p.geometry) continue;\n\n            const [lng, lat] = p.geometry.coordinates;\n            const x = fround(lngX(lng));\n            const y = fround(latY(lat));\n            // store internal point/cluster data in flat numeric arrays for performance\n            data.push(\n                x, y, // projected point coordinates\n                Infinity, // the last zoom the point was processed at\n                i, // index of the source feature in the original input array\n                -1, // parent cluster id\n                1 // number of points in a cluster\n            );\n            if (this.options.reduce) data.push(0); // noop\n        }\n        let tree = this.trees[maxZoom + 1] = this._createTree(data);\n\n        if (log) console.timeEnd(timerId);\n\n        // cluster points on max zoom, then cluster the results on previous zoom, etc.;\n        // results in a cluster hierarchy across zoom levels\n        for (let z = maxZoom; z >= minZoom; z--) {\n            const now = +Date.now();\n\n            // create a new set of clusters for the zoom and index them with a KD-tree\n            tree = this.trees[z] = this._createTree(this._cluster(tree, z));\n\n            if (log) console.log('z%d: %d clusters in %dms', z, tree.numItems, +Date.now() - now);\n        }\n\n        if (log) console.timeEnd('total time');\n\n        return this;\n    }\n\n    getClusters(bbox, zoom) {\n        let minLng = ((bbox[0] + 180) % 360 + 360) % 360 - 180;\n        const minLat = Math.max(-90, Math.min(90, bbox[1]));\n        let maxLng = bbox[2] === 180 ? 180 : ((bbox[2] + 180) % 360 + 360) % 360 - 180;\n        const maxLat = Math.max(-90, Math.min(90, bbox[3]));\n\n        if (bbox[2] - bbox[0] >= 360) {\n            minLng = -180;\n            maxLng = 180;\n        } else if (minLng > maxLng) {\n            const easternHem = this.getClusters([minLng, minLat, 180, maxLat], zoom);\n            const westernHem = this.getClusters([-180, minLat, maxLng, maxLat], zoom);\n            return easternHem.concat(westernHem);\n        }\n\n        const tree = this.trees[this._limitZoom(zoom)];\n        const ids = tree.range(lngX(minLng), latY(maxLat), lngX(maxLng), latY(minLat));\n        const data = tree.data;\n        const clusters = [];\n        for (const id of ids) {\n            const k = this.stride * id;\n            clusters.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n        }\n        return clusters;\n    }\n\n    getChildren(clusterId) {\n        const originId = this._getOriginId(clusterId);\n        const originZoom = this._getOriginZoom(clusterId);\n        const errorMsg = 'No cluster with the specified id.';\n\n        const tree = this.trees[originZoom];\n        if (!tree) throw new Error(errorMsg);\n\n        const data = tree.data;\n        if (originId * this.stride >= data.length) throw new Error(errorMsg);\n\n        const r = this.options.radius / (this.options.extent * Math.pow(2, originZoom - 1));\n        const x = data[originId * this.stride];\n        const y = data[originId * this.stride + 1];\n        const ids = tree.within(x, y, r);\n        const children = [];\n        for (const id of ids) {\n            const k = id * this.stride;\n            if (data[k + OFFSET_PARENT] === clusterId) {\n                children.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n            }\n        }\n\n        if (children.length === 0) throw new Error(errorMsg);\n\n        return children;\n    }\n\n    getLeaves(clusterId, limit, offset) {\n        limit = limit || 10;\n        offset = offset || 0;\n\n        const leaves = [];\n        this._appendLeaves(leaves, clusterId, limit, offset, 0);\n\n        return leaves;\n    }\n\n    getTile(z, x, y) {\n        const tree = this.trees[this._limitZoom(z)];\n        const z2 = Math.pow(2, z);\n        const {extent, radius} = this.options;\n        const p = radius / extent;\n        const top = (y - p) / z2;\n        const bottom = (y + 1 + p) / z2;\n\n        const tile = {\n            features: []\n        };\n\n        this._addTileFeatures(\n            tree.range((x - p) / z2, top, (x + 1 + p) / z2, bottom),\n            tree.data, x, y, z2, tile);\n\n        if (x === 0) {\n            this._addTileFeatures(\n                tree.range(1 - p / z2, top, 1, bottom),\n                tree.data, z2, y, z2, tile);\n        }\n        if (x === z2 - 1) {\n            this._addTileFeatures(\n                tree.range(0, top, p / z2, bottom),\n                tree.data, -1, y, z2, tile);\n        }\n\n        return tile.features.length ? tile : null;\n    }\n\n    getClusterExpansionZoom(clusterId) {\n        let expansionZoom = this._getOriginZoom(clusterId) - 1;\n        while (expansionZoom <= this.options.maxZoom) {\n            const children = this.getChildren(clusterId);\n            expansionZoom++;\n            if (children.length !== 1) break;\n            clusterId = children[0].properties.cluster_id;\n        }\n        return expansionZoom;\n    }\n\n    _appendLeaves(result, clusterId, limit, offset, skipped) {\n        const children = this.getChildren(clusterId);\n\n        for (const child of children) {\n            const props = child.properties;\n\n            if (props && props.cluster) {\n                if (skipped + props.point_count <= offset) {\n                    // skip the whole cluster\n                    skipped += props.point_count;\n                } else {\n                    // enter the cluster\n                    skipped = this._appendLeaves(result, props.cluster_id, limit, offset, skipped);\n                    // exit the cluster\n                }\n            } else if (skipped < offset) {\n                // skip a single point\n                skipped++;\n            } else {\n                // add a single point\n                result.push(child);\n            }\n            if (result.length === limit) break;\n        }\n\n        return skipped;\n    }\n\n    _createTree(data) {\n        const tree = new KDBush(data.length / this.stride | 0, this.options.nodeSize, Float32Array);\n        for (let i = 0; i < data.length; i += this.stride) tree.add(data[i], data[i + 1]);\n        tree.finish();\n        tree.data = data;\n        return tree;\n    }\n\n    _addTileFeatures(ids, data, x, y, z2, tile) {\n        for (const i of ids) {\n            const k = i * this.stride;\n            const isCluster = data[k + OFFSET_NUM] > 1;\n\n            let tags, px, py;\n            if (isCluster) {\n                tags = getClusterProperties(data, k, this.clusterProps);\n                px = data[k];\n                py = data[k + 1];\n            } else {\n                const p = this.points[data[k + OFFSET_ID]];\n                tags = p.properties;\n                const [lng, lat] = p.geometry.coordinates;\n                px = lngX(lng);\n                py = latY(lat);\n            }\n\n            const f = {\n                type: 1,\n                geometry: [[\n                    Math.round(this.options.extent * (px * z2 - x)),\n                    Math.round(this.options.extent * (py * z2 - y))\n                ]],\n                tags\n            };\n\n            // assign id\n            let id;\n            if (isCluster || this.options.generateId) {\n                // optionally generate id for points\n                id = data[k + OFFSET_ID];\n            } else {\n                // keep id if already assigned\n                id = this.points[data[k + OFFSET_ID]].id;\n            }\n\n            if (id !== undefined) f.id = id;\n\n            tile.features.push(f);\n        }\n    }\n\n    _limitZoom(z) {\n        return Math.max(this.options.minZoom, Math.min(Math.floor(+z), this.options.maxZoom + 1));\n    }\n\n    _cluster(tree, zoom) {\n        const {radius, extent, reduce, minPoints} = this.options;\n        const r = radius / (extent * Math.pow(2, zoom));\n        const data = tree.data;\n        const nextData = [];\n        const stride = this.stride;\n\n        // loop through each point\n        for (let i = 0; i < data.length; i += stride) {\n            // if we've already visited the point at this zoom level, skip it\n            if (data[i + OFFSET_ZOOM] <= zoom) continue;\n            data[i + OFFSET_ZOOM] = zoom;\n\n            // find all nearby points\n            const x = data[i];\n            const y = data[i + 1];\n            const neighborIds = tree.within(data[i], data[i + 1], r);\n\n            const numPointsOrigin = data[i + OFFSET_NUM];\n            let numPoints = numPointsOrigin;\n\n            // count the number of points in a potential cluster\n            for (const neighborId of neighborIds) {\n                const k = neighborId * stride;\n                // filter out neighbors that are already processed\n                if (data[k + OFFSET_ZOOM] > zoom) numPoints += data[k + OFFSET_NUM];\n            }\n\n            // if there were neighbors to merge, and there are enough points to form a cluster\n            if (numPoints > numPointsOrigin && numPoints >= minPoints) {\n                let wx = x * numPointsOrigin;\n                let wy = y * numPointsOrigin;\n\n                let clusterProperties;\n                let clusterPropIndex = -1;\n\n                // encode both zoom and point index on which the cluster originated -- offset by total length of features\n                const id = ((i / stride | 0) << 5) + (zoom + 1) + this.points.length;\n\n                for (const neighborId of neighborIds) {\n                    const k = neighborId * stride;\n\n                    if (data[k + OFFSET_ZOOM] <= zoom) continue;\n                    data[k + OFFSET_ZOOM] = zoom; // save the zoom (so it doesn't get processed twice)\n\n                    const numPoints2 = data[k + OFFSET_NUM];\n                    wx += data[k] * numPoints2; // accumulate coordinates for calculating weighted center\n                    wy += data[k + 1] * numPoints2;\n\n                    data[k + OFFSET_PARENT] = id;\n\n                    if (reduce) {\n                        if (!clusterProperties) {\n                            clusterProperties = this._map(data, i, true);\n                            clusterPropIndex = this.clusterProps.length;\n                            this.clusterProps.push(clusterProperties);\n                        }\n                        reduce(clusterProperties, this._map(data, k));\n                    }\n                }\n\n                data[i + OFFSET_PARENT] = id;\n                nextData.push(wx / numPoints, wy / numPoints, Infinity, id, -1, numPoints);\n                if (reduce) nextData.push(clusterPropIndex);\n\n            } else { // left points as unclustered\n                for (let j = 0; j < stride; j++) nextData.push(data[i + j]);\n\n                if (numPoints > 1) {\n                    for (const neighborId of neighborIds) {\n                        const k = neighborId * stride;\n                        if (data[k + OFFSET_ZOOM] <= zoom) continue;\n                        data[k + OFFSET_ZOOM] = zoom;\n                        for (let j = 0; j < stride; j++) nextData.push(data[k + j]);\n                    }\n                }\n            }\n        }\n\n        return nextData;\n    }\n\n    // get index of the point from which the cluster originated\n    _getOriginId(clusterId) {\n        return (clusterId - this.points.length) >> 5;\n    }\n\n    // get zoom of the point from which the cluster originated\n    _getOriginZoom(clusterId) {\n        return (clusterId - this.points.length) % 32;\n    }\n\n    _map(data, i, clone) {\n        if (data[i + OFFSET_NUM] > 1) {\n            const props = this.clusterProps[data[i + OFFSET_PROP]];\n            return clone ? Object.assign({}, props) : props;\n        }\n        const original = this.points[data[i + OFFSET_ID]].properties;\n        const result = this.options.map(original);\n        return clone && result === original ? Object.assign({}, result) : result;\n    }\n}\n\nfunction getClusterJSON(data, i, clusterProps) {\n    return {\n        type: 'Feature',\n        id: data[i + OFFSET_ID],\n        properties: getClusterProperties(data, i, clusterProps),\n        geometry: {\n            type: 'Point',\n            coordinates: [xLng(data[i]), yLat(data[i + 1])]\n        }\n    };\n}\n\nfunction getClusterProperties(data, i, clusterProps) {\n    const count = data[i + OFFSET_NUM];\n    const abbrev =\n        count >= 10000 ? `${Math.round(count / 1000)  }k` :\n        count >= 1000 ? `${Math.round(count / 100) / 10  }k` : count;\n    const propIndex = data[i + OFFSET_PROP];\n    const properties = propIndex === -1 ? {} : Object.assign({}, clusterProps[propIndex]);\n    return Object.assign(properties, {\n        cluster: true,\n        cluster_id: data[i + OFFSET_ID],\n        point_count: count,\n        point_count_abbreviated: abbrev\n    });\n}\n\n// longitude/latitude to spherical mercator in [0..1] range\nfunction lngX(lng) {\n    return lng / 360 + 0.5;\n}\nfunction latY(lat) {\n    const sin = Math.sin(lat * Math.PI / 180);\n    const y = (0.5 - 0.25 * Math.log((1 + sin) / (1 - sin)) / Math.PI);\n    return y < 0 ? 0 : y > 1 ? 1 : y;\n}\n\n// spherical mercator to longitude/latitude\nfunction xLng(x) {\n    return (x - 0.5) * 360;\n}\nfunction yLat(y) {\n    const y2 = (180 - y * 360) * Math.PI / 180;\n    return 360 * Math.atan(Math.exp(y2)) / Math.PI - 90;\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AbstractAlgorithm, AlgorithmInput, AlgorithmOutput } from \"./core\";\nimport SuperCluster, { ClusterFeature } from \"supercluster\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\nimport { Cluster } from \"../cluster\";\nimport equal from \"fast-deep-equal\";\n\nexport type SuperClusterOptions = SuperCluster.Options<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  { [name: string]: any },\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  { [name: string]: any }\n>;\n\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nexport class SuperClusterAlgorithm extends AbstractAlgorithm {\n  protected superCluster: SuperCluster;\n  protected markers: Marker[];\n  protected clusters: Cluster[];\n  protected state = { zoom: -1 };\n\n  constructor({ maxZoom, radius = 60, ...options }: SuperClusterOptions) {\n    super({ maxZoom });\n\n    this.superCluster = new SuperCluster({\n      maxZoom: this.maxZoom,\n      radius,\n      ...options,\n    });\n  }\n\n  public calculate(input: AlgorithmInput): AlgorithmOutput {\n    let changed = false;\n    const state = { zoom: input.map.getZoom() };\n\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n\n      const points = this.markers.map((marker) => {\n        const position = MarkerUtils.getPosition(marker);\n        const coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\" as const,\n          geometry: {\n            type: \"Point\" as const,\n            coordinates,\n          },\n          properties: { marker },\n        };\n      });\n      this.superCluster.load(points);\n    }\n\n    if (!changed) {\n      if (this.state.zoom <= this.maxZoom || state.zoom <= this.maxZoom) {\n        changed = !equal(this.state, state);\n      }\n    }\n\n    this.state = state;\n\n    if (changed) {\n      this.clusters = this.cluster(input);\n    }\n\n    return { clusters: this.clusters, changed };\n  }\n\n  public cluster({ map }: AlgorithmInput): Cluster[] {\n    return this.superCluster\n      .getClusters([-180, -90, 180, 90], Math.round(map.getZoom()))\n      .map((feature: ClusterFeature<{ marker: Marker }>) =>\n        this.transformCluster(feature)\n      );\n  }\n\n  protected transformCluster({\n    geometry: {\n      coordinates: [lng, lat],\n    },\n    properties,\n  }: ClusterFeature<{ marker: Marker }>): Cluster {\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster\n          .getLeaves(properties.cluster_id, Infinity)\n          .map((leaf) => leaf.properties.marker),\n        position: { lat, lng },\n      });\n    }\n\n    const marker = properties.marker;\n\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker),\n    });\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Cluster } from \"./cluster\";\nimport { Marker, MarkerUtils } from \"./marker-utils\";\n\n/**\n * Provides statistics on all clusters in the current render cycle for use in {@link Renderer.render}.\n */\nexport class ClusterStats {\n  public readonly markers: { sum: number };\n  public readonly clusters: {\n    count: number;\n    markers: {\n      mean: number;\n      sum: number;\n      min: number;\n      max: number;\n    };\n  };\n\n  constructor(markers: Marker[], clusters: Cluster[]) {\n    this.markers = { sum: markers.length };\n    const clusterMarkerCounts = clusters.map((a) => a.count);\n    const clusterMarkerSum = clusterMarkerCounts.reduce((a, b) => a + b, 0);\n\n    this.clusters = {\n      count: clusters.length,\n      markers: {\n        mean: clusterMarkerSum / clusters.length,\n        sum: clusterMarkerSum,\n        min: Math.min(...clusterMarkerCounts),\n        max: Math.max(...clusterMarkerCounts),\n      },\n    };\n  }\n}\n\nexport interface Renderer {\n  /**\n   * Turn a {@link Cluster} into a `Marker`.\n   *\n   * Below is a simple example to create a marker with the number of markers in the cluster as a label.\n   *\n   * ```typescript\n   * return new google.maps.Marker({\n   *   position,\n   *   label: String(markers.length),\n   * });\n   * ```\n   */\n  render(cluster: Cluster, stats: ClusterStats, map: google.maps.Map): Marker;\n}\n\nexport class DefaultRenderer implements Renderer {\n  /**\n   * The default render function for the library used by {@link MarkerClusterer}.\n   *\n   * Currently set to use the following:\n   *\n   * ```typescript\n   * // change color if this cluster has more markers than the mean cluster\n   * const color =\n   *   count > Math.max(10, stats.clusters.markers.mean)\n   *     ? \"#ff0000\"\n   *     : \"#0000ff\";\n   *\n   * // create svg url with fill color\n   * const svg = window.btoa(`\n   * <svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\">\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".1\" r=\"130\" />\n   * </svg>`);\n   *\n   * // create marker using svg icon\n   * return new google.maps.Marker({\n   *   position,\n   *   icon: {\n   *     url: `data:image/svg+xml;base64,${svg}`,\n   *     scaledSize: new google.maps.Size(45, 45),\n   *   },\n   *   label: {\n   *     text: String(count),\n   *     color: \"rgba(255,255,255,0.9)\",\n   *     fontSize: \"12px\",\n   *   },\n   *   // adjust zIndex to be above other markers\n   *   zIndex: 1000 + count,\n   * });\n   * ```\n   */\n  public render(\n    { count, position }: Cluster,\n    stats: ClusterStats,\n    map: google.maps.Map\n  ): Marker {\n    // change color if this cluster has more markers than the mean cluster\n    const color =\n      count > Math.max(10, stats.clusters.markers.mean) ? \"#ff0000\" : \"#0000ff\";\n\n    // create svg literal with fill color\n    const svg = `<svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\" width=\"50\" height=\"50\">\n<circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n<text x=\"50%\" y=\"50%\" style=\"fill:#fff\" text-anchor=\"middle\" font-size=\"50\" dominant-baseline=\"middle\" font-family=\"roboto,arial,sans-serif\">${count}</text>\n</svg>`;\n\n    const title = `Cluster of ${count} markers`,\n      // adjust zIndex to be above other markers\n      zIndex: number = Number(google.maps.Marker.MAX_ZINDEX) + count;\n\n    if (MarkerUtils.isAdvancedMarkerAvailable(map)) {\n      // create cluster SVG element\n      const parser = new DOMParser();\n      const svgEl = parser.parseFromString(\n        svg,\n        \"image/svg+xml\"\n      ).documentElement;\n      svgEl.setAttribute(\"transform\", \"translate(0 25)\");\n\n      const clusterOptions: google.maps.marker.AdvancedMarkerElementOptions = {\n        map,\n        position,\n        zIndex,\n        title,\n        content: svgEl,\n      };\n      return new google.maps.marker.AdvancedMarkerElement(clusterOptions);\n    }\n\n    const clusterOptions: google.maps.MarkerOptions = {\n      position,\n      zIndex,\n      title,\n      icon: {\n        url: `data:image/svg+xml;base64,${btoa(svg)}`,\n        anchor: new google.maps.Point(25, 25),\n      },\n    };\n    return new google.maps.Marker(clusterOptions);\n  }\n}\n", "/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface OverlayViewSafe extends google.maps.OverlayView {}\n\n/**\n * Extends an object's prototype by another's.\n *\n * @param type1 The Type to be extended.\n * @param type2 The Type to extend with.\n * @ignore\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction extend(type1: any, type2: any): void {\n  /* istanbul ignore next */\n  // eslint-disable-next-line prefer-const\n  for (let property in type2.prototype) {\n    type1.prototype[property] = type2.prototype[property];\n  }\n}\n\n/**\n * @ignore\n */\nexport class OverlayViewSafe {\n  constructor() {\n    // MarkerClusterer implements google.maps.OverlayView interface. We use the\n    // extend function to extend MarkerClusterer with google.maps.OverlayView\n    // because it might not always be available when the code is defined so we\n    // look for it at the last possible moment. If it doesn't exist now then\n    // there is no point going ahead :)\n    extend(OverlayViewSafe, google.maps.OverlayView);\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Algorithm,\n  AlgorithmOptions,\n  SuperClusterAlgorithm,\n} from \"./algorithms\";\nimport { ClusterStats, DefaultRenderer, Renderer } from \"./renderer\";\nimport { Cluster } from \"./cluster\";\nimport { OverlayViewSafe } from \"./overlay-view-safe\";\nimport { MarkerUtils, Marker } from \"./marker-utils\";\n\nexport type onClusterClickHandler = (\n  event: google.maps.MapMouseEvent,\n  cluster: Cluster,\n  map: google.maps.Map\n) => void;\nexport interface MarkerClustererOptions {\n  markers?: Marker[];\n  /**\n   * An algorithm to cluster markers. Default is {@link SuperClusterAlgorithm}. Must\n   * provide a `calculate` method accepting {@link AlgorithmInput} and returning\n   * an array of {@link Cluster}.\n   */\n  algorithm?: Algorithm;\n  algorithmOptions?: AlgorithmOptions;\n  map?: google.maps.Map | null;\n  /**\n   * An object that converts a {@link Cluster} into a `google.maps.Marker`.\n   * Default is {@link DefaultRenderer}.\n   */\n  renderer?: Renderer;\n  onClusterClick?: onClusterClickHandler;\n}\n\nexport enum MarkerClustererEvents {\n  CLUSTERING_BEGIN = \"clusteringbegin\",\n  CLUSTERING_END = \"clusteringend\",\n  CLUSTER_CLICK = \"click\",\n}\n\nexport const defaultOnClusterClickHandler: onClusterClickHandler = (\n  _: google.maps.MapMouseEvent,\n  cluster: Cluster,\n  map: google.maps.Map\n): void => {\n  map.fitBounds(cluster.bounds);\n};\n/**\n * MarkerClusterer creates and manages per-zoom-level clusters for large amounts\n * of markers. See {@link MarkerClustererOptions} for more details.\n *\n */\nexport class MarkerClusterer extends OverlayViewSafe {\n  /** @see {@link MarkerClustererOptions.onClusterClick} */\n  public onClusterClick: onClusterClickHandler;\n  /** @see {@link MarkerClustererOptions.algorithm} */\n  protected algorithm: Algorithm;\n  protected clusters: Cluster[];\n  protected markers: Marker[];\n  /** @see {@link MarkerClustererOptions.renderer} */\n  protected renderer: Renderer;\n  /** @see {@link MarkerClustererOptions.map} */\n  protected map: google.maps.Map | null;\n  protected idleListener: google.maps.MapsEventListener;\n\n  constructor({\n    map,\n    markers = [],\n    algorithmOptions = {},\n    algorithm = new SuperClusterAlgorithm(algorithmOptions),\n    renderer = new DefaultRenderer(),\n    onClusterClick = defaultOnClusterClickHandler,\n  }: MarkerClustererOptions) {\n    super();\n    this.markers = [...markers];\n    this.clusters = [];\n\n    this.algorithm = algorithm;\n    this.renderer = renderer;\n\n    this.onClusterClick = onClusterClick;\n\n    if (map) {\n      this.setMap(map);\n    }\n  }\n\n  public addMarker(marker: Marker, noDraw?: boolean): void {\n    if (this.markers.includes(marker)) {\n      return;\n    }\n\n    this.markers.push(marker);\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  public addMarkers(markers: Marker[], noDraw?: boolean): void {\n    markers.forEach((marker) => {\n      this.addMarker(marker, true);\n    });\n\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  public removeMarker(marker: Marker, noDraw?: boolean): boolean {\n    const index = this.markers.indexOf(marker);\n\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false;\n    }\n\n    MarkerUtils.setMap(marker, null);\n    this.markers.splice(index, 1); // Remove the marker from the list of managed markers\n\n    if (!noDraw) {\n      this.render();\n    }\n\n    return true;\n  }\n\n  public removeMarkers(markers: Marker[], noDraw?: boolean): boolean {\n    let removed = false;\n\n    markers.forEach((marker) => {\n      removed = this.removeMarker(marker, true) || removed;\n    });\n\n    if (removed && !noDraw) {\n      this.render();\n    }\n\n    return removed;\n  }\n\n  public clearMarkers(noDraw?: boolean): void {\n    this.markers.length = 0;\n\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  /**\n   * Recalculates and draws all the marker clusters.\n   */\n  public render(): void {\n    const map = this.getMap();\n    if (map instanceof google.maps.Map && map.getProjection()) {\n      google.maps.event.trigger(\n        this,\n        MarkerClustererEvents.CLUSTERING_BEGIN,\n        this\n      );\n      const { clusters, changed } = this.algorithm.calculate({\n        markers: this.markers,\n        map,\n        mapCanvasProjection: this.getProjection(),\n      });\n\n      // Allow algorithms to return flag on whether the clusters/markers have changed.\n      if (changed || changed == undefined) {\n        // Accumulate the markers of the clusters composed of a single marker.\n        // Those clusters directly use the marker.\n        // Clusters with more than one markers use a group marker generated by a renderer.\n        const singleMarker = new Set<Marker>();\n        for (const cluster of clusters) {\n          if (cluster.markers.length == 1) {\n            singleMarker.add(cluster.markers[0]);\n          }\n        }\n\n        const groupMarkers: Marker[] = [];\n        // Iterate the clusters that are currently rendered.\n        for (const cluster of this.clusters) {\n          if (cluster.marker == null) {\n            continue;\n          }\n          if (cluster.markers.length == 1) {\n            if (!singleMarker.has(cluster.marker)) {\n              // The marker:\n              // - was previously rendered because it is from a cluster with 1 marker,\n              // - should no more be rendered as it is not in singleMarker.\n              MarkerUtils.setMap(cluster.marker, null);\n            }\n          } else {\n            // Delay the removal of old group markers to avoid flickering.\n            groupMarkers.push(cluster.marker);\n          }\n        }\n\n        this.clusters = clusters;\n        this.renderClusters();\n\n        // Delayed removal of the markers of the former groups.\n        requestAnimationFrame(() =>\n          groupMarkers.forEach((marker) => MarkerUtils.setMap(marker, null))\n        );\n      }\n      google.maps.event.trigger(\n        this,\n        MarkerClustererEvents.CLUSTERING_END,\n        this\n      );\n    }\n  }\n\n  public onAdd(): void {\n    this.idleListener = this.getMap().addListener(\n      \"idle\",\n      this.render.bind(this)\n    );\n    this.render();\n  }\n\n  public onRemove(): void {\n    google.maps.event.removeListener(this.idleListener);\n    this.reset();\n  }\n\n  protected reset(): void {\n    this.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n    this.clusters.forEach((cluster) => cluster.delete());\n    this.clusters = [];\n  }\n\n  protected renderClusters(): void {\n    // Generate stats to pass to renderers.\n    const stats = new ClusterStats(this.markers, this.clusters);\n    const map = this.getMap() as google.maps.Map;\n\n    this.clusters.forEach((cluster) => {\n      if (cluster.markers.length === 1) {\n        cluster.marker = cluster.markers[0];\n      } else {\n        // Generate the marker to represent the group.\n        cluster.marker = this.renderer.render(cluster, stats, map);\n        // Make sure all individual markers are removed from the map.\n        cluster.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n        if (this.onClusterClick) {\n          cluster.marker.addListener(\n            \"click\",\n            /* istanbul ignore next */\n            (event: google.maps.MapMouseEvent) => {\n              google.maps.event.trigger(\n                this,\n                MarkerClustererEvents.CLUSTER_CLICK,\n                cluster\n              );\n              this.onClusterClick(event, cluster, map);\n            }\n          );\n        }\n      }\n      MarkerUtils.setMap(cluster.marker, map);\n    });\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractViewportAlgorithm,\n  AlgorithmInput,\n  AlgorithmOutput,\n  ViewportAlgorithmOptions,\n} from \"./core\";\nimport {\n  distanceBetweenPoints,\n  extendBoundsToPaddedViewport,\n  filterMarkersToPaddedViewport,\n} from \"./utils\";\n\nimport { Cluster } from \"../cluster\";\nimport equal from \"fast-deep-equal\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\nexport interface GridOptions extends ViewportAlgorithmOptions {\n  gridSize?: number;\n  /**\n   * Max distance between cluster center and point in meters.\n   * @default 10000\n   */\n  maxDistance?: number;\n}\n\n/**\n * The default Grid algorithm historically used in Google Maps marker\n * clustering.\n *\n * The Grid algorithm does not implement caching and markers may flash as the\n * viewport changes. Instead use {@link SuperClusterAlgorithm}.\n */\nexport class GridAlgorithm extends AbstractViewportAlgorithm {\n  protected gridSize: number;\n  protected maxDistance: number;\n  protected clusters: Cluster[] = [];\n  protected state = { zoom: -1 };\n\n  constructor({ maxDistance = 40000, gridSize = 40, ...options }: GridOptions) {\n    super(options);\n\n    this.maxDistance = maxDistance;\n    this.gridSize = gridSize;\n  }\n\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    const state = { zoom: map.getZoom() };\n    let changed = false;\n    if (this.state.zoom >= this.maxZoom && state.zoom >= this.maxZoom) {\n      // still at or beyond maxZoom, no change\n    } else {\n      changed = !equal(this.state, state);\n    }\n    this.state = state;\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers,\n        }),\n        changed,\n      };\n    }\n\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(\n          map,\n          mapCanvasProjection,\n          markers,\n          this.viewportPadding\n        ),\n        map,\n        mapCanvasProjection,\n      }),\n    };\n  }\n\n  protected cluster({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): Cluster[] {\n    this.clusters = [];\n    markers.forEach((marker) => {\n      this.addToClosestCluster(marker, map, mapCanvasProjection);\n    });\n\n    return this.clusters;\n  }\n\n  protected addToClosestCluster(\n    marker: Marker,\n    map: google.maps.Map,\n    projection: google.maps.MapCanvasProjection\n  ): void {\n    let maxDistance = this.maxDistance; // Some large number\n    let cluster: Cluster = null;\n\n    for (let i = 0; i < this.clusters.length; i++) {\n      const candidate = this.clusters[i];\n      const distance = distanceBetweenPoints(\n        candidate.bounds.getCenter().toJSON(),\n        MarkerUtils.getPosition(marker).toJSON()\n      );\n\n      if (distance < maxDistance) {\n        maxDistance = distance;\n        cluster = candidate;\n      }\n    }\n\n    if (\n      cluster &&\n      extendBoundsToPaddedViewport(\n        cluster.bounds,\n        projection,\n        this.gridSize\n      ).contains(MarkerUtils.getPosition(marker))\n    ) {\n      cluster.push(marker);\n    } else {\n      const cluster = new Cluster({ markers: [marker] });\n      this.clusters.push(cluster);\n    }\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractAlgorithm,\n  AlgorithmInput,\n  AlgorithmOptions,\n  AlgorithmOutput,\n} from \"./core\";\n\nimport { Cluster } from \"../cluster\";\n\n/**\n * Noop algorithm does not generate any clusters or filter markers by the an extended viewport.\n */\nexport class NoopAlgorithm extends AbstractAlgorithm {\n  constructor({ ...options }: AlgorithmOptions) {\n    super(options);\n  }\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    return {\n      clusters: this.cluster({ markers, map, mapCanvasProjection }),\n      changed: false,\n    };\n  }\n\n  protected cluster(input: AlgorithmInput): Cluster[] {\n    return this.noop(input);\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractViewportAlgorithm,\n  AlgorithmInput,\n  AlgorithmOutput,\n  ViewportAlgorithmOptions,\n} from \"./core\";\nimport { SuperClusterOptions } from \"./supercluster\";\nimport SuperCluster, { ClusterFeature } from \"supercluster\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\nimport { Cluster } from \"../cluster\";\nimport { getPaddedViewport } from \"./utils\";\nimport equal from \"fast-deep-equal\";\n\nexport interface SuperClusterViewportOptions\n  extends SuperClusterOptions,\n    ViewportAlgorithmOptions {}\n\nexport interface SuperClusterViewportState {\n  /* The current zoom level */\n  zoom: number;\n\n  /* The current viewport as a bbox [westLng, southLat, eastLng, northLat] */\n  view: [number, number, number, number];\n}\n\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nexport class SuperClusterViewportAlgorithm extends AbstractViewportAlgorithm {\n  protected superCluster: SuperCluster;\n  protected markers: Marker[];\n  protected clusters: Cluster[];\n  protected state: SuperClusterViewportState;\n\n  constructor({\n    maxZoom,\n    radius = 60,\n    viewportPadding = 60,\n    ...options\n  }: SuperClusterViewportOptions) {\n    super({ maxZoom, viewportPadding });\n\n    this.superCluster = new SuperCluster({\n      maxZoom: this.maxZoom,\n      radius,\n      ...options,\n    });\n\n    this.state = { zoom: -1, view: [0, 0, 0, 0] };\n  }\n\n  public calculate(input: AlgorithmInput): AlgorithmOutput {\n    const state: SuperClusterViewportState = {\n      zoom: Math.round(input.map.getZoom()),\n      view: getPaddedViewport(\n        input.map.getBounds(),\n        input.mapCanvasProjection,\n        this.viewportPadding\n      ),\n    };\n\n    let changed = !equal(this.state, state);\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n\n      const points = this.markers.map((marker) => {\n        const position = MarkerUtils.getPosition(marker);\n        const coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\" as const,\n          geometry: {\n            type: \"Point\" as const,\n            coordinates,\n          },\n          properties: { marker },\n        };\n      });\n      this.superCluster.load(points);\n    }\n\n    if (changed) {\n      this.clusters = this.cluster(input);\n      this.state = state;\n    }\n\n    return { clusters: this.clusters, changed };\n  }\n\n  public cluster({ map, mapCanvasProjection }: AlgorithmInput): Cluster[] {\n    /* recalculate new state because we can't use the cached version. */\n    const state: SuperClusterViewportState = {\n      zoom: Math.round(map.getZoom()),\n      view: getPaddedViewport(\n        map.getBounds(),\n        mapCanvasProjection,\n        this.viewportPadding\n      ),\n    };\n\n    return this.superCluster\n      .getClusters(state.view, state.zoom)\n      .map((feature: ClusterFeature<{ marker: Marker }>) =>\n        this.transformCluster(feature)\n      );\n  }\n\n  protected transformCluster({\n    geometry: {\n      coordinates: [lng, lat],\n    },\n    properties,\n  }: ClusterFeature<{ marker: Marker }>): Cluster {\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster\n          .getLeaves(properties.cluster_id, Infinity)\n          .map((leaf) => leaf.properties.marker),\n        position: { lat, lng },\n      });\n    }\n\n    const marker = properties.marker;\n\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker),\n    });\n  }\n}\n"], "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "static", "map", "google", "maps", "marker", "getMapCapabilities", "isAdvancedMarkersAvailable", "AdvancedMarkerElement", "this", "isAdvancedMarker", "setMap", "position", "LatLng", "lat", "lng", "getPosition", "getVisible", "Cluster", "constructor", "_ref", "markers", "_position", "bounds", "LatLngBounds", "extend", "getCenter", "count", "filter", "m", "push", "delete", "undefined", "filterMarkersToPaddedViewport", "mapCanvasProjection", "viewportPaddingPixels", "extendedMapBounds", "extendBoundsToPaddedViewport", "getBounds", "contains", "projection", "numPixels", "northEast", "southWest", "latLngBoundsToPixelBounds", "extendedPixelBounds", "extendPixelBounds", "pixelBoundsToLatLngBounds", "getPaddedViewport", "pixels", "extended", "ne", "getNorthEast", "sw", "getSouthWest", "distanceBetweenPoints", "p1", "p2", "dLat", "Math", "PI", "dLon", "sinDLat", "sin", "sinDLon", "a", "cos", "atan2", "sqrt", "fromLatLngToDivPixel", "x", "y", "_ref2", "fromDivPixelToLatLng", "AbstractAlgorithm", "max<PERSON><PERSON>", "noop", "AbstractViewportAlgorithm", "_a", "viewportPadding", "super", "calculate", "_ref3", "getZoom", "clusters", "changed", "cluster", "fastDeepEqual", "equal", "b", "keys", "Array", "isArray", "RegExp", "source", "flags", "valueOf", "toString", "key", "ARRAY_TYPES", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "KDBush", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "magic", "versionAndType", "version", "ArrayType", "nodeSize", "numItems", "isNaN", "min", "max", "IndexArrayType", "arrayTypeIndex", "coordsByteSize", "BYTES_PER_ELEMENT", "idsByteSize", "padCoords", "ids", "coords", "_pos", "_finished", "set", "add", "index", "finish", "numAdded", "sort", "range", "minX", "minY", "maxX", "maxY", "stack", "result", "axis", "pop", "right", "left", "within", "qx", "qy", "r", "r2", "sqDist", "select", "k", "n", "z", "log", "exp", "sd", "floor", "j", "swapItem", "swap", "arr", "tmp", "ax", "ay", "bx", "by", "dx", "dy", "defaultOptions", "minZoom", "minPoints", "radius", "extent", "generateId", "reduce", "props", "fround", "OFFSET_ID", "OFFSET_NUM", "OFFSET_PROP", "Supercluster", "options", "assign", "create", "trees", "stride", "clusterProps", "load", "points", "console", "time", "timerId", "geometry", "coordinates", "lngX", "latY", "Infinity", "tree", "_createTree", "timeEnd", "now", "Date", "_cluster", "getClusters", "bbox", "zoom", "minLng", "minLat", "maxLng", "maxLat", "easternHem", "westernHem", "concat", "_limitZoom", "id", "getClusterJSON", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clusterId", "originId", "_getOriginId", "originZoom", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "errorMsg", "pow", "children", "getLeaves", "limit", "offset", "leaves", "_appendLeaves", "getTile", "z2", "top", "bottom", "tile", "features", "_addTileFeatures", "getClusterExpansionZoom", "expansionZoom", "properties", "cluster_id", "skipped", "child", "point_count", "isCluster", "tags", "px", "py", "getClusterProperties", "f", "type", "round", "nextData", "neighborIds", "numPointsOrigin", "numPoints", "neighborId", "clusterProperties", "wx", "wy", "clusterPropIndex", "numPoints2", "_map", "clone", "original", "yLat", "abbrev", "propIndex", "point_count_abbreviated", "y2", "atan", "SuperClusterAlgorithm", "state", "superCluster", "SuperCluster", "input", "feature", "transformCluster", "leaf", "ClusterStats", "sum", "clusterMarkerCounts", "clusterMarkerSum", "mean", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "stats", "svg", "title", "zIndex", "Number", "<PERSON><PERSON>", "MAX_ZINDEX", "isAdvancedMarkerAvailable", "svgEl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "documentElement", "setAttribute", "clusterOptions", "content", "icon", "url", "btoa", "anchor", "Point", "OverlayViewSafe", "type1", "type2", "property", "OverlayView", "MarkerClustererEvents", "exports", "defaultOnClusterClickHandler", "_", "fitBounds", "maxDistance", "gridSize", "for<PERSON>ach", "addToClosestCluster", "candidate", "distance", "toJSON", "algorithmOptions", "algorithm", "renderer", "onClusterClick", "add<PERSON><PERSON><PERSON>", "noDraw", "includes", "addMarkers", "<PERSON><PERSON><PERSON><PERSON>", "splice", "removeMarkers", "removed", "clearMarkers", "getMap", "Map", "getProjection", "event", "trigger", "CLUSTERING_BEGIN", "singleMarker", "Set", "groupMarkers", "has", "renderClusters", "requestAnimationFrame", "CLUSTERING_END", "onAdd", "idleListener", "addListener", "bind", "onRemove", "removeListener", "reset", "CLUSTER_CLICK", "view"], "mappings": "uPAwCO,SAASA,EAAOC,EAAGC,GACtB,IAAIC,EAAI,CAAA,EACR,IAAK,IAAIC,KAAKH,EAAOI,OAAOC,UAAUC,eAAeC,KAAKP,EAAGG,IAAMF,EAAEO,QAAQL,GAAK,IAC9ED,EAAEC,GAAKH,EAAEG,IACb,GAAS,MAALH,GAAqD,mBAAjCI,OAAOK,sBACtB,KAAIC,EAAI,EAAb,IAAgBP,EAAIC,OAAOK,sBAAsBT,GAAIU,EAAIP,EAAEQ,OAAQD,IAC3DT,EAAEO,QAAQL,EAAEO,IAAM,GAAKN,OAAOC,UAAUO,qBAAqBL,KAAKP,EAAGG,EAAEO,MACvER,EAAEC,EAAEO,IAAMV,EAAEG,EAAEO,IAF4B,CAItD,OAAOR,CACX,OCvBaW,EACJC,iCAAiCC,GACtC,OACEC,OAAOC,KAAKC,SAC4C,IAAxDH,EAAII,qBAAqBC,0BAE7B,CAEON,wBACLI,GAEA,OACEF,OAAOC,KAAKC,QACZA,aAAkBF,OAAOC,KAAKC,OAAOG,qBAEzC,CAEOP,cAAcI,EAAgBH,GAC/BO,KAAKC,iBAAiBL,GACxBA,EAAOH,IAAMA,EAEbG,EAAOM,OAAOT,EAElB,CAEOD,mBAAmBI,GAExB,GAAII,KAAKC,iBAAiBL,GAAS,CACjC,GAAIA,EAAOO,SAAU,CACnB,GAAIP,EAAOO,oBAAoBT,OAAOC,KAAKS,OACzC,OAAOR,EAAOO,SAGhB,GAAIP,EAAOO,SAASE,KAAOT,EAAOO,SAASG,IACzC,OAAO,IAAIZ,OAAOC,KAAKS,OACrBR,EAAOO,SAASE,IAChBT,EAAOO,SAASG,IAGrB,CACD,OAAO,IAAIZ,OAAOC,KAAKS,OAAO,KAC/B,CACD,OAAOR,EAAOW,aAChB,CAEOf,kBAAkBI,GACvB,QAAII,KAAKC,iBAAiBL,IAUnBA,EAAOY,YAChB,QC7DWC,EAKXC,YAAAC,GAAiD,IAArCC,QAAEA,EAAOT,SAAEA,GAA0BQ,EAC/CX,KAAKY,QAAUA,EAEXT,IACEA,aAAoBT,OAAOC,KAAKS,OAClCJ,KAAKa,UAAYV,EAEjBH,KAAKa,UAAY,IAAInB,OAAOC,KAAKS,OAAOD,GAG9C,CAEWW,aACT,GAA4B,IAAxBd,KAAKY,QAAQvB,SAAiBW,KAAKa,UACrC,OAGF,MAAMC,EAAS,IAAIpB,OAAOC,KAAKoB,aAAaf,KAAKa,UAAWb,KAAKa,WACjE,IAAK,MAAMjB,KAAUI,KAAKY,QACxBE,EAAOE,OAAOzB,EAAYgB,YAAYX,IAExC,OAAOkB,CACT,CAEWX,eACT,OAAOH,KAAKa,WAAab,KAAKc,OAAOG,WACvC,CAKWC,YACT,OAAOlB,KAAKY,QAAQO,QAAQC,GAAc7B,EAAYiB,WAAWY,KAAI/B,MACvE,CAKOgC,KAAKzB,GACVI,KAAKY,QAAQS,KAAKzB,EACpB,CAKO0B,SACDtB,KAAKJ,SACPL,EAAYW,OAAOF,KAAKJ,OAAQ,MAChCI,KAAKJ,YAAS2B,GAEhBvB,KAAKY,QAAQvB,OAAS,CACxB,ECpDWmC,MAAAA,EAAgCA,CAC3C/B,EACAgC,EACAb,EACAc,KAEA,MAAMC,EAAoBC,EACxBnC,EAAIoC,YACJJ,EACAC,GAEF,OAAOd,EAAQO,QAAQvB,GACrB+B,EAAkBG,SAASvC,EAAYgB,YAAYX,KACpD,EAMUgC,EAA+BA,CAC1Cd,EACAiB,EACAC,KAEA,MAAMC,UAAEA,EAASC,UAAEA,GAAcC,EAC/BrB,EACAiB,GAEIK,EAAsBC,EAC1B,CAAEJ,YAAWC,aACbF,GAEF,OAAOM,EAA0BF,EAAqBL,EAAW,EAMtDQ,EAAoBA,CAC/BzB,EACAiB,EACAS,KAEA,MAAMC,EAAWb,EAA6Bd,EAAQiB,EAAYS,GAC5DE,EAAKD,EAASE,eACdC,EAAKH,EAASI,eAEpB,MAAO,CAACD,EAAGtC,MAAOsC,EAAGvC,MAAOqC,EAAGpC,MAAOoC,EAAGrC,MAAM,EAQpCyC,EAAwBA,CACnCC,EACAC,KAEA,MACMC,GAASD,EAAG3C,IAAM0C,EAAG1C,KAAO6C,KAAKC,GAAM,IACvCC,GAASJ,EAAG1C,IAAMyC,EAAGzC,KAAO4C,KAAKC,GAAM,IACvCE,EAAUH,KAAKI,IAAIL,EAAO,GAC1BM,EAAUL,KAAKI,IAAIF,EAAO,GAC1BI,EACJH,EAAUA,EACVH,KAAKO,IAAKV,EAAG1C,IAAM6C,KAAKC,GAAM,KAC5BD,KAAKO,IAAKT,EAAG3C,IAAM6C,KAAKC,GAAM,KAC9BI,EACAA,EAEJ,OAZU,MAWA,EAAIL,KAAKQ,MAAMR,KAAKS,KAAKH,GAAIN,KAAKS,KAAK,EAAIH,IACzC,EAaRrB,EAA4BA,CAChCrB,EACAiB,KAEO,CACLE,UAAWF,EAAW6B,qBAAqB9C,EAAO6B,gBAClDT,UAAWH,EAAW6B,qBAAqB9C,EAAO+B,kBASzCR,EAAoBA,CAAA1B,EAE/BqB,KACe,IAFfC,UAAEA,EAASC,UAAEA,GAAwBvB,EASrC,OANAsB,EAAU4B,GAAK7B,EACfC,EAAU6B,GAAK9B,EAEfE,EAAU2B,GAAK7B,EACfE,EAAU4B,GAAK9B,EAER,CAAEC,YAAWC,YAAW,EAMpBI,EAA4BA,CAAAyB,EAEvChC,KAC4B,IAF5BE,UAAEA,EAASC,UAAEA,GAAwB6B,EAGrC,MAAMnB,EAAKb,EAAWiC,qBAAqB9B,GACrCQ,EAAKX,EAAWiC,qBAAqB/B,GAC3C,OAAO,IAAIvC,OAAOC,KAAKoB,aAAa6B,EAAIF,EAAG,QClFvBuB,EAGpBvD,YAAAC,GAA8C,IAAlCuD,QAAEA,EAAU,IAAsBvD,EAC5CX,KAAKkE,QAAUA,CACjB,CAaUC,KAAIJ,GAEV,IAFsDnD,QACxDA,GACEmD,EACF,OAAOI,EAAKvD,EACd,EAgCI,MAAgBwD,UAAkCH,EAGtDvD,YAAY2D,OAAAC,gBAAEA,EAAkB,IAAED,EAChCE,MAD4C9F,EAAA4F,EAAlC,sBAFFrE,KAAesE,gBAAG,GAI1BtE,KAAKsE,gBAAkBA,CACzB,CACOE,UAASC,GAIC,IAJA7D,QACfA,EAAOnB,IACPA,EAAGgC,oBACHA,GACegD,EACf,OAAIhF,EAAIiF,WAAa1E,KAAKkE,QACjB,CACLS,SAAU3E,KAAKmE,KAAK,CAClBvD,YAEFgE,SAAS,GAIN,CACLD,SAAU3E,KAAK6E,QAAQ,CACrBjE,QAASY,EACP/B,EACAgC,EACAb,EACAZ,KAAKsE,iBAEP7E,MACAgC,wBAGN,EAOW0C,MAAAA,EAAQvD,GACFA,EAAQnB,KACtBG,GACC,IAAIa,EAAQ,CACVN,SAAUZ,EAAYgB,YAAYX,GAClCgB,QAAS,CAAChB,2GC/JlB,IAAAkF,EAAiB,SAASC,EAAMvB,EAAGwB,GACjC,GAAIxB,IAAMwB,EAAG,OAAO,EAEpB,GAAIxB,GAAKwB,GAAiB,iBAALxB,GAA6B,iBAALwB,EAAe,CAC1D,GAAIxB,EAAE9C,cAAgBsE,EAAEtE,YAAa,OAAO,EAE5C,IAAIrB,EAAQD,EAAG6F,EACf,GAAIC,MAAMC,QAAQ3B,GAAI,CAEpB,IADAnE,EAASmE,EAAEnE,SACG2F,EAAE3F,OAAQ,OAAO,EAC/B,IAAKD,EAAIC,EAAgB,GAARD,KACf,IAAK2F,EAAMvB,EAAEpE,GAAI4F,EAAE5F,IAAK,OAAO,EACjC,OAAO,CACR,CAID,GAAIoE,EAAE9C,cAAgB0E,OAAQ,OAAO5B,EAAE6B,SAAWL,EAAEK,QAAU7B,EAAE8B,QAAUN,EAAEM,MAC5E,GAAI9B,EAAE+B,UAAYzG,OAAOC,UAAUwG,QAAS,OAAO/B,EAAE+B,YAAcP,EAAEO,UACrE,GAAI/B,EAAEgC,WAAa1G,OAAOC,UAAUyG,SAAU,OAAOhC,EAAEgC,aAAeR,EAAEQ,WAIxE,IADAnG,GADA4F,EAAOnG,OAAOmG,KAAKzB,IACLnE,UACCP,OAAOmG,KAAKD,GAAG3F,OAAQ,OAAO,EAE7C,IAAKD,EAAIC,EAAgB,GAARD,KACf,IAAKN,OAAOC,UAAUC,eAAeC,KAAK+F,EAAGC,EAAK7F,IAAK,OAAO,EAEhE,IAAKA,EAAIC,EAAgB,GAARD,KAAY,CAC3B,IAAIqG,EAAMR,EAAK7F,GAEf,IAAK2F,EAAMvB,EAAEiC,GAAMT,EAAES,IAAO,OAAO,CACpC,CAED,OAAO,CACR,CAGD,OAAOjC,GAAIA,GAAKwB,GAAIA,CACtB,SC5CA,MAAMU,EAAc,CAChBC,UAAWC,WAAYC,kBAAmBC,WAAYC,YACtDC,WAAYC,YAAaC,aAAcC,cAQ5B,MAAMC,EAMjB5G,YAAY6G,GACR,KAAMA,aAAgBC,aAClB,MAAM,IAAIC,MAAM,4CAEpB,MAAOC,EAAOC,GAAkB,IAAIb,WAAWS,EAAM,EAAG,GACxD,GAAc,MAAVG,EACA,MAAM,IAAID,MAAM,kDAEpB,MAAMG,EAAUD,GAAkB,EAClC,GAlBQ,IAkBJC,EACA,MAAM,IAAIH,MAAO,QAAOG,4BAE5B,MAAMC,EAAYjB,EAA6B,GAAjBe,GAC9B,IAAKE,EACD,MAAM,IAAIJ,MAAM,4BAEpB,MAAOK,GAAY,IAAIb,YAAYM,EAAM,EAAG,IACrCQ,GAAY,IAAIZ,YAAYI,EAAM,EAAG,GAE5C,OAAO,IAAID,EAAOS,EAAUD,EAAUD,EAAWN,EACrD,CASA3F,YAAYmG,EAAUD,EAAW,GAAID,EAAYR,aAAcE,GAC3D,GAAIS,MAAMD,IAAaA,EAAW,EAAG,MAAM,IAAIN,MAAO,+BAA8BM,MAEpF7G,KAAK6G,UAAYA,EACjB7G,KAAK4G,SAAW1D,KAAK6D,IAAI7D,KAAK8D,KAAKJ,EAAU,GAAI,OACjD5G,KAAK2G,UAAYA,EACjB3G,KAAKiH,eAAiBJ,EAAW,MAAQd,YAAcE,YAEvD,MAAMiB,EAAiBxB,EAAYxG,QAAQc,KAAK2G,WAC1CQ,EAA4B,EAAXN,EAAe7G,KAAK2G,UAAUS,kBAC/CC,EAAcR,EAAW7G,KAAKiH,eAAeG,kBAC7CE,GAAa,EAAID,EAAc,GAAK,EAE1C,GAAIH,EAAiB,EACjB,MAAM,IAAIX,MAAO,iCAAgCI,MAGjDN,GAASA,aAAgBC,aACzBtG,KAAKqG,KAAOA,EACZrG,KAAKuH,IAAM,IAAIvH,KAAKiH,eAAejH,KAAKqG,KAxDhC,EAwDmDQ,GAC3D7G,KAAKwH,OAAS,IAAIxH,KAAK2G,UAAU3G,KAAKqG,KAzD9B,EAyDkDgB,EAAcC,EAAsB,EAAXT,GACnF7G,KAAKyH,KAAkB,EAAXZ,EACZ7G,KAAK0H,WAAY,IAEjB1H,KAAKqG,KAAO,IAAIC,YA7DR,EA6DkCa,EAAiBE,EAAcC,GACzEtH,KAAKuH,IAAM,IAAIvH,KAAKiH,eAAejH,KAAKqG,KA9DhC,EA8DmDQ,GAC3D7G,KAAKwH,OAAS,IAAIxH,KAAK2G,UAAU3G,KAAKqG,KA/D9B,EA+DkDgB,EAAcC,EAAsB,EAAXT,GACnF7G,KAAKyH,KAAO,EACZzH,KAAK0H,WAAY,EAGjB,IAAI9B,WAAW5F,KAAKqG,KAAM,EAAG,GAAGsB,IAAI,CAAC,IAAM,GAAiBT,IAC5D,IAAInB,YAAY/F,KAAKqG,KAAM,EAAG,GAAG,GAAKO,EACtC,IAAIX,YAAYjG,KAAKqG,KAAM,EAAG,GAAG,GAAKQ,EAE9C,CAQAe,IAAI/D,EAAGC,GACH,MAAM+D,EAAQ7H,KAAKyH,MAAQ,EAI3B,OAHAzH,KAAKuH,IAAIM,GAASA,EAClB7H,KAAKwH,OAAOxH,KAAKyH,QAAU5D,EAC3B7D,KAAKwH,OAAOxH,KAAKyH,QAAU3D,EACpB+D,CACX,CAKAC,SACI,MAAMC,EAAW/H,KAAKyH,MAAQ,EAC9B,GAAIM,IAAa/H,KAAK6G,SAClB,MAAM,IAAIN,MAAO,SAAQwB,yBAAgC/H,KAAK6G,aAMlE,OAHAmB,EAAKhI,KAAKuH,IAAKvH,KAAKwH,OAAQxH,KAAK4G,SAAU,EAAG5G,KAAK6G,SAAW,EAAG,GAEjE7G,KAAK0H,WAAY,EACV1H,IACX,CAUAiI,MAAMC,EAAMC,EAAMC,EAAMC,GACpB,IAAKrI,KAAK0H,UAAW,MAAM,IAAInB,MAAM,+CAErC,MAAMgB,IAACA,EAAGC,OAAEA,EAAMZ,SAAEA,GAAY5G,KAC1BsI,EAAQ,CAAC,EAAGf,EAAIlI,OAAS,EAAG,GAC5BkJ,EAAS,GAGf,KAAOD,EAAMjJ,QAAQ,CACjB,MAAMmJ,EAAOF,EAAMG,OAAS,EACtBC,EAAQJ,EAAMG,OAAS,EACvBE,EAAOL,EAAMG,OAAS,EAG5B,GAAIC,EAAQC,GAAQ/B,EAAU,CAC1B,IAAK,IAAIxH,EAAIuJ,EAAMvJ,GAAKsJ,EAAOtJ,IAAK,CAChC,MAAMyE,EAAI2D,EAAO,EAAIpI,GACf0E,EAAI0D,EAAO,EAAIpI,EAAI,GACrByE,GAAKqE,GAAQrE,GAAKuE,GAAQtE,GAAKqE,GAAQrE,GAAKuE,GAAME,EAAOlH,KAAKkG,EAAInI,GAC1E,CACA,QACJ,CAGA,MAAMgC,EAAKuH,EAAOD,GAAU,EAGtB7E,EAAI2D,EAAO,EAAIpG,GACf0C,EAAI0D,EAAO,EAAIpG,EAAI,GACrByC,GAAKqE,GAAQrE,GAAKuE,GAAQtE,GAAKqE,GAAQrE,GAAKuE,GAAME,EAAOlH,KAAKkG,EAAInG,KAGzD,IAAToH,EAAaN,GAAQrE,EAAIsE,GAAQrE,KACjCwE,EAAMjH,KAAKsH,GACXL,EAAMjH,KAAKD,EAAI,GACfkH,EAAMjH,KAAK,EAAImH,KAEN,IAATA,EAAaJ,GAAQvE,EAAIwE,GAAQvE,KACjCwE,EAAMjH,KAAKD,EAAI,GACfkH,EAAMjH,KAAKqH,GACXJ,EAAMjH,KAAK,EAAImH,GAEvB,CAEA,OAAOD,CACX,CASAK,OAAOC,EAAIC,EAAIC,GACX,IAAK/I,KAAK0H,UAAW,MAAM,IAAInB,MAAM,+CAErC,MAAMgB,IAACA,EAAGC,OAAEA,EAAMZ,SAAEA,GAAY5G,KAC1BsI,EAAQ,CAAC,EAAGf,EAAIlI,OAAS,EAAG,GAC5BkJ,EAAS,GACTS,EAAKD,EAAIA,EAGf,KAAOT,EAAMjJ,QAAQ,CACjB,MAAMmJ,EAAOF,EAAMG,OAAS,EACtBC,EAAQJ,EAAMG,OAAS,EACvBE,EAAOL,EAAMG,OAAS,EAG5B,GAAIC,EAAQC,GAAQ/B,EAAU,CAC1B,IAAK,IAAIxH,EAAIuJ,EAAMvJ,GAAKsJ,EAAOtJ,IACvB6J,EAAOzB,EAAO,EAAIpI,GAAIoI,EAAO,EAAIpI,EAAI,GAAIyJ,EAAIC,IAAOE,GAAIT,EAAOlH,KAAKkG,EAAInI,IAEhF,QACJ,CAGA,MAAMgC,EAAKuH,EAAOD,GAAU,EAGtB7E,EAAI2D,EAAO,EAAIpG,GACf0C,EAAI0D,EAAO,EAAIpG,EAAI,GACrB6H,EAAOpF,EAAGC,EAAG+E,EAAIC,IAAOE,GAAIT,EAAOlH,KAAKkG,EAAInG,KAGnC,IAAToH,EAAaK,EAAKE,GAAKlF,EAAIiF,EAAKC,GAAKjF,KACrCwE,EAAMjH,KAAKsH,GACXL,EAAMjH,KAAKD,EAAI,GACfkH,EAAMjH,KAAK,EAAImH,KAEN,IAATA,EAAaK,EAAKE,GAAKlF,EAAIiF,EAAKC,GAAKjF,KACrCwE,EAAMjH,KAAKD,EAAI,GACfkH,EAAMjH,KAAKqH,GACXJ,EAAMjH,KAAK,EAAImH,GAEvB,CAEA,OAAOD,CACX,EAWJ,SAASP,EAAKT,EAAKC,EAAQZ,EAAU+B,EAAMD,EAAOF,GAC9C,GAAIE,EAAQC,GAAQ/B,EAAU,OAE9B,MAAMxF,EAAKuH,EAAOD,GAAU,EAI5BQ,EAAO3B,EAAKC,EAAQpG,EAAGuH,EAAMD,EAAOF,GAGpCR,EAAKT,EAAKC,EAAQZ,EAAU+B,EAAMvH,EAAI,EAAG,EAAIoH,GAC7CR,EAAKT,EAAKC,EAAQZ,EAAUxF,EAAI,EAAGsH,EAAO,EAAIF,EAClD,CAYA,SAASU,EAAO3B,EAAKC,EAAQ2B,EAAGR,EAAMD,EAAOF,GAEzC,KAAOE,EAAQC,GAAM,CACjB,GAAID,EAAQC,EAAO,IAAK,CACpB,MAAMS,EAAIV,EAAQC,EAAO,EACnBvH,EAAI+H,EAAIR,EAAO,EACfU,EAAInG,KAAKoG,IAAIF,GACb1K,EAAI,GAAMwE,KAAKqG,IAAI,EAAIF,EAAI,GAC3BG,EAAK,GAAMtG,KAAKS,KAAK0F,EAAI3K,GAAK0K,EAAI1K,GAAK0K,IAAMhI,EAAIgI,EAAI,EAAI,GAAK,EAAI,GAGxEF,EAAO3B,EAAKC,EAAQ2B,EAFJjG,KAAK8D,IAAI2B,EAAMzF,KAAKuG,MAAMN,EAAI/H,EAAI1C,EAAI0K,EAAII,IACzCtG,KAAK6D,IAAI2B,EAAOxF,KAAKuG,MAAMN,GAAKC,EAAIhI,GAAK1C,EAAI0K,EAAII,IACxBhB,EAC9C,CAEA,MAAM5J,EAAI4I,EAAO,EAAI2B,EAAIX,GACzB,IAAIpJ,EAAIuJ,EACJe,EAAIhB,EAKR,IAHAiB,EAASpC,EAAKC,EAAQmB,EAAMQ,GACxB3B,EAAO,EAAIkB,EAAQF,GAAQ5J,GAAG+K,EAASpC,EAAKC,EAAQmB,EAAMD,GAEvDtJ,EAAIsK,GAAG,CAIV,IAHAC,EAASpC,EAAKC,EAAQpI,EAAGsK,GACzBtK,IACAsK,IACOlC,EAAO,EAAIpI,EAAIoJ,GAAQ5J,GAAGQ,IACjC,KAAOoI,EAAO,EAAIkC,EAAIlB,GAAQ5J,GAAG8K,GACrC,CAEIlC,EAAO,EAAImB,EAAOH,KAAU5J,EAAG+K,EAASpC,EAAKC,EAAQmB,EAAMe,IAE3DA,IACAC,EAASpC,EAAKC,EAAQkC,EAAGhB,IAGzBgB,GAAKP,IAAGR,EAAOe,EAAI,GACnBP,GAAKO,IAAGhB,EAAQgB,EAAI,EAC5B,CACJ,CAQA,SAASC,EAASpC,EAAKC,EAAQpI,EAAGsK,GAC9BE,EAAKrC,EAAKnI,EAAGsK,GACbE,EAAKpC,EAAQ,EAAIpI,EAAG,EAAIsK,GACxBE,EAAKpC,EAAQ,EAAIpI,EAAI,EAAG,EAAIsK,EAAI,EACpC,CAOA,SAASE,EAAKC,EAAKzK,EAAGsK,GAClB,MAAMI,EAAMD,EAAIzK,GAChByK,EAAIzK,GAAKyK,EAAIH,GACbG,EAAIH,GAAKI,CACb,CAQA,SAASb,EAAOc,EAAIC,EAAIC,EAAIC,GACxB,MAAMC,EAAKJ,EAAKE,EACVG,EAAKJ,EAAKE,EAChB,OAAOC,EAAKA,EAAKC,EAAKA,CAC1B,CCnUA,MAAMC,EAAiB,CACnBC,QAAS,EACTpG,QAAS,GACTqG,UAAW,EACXC,OAAQ,GACRC,OAAQ,IACR7D,SAAU,GACV0C,KAAK,EAGLoB,YAAY,EAGZC,OAAQ,KAGRlL,IAAKmL,GAASA,GAGZC,EAAS3H,KAAK2H,SAAWf,EAAiD,IAAI5D,aAAa,GAAzDrC,IAAQiG,EAAI,IAAMjG,EAAUiG,EAAI,KAA1C,IAACA,EAE/B,MACMgB,EAAY,EAEZC,EAAa,EACbC,EAAc,EAEL,MAAMC,EACjBvK,YAAYwK,GACRlL,KAAKkL,QAAUpM,OAAOqM,OAAOrM,OAAOsM,OAAOf,GAAiBa,GAC5DlL,KAAKqL,MAAQ,IAAInG,MAAMlF,KAAKkL,QAAQhH,QAAU,GAC9ClE,KAAKsL,OAAStL,KAAKkL,QAAQP,OAAS,EAAI,EACxC3K,KAAKuL,aAAe,EACxB,CAEAC,KAAKC,GACD,MAAMnC,IAACA,EAAGgB,QAAEA,EAAOpG,QAAEA,GAAWlE,KAAKkL,QAEjC5B,GAAKoC,QAAQC,KAAK,cAEtB,MAAMC,EAAW,WAAYH,EAAOpM,gBAChCiK,GAAKoC,QAAQC,KAAKC,GAEtB5L,KAAKyL,OAASA,EAGd,MAAMpF,EAAO,GAEb,IAAK,IAAIjH,EAAI,EAAGA,EAAIqM,EAAOpM,OAAQD,IAAK,CACpC,MAAMP,EAAI4M,EAAOrM,GACjB,IAAKP,EAAEgN,SAAU,SAEjB,MAAOvL,EAAKD,GAAOxB,EAAEgN,SAASC,YACxBjI,EAAIgH,EAAOkB,EAAKzL,IAChBwD,EAAI+G,EAAOmB,EAAK3L,IAEtBgG,EAAKhF,KACDwC,EAAGC,EACHmI,IACA7M,GACC,EACD,GAEAY,KAAKkL,QAAQP,QAAQtE,EAAKhF,KAAK,EACvC,CACA,IAAI6K,EAAOlM,KAAKqL,MAAMnH,EAAU,GAAKlE,KAAKmM,YAAY9F,GAElDiD,GAAKoC,QAAQU,QAAQR,GAIzB,IAAK,IAAIvC,EAAInF,EAASmF,GAAKiB,EAASjB,IAAK,CACrC,MAAMgD,GAAOC,KAAKD,MAGlBH,EAAOlM,KAAKqL,MAAMhC,GAAKrJ,KAAKmM,YAAYnM,KAAKuM,SAASL,EAAM7C,IAExDC,GAAKoC,QAAQpC,IAAI,2BAA4BD,EAAG6C,EAAKrF,UAAWyF,KAAKD,MAAQA,EACrF,CAIA,OAFI/C,GAAKoC,QAAQU,QAAQ,cAElBpM,IACX,CAEAwM,YAAYC,EAAMC,GACd,IAAIC,IAAWF,EAAK,GAAK,KAAO,IAAM,KAAO,IAAM,IACnD,MAAMG,EAAS1J,KAAK8D,KAAK,GAAI9D,KAAK6D,IAAI,GAAI0F,EAAK,KAC/C,IAAII,EAAqB,MAAZJ,EAAK,GAAa,MAAQA,EAAK,GAAK,KAAO,IAAM,KAAO,IAAM,IAC3E,MAAMK,EAAS5J,KAAK8D,KAAK,GAAI9D,KAAK6D,IAAI,GAAI0F,EAAK,KAE/C,GAAIA,EAAK,GAAKA,EAAK,IAAM,IACrBE,GAAU,IACVE,EAAS,SACN,GAAIF,EAASE,EAAQ,CACxB,MAAME,EAAa/M,KAAKwM,YAAY,CAACG,EAAQC,EAAQ,IAAKE,GAASJ,GAC7DM,EAAahN,KAAKwM,YAAY,EAAE,IAAKI,EAAQC,EAAQC,GAASJ,GACpE,OAAOK,EAAWE,OAAOD,EAC7B,CAEA,MAAMd,EAAOlM,KAAKqL,MAAMrL,KAAKkN,WAAWR,IAClCnF,EAAM2E,EAAKjE,MAAM8D,EAAKY,GAASX,EAAKc,GAASf,EAAKc,GAASb,EAAKY,IAChEvG,EAAO6F,EAAK7F,KACZ1B,EAAW,GACjB,IAAK,MAAMwI,KAAM5F,EAAK,CAClB,MAAM4B,EAAInJ,KAAKsL,OAAS6B,EACxBxI,EAAStD,KAAKgF,EAAK8C,EAAI4B,GAAc,EAAIqC,EAAe/G,EAAM8C,EAAGnJ,KAAKuL,cAAgBvL,KAAKyL,OAAOpF,EAAK8C,EAAI2B,IAC/G,CACA,OAAOnG,CACX,CAEA0I,YAAYC,GACR,MAAMC,EAAWvN,KAAKwN,aAAaF,GAC7BG,EAAazN,KAAK0N,eAAeJ,GACjCK,EAAW,oCAEXzB,EAAOlM,KAAKqL,MAAMoC,GACxB,IAAKvB,EAAM,MAAM,IAAI3F,MAAMoH,GAE3B,MAAMtH,EAAO6F,EAAK7F,KAClB,GAAIkH,EAAWvN,KAAKsL,QAAUjF,EAAKhH,OAAQ,MAAM,IAAIkH,MAAMoH,GAE3D,MAAM5E,EAAI/I,KAAKkL,QAAQV,QAAUxK,KAAKkL,QAAQT,OAASvH,KAAK0K,IAAI,EAAGH,EAAa,IAC1E5J,EAAIwC,EAAKkH,EAAWvN,KAAKsL,QACzBxH,EAAIuC,EAAKkH,EAAWvN,KAAKsL,OAAS,GAClC/D,EAAM2E,EAAKtD,OAAO/E,EAAGC,EAAGiF,GACxB8E,EAAW,GACjB,IAAK,MAAMV,KAAM5F,EAAK,CAClB,MAAM4B,EAAIgE,EAAKnN,KAAKsL,OAChBjF,EAAK8C,EA1GC,KA0GsBmE,GAC5BO,EAASxM,KAAKgF,EAAK8C,EAAI4B,GAAc,EAAIqC,EAAe/G,EAAM8C,EAAGnJ,KAAKuL,cAAgBvL,KAAKyL,OAAOpF,EAAK8C,EAAI2B,IAEnH,CAEA,GAAwB,IAApB+C,EAASxO,OAAc,MAAM,IAAIkH,MAAMoH,GAE3C,OAAOE,CACX,CAEAC,UAAUR,EAAWS,EAAOC,GACxBD,EAAQA,GAAS,GACjBC,EAASA,GAAU,EAEnB,MAAMC,EAAS,GAGf,OAFAjO,KAAKkO,cAAcD,EAAQX,EAAWS,EAAOC,EAAQ,GAE9CC,CACX,CAEAE,QAAQ9E,EAAGxF,EAAGC,GACV,MAAMoI,EAAOlM,KAAKqL,MAAMrL,KAAKkN,WAAW7D,IAClC+E,EAAKlL,KAAK0K,IAAI,EAAGvE,IACjBoB,OAACA,EAAMD,OAAEA,GAAUxK,KAAKkL,QACxBrM,EAAI2L,EAASC,EACb4D,GAAOvK,EAAIjF,GAAKuP,EAChBE,GAAUxK,EAAI,EAAIjF,GAAKuP,EAEvBG,EAAO,CACTC,SAAU,IAkBd,OAfAxO,KAAKyO,iBACDvC,EAAKjE,OAAOpE,EAAIhF,GAAKuP,EAAIC,GAAMxK,EAAI,EAAIhF,GAAKuP,EAAIE,GAChDpC,EAAK7F,KAAMxC,EAAGC,EAAGsK,EAAIG,GAEf,IAAN1K,GACA7D,KAAKyO,iBACDvC,EAAKjE,MAAM,EAAIpJ,EAAIuP,EAAIC,EAAK,EAAGC,GAC/BpC,EAAK7F,KAAM+H,EAAItK,EAAGsK,EAAIG,GAE1B1K,IAAMuK,EAAK,GACXpO,KAAKyO,iBACDvC,EAAKjE,MAAM,EAAGoG,EAAKxP,EAAIuP,EAAIE,GAC3BpC,EAAK7F,MAAO,EAAGvC,EAAGsK,EAAIG,GAGvBA,EAAKC,SAASnP,OAASkP,EAAO,IACzC,CAEAG,wBAAwBpB,GACpB,IAAIqB,EAAgB3O,KAAK0N,eAAeJ,GAAa,EACrD,KAAOqB,GAAiB3O,KAAKkL,QAAQhH,SAAS,CAC1C,MAAM2J,EAAW7N,KAAKqN,YAAYC,GAElC,GADAqB,IACwB,IAApBd,EAASxO,OAAc,MAC3BiO,EAAYO,EAAS,GAAGe,WAAWC,UACvC,CACA,OAAOF,CACX,CAEAT,cAAc3F,EAAQ+E,EAAWS,EAAOC,EAAQc,GAC5C,MAAMjB,EAAW7N,KAAKqN,YAAYC,GAElC,IAAK,MAAMyB,KAASlB,EAAU,CAC1B,MAAMjD,EAAQmE,EAAMH,WAkBpB,GAhBIhE,GAASA,EAAM/F,QACXiK,EAAUlE,EAAMoE,aAAehB,EAE/Bc,GAAWlE,EAAMoE,YAGjBF,EAAU9O,KAAKkO,cAAc3F,EAAQqC,EAAMiE,WAAYd,EAAOC,EAAQc,GAGnEA,EAAUd,EAEjBc,IAGAvG,EAAOlH,KAAK0N,GAEZxG,EAAOlJ,SAAW0O,EAAO,KACjC,CAEA,OAAOe,CACX,CAEA3C,YAAY9F,GACR,MAAM6F,EAAO,IAAI9F,EAAOC,EAAKhH,OAASW,KAAKsL,OAAS,EAAGtL,KAAKkL,QAAQtE,SAAUV,cAC9E,IAAK,IAAI9G,EAAI,EAAGA,EAAIiH,EAAKhH,OAAQD,GAAKY,KAAKsL,OAAQY,EAAKtE,IAAIvB,EAAKjH,GAAIiH,EAAKjH,EAAI,IAG9E,OAFA8M,EAAKpE,SACLoE,EAAK7F,KAAOA,EACL6F,CACX,CAEAuC,iBAAiBlH,EAAKlB,EAAMxC,EAAGC,EAAGsK,EAAIG,GAClC,IAAK,MAAMnP,KAAKmI,EAAK,CACjB,MAAM4B,EAAI/J,EAAIY,KAAKsL,OACb2D,EAAY5I,EAAK8C,EAAI4B,GAAc,EAEzC,IAAImE,EAAMC,EAAIC,EACd,GAAIH,EACAC,EAAOG,EAAqBhJ,EAAM8C,EAAGnJ,KAAKuL,cAC1C4D,EAAK9I,EAAK8C,GACViG,EAAK/I,EAAK8C,EAAI,OACX,CACH,MAAMtK,EAAImB,KAAKyL,OAAOpF,EAAK8C,EAAI2B,IAC/BoE,EAAOrQ,EAAE+P,WACT,MAAOtO,EAAKD,GAAOxB,EAAEgN,SAASC,YAC9BqD,EAAKpD,EAAKzL,GACV8O,EAAKpD,EAAK3L,EACd,CAEA,MAAMiP,EAAI,CACNC,KAAM,EACN1D,SAAU,CAAC,CACP3I,KAAKsM,MAAMxP,KAAKkL,QAAQT,QAAU0E,EAAKf,EAAKvK,IAC5CX,KAAKsM,MAAMxP,KAAKkL,QAAQT,QAAU2E,EAAKhB,EAAKtK,MAEhDoL,QAIJ,IAAI/B,EAGAA,EAFA8B,GAAajP,KAAKkL,QAAQR,WAErBrE,EAAK8C,EAAI2B,GAGT9K,KAAKyL,OAAOpF,EAAK8C,EAAI2B,IAAYqC,QAG/B5L,IAAP4L,IAAkBmC,EAAEnC,GAAKA,GAE7BoB,EAAKC,SAASnN,KAAKiO,EACvB,CACJ,CAEApC,WAAW7D,GACP,OAAOnG,KAAK8D,IAAIhH,KAAKkL,QAAQZ,QAASpH,KAAK6D,IAAI7D,KAAKuG,OAAOJ,GAAIrJ,KAAKkL,QAAQhH,QAAU,GAC1F,CAEAqI,SAASL,EAAMQ,GACX,MAAMlC,OAACA,EAAMC,OAAEA,EAAME,OAAEA,EAAMJ,UAAEA,GAAavK,KAAKkL,QAC3CnC,EAAIyB,GAAUC,EAASvH,KAAK0K,IAAI,EAAGlB,IACnCrG,EAAO6F,EAAK7F,KACZoJ,EAAW,GACXnE,EAAStL,KAAKsL,OAGpB,IAAK,IAAIlM,EAAI,EAAGA,EAAIiH,EAAKhH,OAAQD,GAAKkM,EAAQ,CAE1C,GAAIjF,EAAKjH,EAtQD,IAsQqBsN,EAAM,SACnCrG,EAAKjH,EAvQG,GAuQgBsN,EAGxB,MAAM7I,EAAIwC,EAAKjH,GACT0E,EAAIuC,EAAKjH,EAAI,GACbsQ,EAAcxD,EAAKtD,OAAOvC,EAAKjH,GAAIiH,EAAKjH,EAAI,GAAI2J,GAEhD4G,EAAkBtJ,EAAKjH,EAAI2L,GACjC,IAAI6E,EAAYD,EAGhB,IAAK,MAAME,KAAcH,EAAa,CAClC,MAAMvG,EAAI0G,EAAavE,EAEnBjF,EAAK8C,EArRL,GAqRwBuD,IAAMkD,GAAavJ,EAAK8C,EAAI4B,GAC5D,CAGA,GAAI6E,EAAYD,GAAmBC,GAAarF,EAAW,CACvD,IAGIuF,EAHAC,EAAKlM,EAAI8L,EACTK,EAAKlM,EAAI6L,EAGTM,GAAoB,EAGxB,MAAM9C,IAAO/N,EAAIkM,EAAS,IAAM,IAAMoB,EAAO,GAAK1M,KAAKyL,OAAOpM,OAE9D,IAAK,MAAMwQ,KAAcH,EAAa,CAClC,MAAMvG,EAAI0G,EAAavE,EAEvB,GAAIjF,EAAK8C,EAtST,IAsS6BuD,EAAM,SACnCrG,EAAK8C,EAvSL,GAuSwBuD,EAExB,MAAMwD,EAAa7J,EAAK8C,EAAI4B,GAC5BgF,GAAM1J,EAAK8C,GAAK+G,EAChBF,GAAM3J,EAAK8C,EAAI,GAAK+G,EAEpB7J,EAAK8C,EA3SH,GA2SwBgE,EAEtBxC,IACKmF,IACDA,EAAoB9P,KAAKmQ,KAAK9J,EAAMjH,GAAG,GACvC6Q,EAAmBjQ,KAAKuL,aAAalM,OACrCW,KAAKuL,aAAalK,KAAKyO,IAE3BnF,EAAOmF,EAAmB9P,KAAKmQ,KAAK9J,EAAM8C,IAElD,CAEA9C,EAAKjH,EAvTC,GAuToB+N,EAC1BsC,EAASpO,KAAK0O,EAAKH,EAAWI,EAAKJ,EAAW3D,IAAUkB,GAAK,EAAGyC,GAC5DjF,GAAQ8E,EAASpO,KAAK4O,EAE9B,KAAO,CACH,IAAK,IAAIvG,EAAI,EAAGA,EAAI4B,EAAQ5B,IAAK+F,EAASpO,KAAKgF,EAAKjH,EAAIsK,IAExD,GAAIkG,EAAY,EACZ,IAAK,MAAMC,KAAcH,EAAa,CAClC,MAAMvG,EAAI0G,EAAavE,EACvB,KAAIjF,EAAK8C,EAnUb,IAmUiCuD,GAA7B,CACArG,EAAK8C,EApUT,GAoU4BuD,EACxB,IAAK,IAAIhD,EAAI,EAAGA,EAAI4B,EAAQ5B,IAAK+F,EAASpO,KAAKgF,EAAK8C,EAAIO,GAFrB,CAGvC,CAER,CACJ,CAEA,OAAO+F,CACX,CAGAjC,aAAaF,GACT,OAAQA,EAAYtN,KAAKyL,OAAOpM,QAAW,CAC/C,CAGAqO,eAAeJ,GACX,OAAQA,EAAYtN,KAAKyL,OAAOpM,QAAU,EAC9C,CAEA8Q,KAAK9J,EAAMjH,EAAGgR,GACV,GAAI/J,EAAKjH,EAAI2L,GAAc,EAAG,CAC1B,MAAMH,EAAQ5K,KAAKuL,aAAalF,EAAKjH,EAAI4L,IACzC,OAAOoF,EAAQtR,OAAOqM,OAAO,CAAA,EAAIP,GAASA,CAC9C,CACA,MAAMyF,EAAWrQ,KAAKyL,OAAOpF,EAAKjH,EAAI0L,IAAY8D,WAC5CrG,EAASvI,KAAKkL,QAAQzL,IAAI4Q,GAChC,OAAOD,GAAS7H,IAAW8H,EAAWvR,OAAOqM,OAAO,CAAE,EAAE5C,GAAUA,CACtE,EAGJ,SAAS6E,EAAe/G,EAAMjH,EAAGmM,GAC7B,MAAO,CACHgE,KAAM,UACNpC,GAAI9G,EAAKjH,EAAI0L,GACb8D,WAAYS,EAAqBhJ,EAAMjH,EAAGmM,GAC1CM,SAAU,CACN0D,KAAM,QACNzD,YAAa,EA+BXjI,EA/BiBwC,EAAKjH,GAgCb,KAAXyE,EAAI,KAhCyByM,EAAKjK,EAAKjH,EAAI,OA+BvD,IAAcyE,CA5Bd,CAEA,SAASwL,EAAqBhJ,EAAMjH,EAAGmM,GACnC,MAAMrK,EAAQmF,EAAKjH,EAAI2L,GACjBwF,EACFrP,GAAS,IAAS,GAAEgC,KAAKsM,MAAMtO,EAAQ,QACvCA,GAAS,IAAUgC,KAAKsM,MAAMtO,EAAQ,KAAO,GAA5B,IAAsCA,EACrDsP,EAAYnK,EAAKjH,EAAI4L,GACrB4D,GAA4B,IAAf4B,EAAmB,CAAE,EAAG1R,OAAOqM,OAAO,CAAE,EAAEI,EAAaiF,IAC1E,OAAO1R,OAAOqM,OAAOyD,EAAY,CAC7B/J,SAAS,EACTgK,WAAYxI,EAAKjH,EAAI0L,GACrBkE,YAAa9N,EACbuP,wBAAyBF,GAEjC,CAGA,SAASxE,EAAKzL,GACV,OAAOA,EAAM,IAAM,EACvB,CACA,SAAS0L,EAAK3L,GACV,MAAMiD,EAAMJ,KAAKI,IAAIjD,EAAM6C,KAAKC,GAAK,KAC/BW,EAAK,GAAM,IAAOZ,KAAKoG,KAAK,EAAIhG,IAAQ,EAAIA,IAAQJ,KAAKC,GAC/D,OAAOW,EAAI,EAAI,EAAIA,EAAI,EAAI,EAAIA,CACnC,CAMA,SAASwM,EAAKxM,GACV,MAAM4M,GAAM,IAAU,IAAJ5M,GAAWZ,KAAKC,GAAK,IACvC,OAAO,IAAMD,KAAKyN,KAAKzN,KAAKqG,IAAImH,IAAOxN,KAAKC,GAAK,EACrD,CCrYM,MAAOyN,UAA8B3M,EAMzCvD,YAAY2D,OAAAH,QAAEA,EAAOsG,OAAEA,EAAS,MAAOU,EAA3BzM,EAAA4F,EAAA,CAAA,UAAA,WACVE,MAAM,CAAEL,YAHAlE,KAAA6Q,MAAQ,CAAEnE,MAAO,GAKzB1M,KAAK8Q,aAAe,IAAIC,iBACtB7M,QAASlE,KAAKkE,QACdsG,UACGU,GAEP,CAEO1G,UAAUwM,GACf,IAAIpM,GAAU,EACd,MAAMiM,EAAQ,CAAEnE,KAAMsE,EAAMvR,IAAIiF,WAEhC,IAAKK,EAAMiM,EAAMpQ,QAASZ,KAAKY,SAAU,CACvCgE,GAAU,EAEV5E,KAAKY,QAAU,IAAIoQ,EAAMpQ,SAEzB,MAAM6K,EAASzL,KAAKY,QAAQnB,KAAKG,IAC/B,MAAMO,EAAWZ,EAAYgB,YAAYX,GAEzC,MAAO,CACL2P,KAAM,UACN1D,SAAU,CACR0D,KAAM,QACNzD,YALgB,CAAC3L,EAASG,MAAOH,EAASE,QAO5CuO,WAAY,CAAEhP,UACf,IAEHI,KAAK8Q,aAAatF,KAAKC,EACxB,CAcD,OAZK7G,IACC5E,KAAK6Q,MAAMnE,MAAQ1M,KAAKkE,SAAW2M,EAAMnE,MAAQ1M,KAAKkE,WACxDU,GAAWG,EAAM/E,KAAK6Q,MAAOA,IAIjC7Q,KAAK6Q,MAAQA,EAETjM,IACF5E,KAAK2E,SAAW3E,KAAK6E,QAAQmM,IAGxB,CAAErM,SAAU3E,KAAK2E,SAAUC,UACpC,CAEOC,QAAOlE,GAAwB,IAAvBlB,IAAEA,GAAqBkB,EACpC,OAAOX,KAAK8Q,aACTtE,YAAY,EAAE,KAAM,GAAI,IAAK,IAAKtJ,KAAKsM,MAAM/P,EAAIiF,YACjDjF,KAAKwR,GACJjR,KAAKkR,iBAAiBD,IAE5B,CAEUC,iBAAgBnN,GAKW,IAJnC8H,UACEC,aAAcxL,EAAKD,IACpBuO,WACDA,GACmC7K,EACnC,GAAI6K,EAAW/J,QACb,OAAO,IAAIpE,EAAQ,CACjBG,QAASZ,KAAK8Q,aACXhD,UAAUc,EAAWC,WAAY5C,KACjCxM,KAAK0R,GAASA,EAAKvC,WAAWhP,SACjCO,SAAU,CAAEE,MAAKC,SAIrB,MAAMV,EAASgP,EAAWhP,OAE1B,OAAO,IAAIa,EAAQ,CACjBG,QAAS,CAAChB,GACVO,SAAUZ,EAAYgB,YAAYX,IAEtC,QChGWwR,EAYX1Q,YAAYE,EAAmB+D,GAC7B3E,KAAKY,QAAU,CAAEyQ,IAAKzQ,EAAQvB,QAC9B,MAAMiS,EAAsB3M,EAASlF,KAAK+D,GAAMA,EAAEtC,QAC5CqQ,EAAmBD,EAAoB3G,QAAO,CAACnH,EAAGwB,IAAMxB,EAAIwB,GAAG,GAErEhF,KAAK2E,SAAW,CACdzD,MAAOyD,EAAStF,OAChBuB,QAAS,CACP4Q,KAAMD,EAAmB5M,EAAStF,OAClCgS,IAAKE,EACLxK,IAAK7D,KAAK6D,OAAOuK,GACjBtK,IAAK9D,KAAK8D,OAAOsK,IAGvB,QAmBWG,EAuCJC,OAAM/Q,EAEXgR,EACAlS,GAAoB,IAFpByB,MAAEA,EAAKf,SAAEA,GAAmBQ,EAK5B,MAIMiR,EAAM,cAHV1Q,EAAQgC,KAAK8D,IAAI,GAAI2K,EAAMhN,SAAS/D,QAAQ4Q,MAAQ,UAAY,oYAOyEtQ,mBAGrI2Q,EAAsB,cAAA3Q,YAE1B4Q,EAAiBC,OAAOrS,OAAOC,KAAKqS,OAAOC,YAAc/Q,EAE3D,GAAI3B,EAAY2S,0BAA0BzS,GAAM,CAE9C,MACM0S,GADS,IAAIC,WACEC,gBACnBT,EACA,iBACAU,gBACFH,EAAMI,aAAa,YAAa,mBAEhC,MAAMC,EAAkE,CACtE/S,MACAU,WACA2R,SACAD,QACAY,QAASN,GAEX,OAAO,IAAIzS,OAAOC,KAAKC,OAAOG,sBAAsByS,EACrD,CAED,MAAMA,EAA4C,CAChDrS,WACA2R,SACAD,QACAa,KAAM,CACJC,IAAkC,6BAAAC,KAAKhB,KACvCiB,OAAQ,IAAInT,OAAOC,KAAKmT,MAAM,GAAI,MAGtC,OAAO,IAAIpT,OAAOC,KAAKqS,OAAOQ,EAChC,QCtHWO,EACXrS,eAZF,SAAgBsS,EAAYC,GAG1B,IAAK,IAAIC,KAAYD,EAAMlU,UACzBiU,EAAMjU,UAAUmU,GAAYD,EAAMlU,UAAUmU,EAEhD,CAYIlS,CAAO+R,EAAiBrT,OAAOC,KAAKwT,YACtC,ECGF,IAAYC,EAIXC,EAAAD,2BAAA,GAJWA,EAAAA,0BAAAA,EAAAA,sBAIX,CAAA,IAHC,iBAAA,kBACAA,EAAA,eAAA,gBACAA,EAAA,cAAA,QAGWE,MAAAA,EAAsDA,CACjEC,EACA1O,EACApF,KAEAA,EAAI+T,UAAU3O,EAAQ/D,OAAO,uHCZzB,cAA6BsD,EAMjC1D,YAAY2D,GAAA,IAAAoP,YAAEA,EAAc,IAAKC,SAAEA,EAAW,IAAErP,EAC9CE,MADU9F,EAAA4F,EAAA,CAAA,cAAA,cAHFrE,KAAQ2E,SAAc,GACtB3E,KAAA6Q,MAAQ,CAAEnE,MAAO,GAKzB1M,KAAKyT,YAAcA,EACnBzT,KAAK0T,SAAWA,CAClB,CAEOlP,UAAS7D,GAIC,IAJAC,QACfA,EAAOnB,IACPA,EAAGgC,oBACHA,GACed,EACf,MAAMkQ,EAAQ,CAAEnE,KAAMjN,EAAIiF,WAC1B,IAAIE,GAAU,EAOd,OANI5E,KAAK6Q,MAAMnE,MAAQ1M,KAAKkE,SAAW2M,EAAMnE,MAAQ1M,KAAKkE,UAGxDU,GAAWG,EAAM/E,KAAK6Q,MAAOA,IAE/B7Q,KAAK6Q,MAAQA,EACTpR,EAAIiF,WAAa1E,KAAKkE,QACjB,CACLS,SAAU3E,KAAKmE,KAAK,CAClBvD,YAEFgE,WAIG,CACLD,SAAU3E,KAAK6E,QAAQ,CACrBjE,QAASY,EACP/B,EACAgC,EACAb,EACAZ,KAAKsE,iBAEP7E,MACAgC,wBAGN,CAEUoD,QAAOd,GAIA,IAJCnD,QAChBA,EAAOnB,IACPA,EAAGgC,oBACHA,GACesC,EAMf,OALA/D,KAAK2E,SAAW,GAChB/D,EAAQ+S,SAAS/T,IACfI,KAAK4T,oBAAoBhU,EAAQH,EAAKgC,EAAoB,IAGrDzB,KAAK2E,QACd,CAEUiP,oBACRhU,EACAH,EACAsC,GAEA,IAAI0R,EAAczT,KAAKyT,YACnB5O,EAAmB,KAEvB,IAAK,IAAIzF,EAAI,EAAGA,EAAIY,KAAK2E,SAAStF,OAAQD,IAAK,CAC7C,MAAMyU,EAAY7T,KAAK2E,SAASvF,GAC1B0U,EAAWhR,EACf+Q,EAAU/S,OAAOG,YAAY8S,SAC7BxU,EAAYgB,YAAYX,GAAQmU,UAG9BD,EAAWL,IACbA,EAAcK,EACdjP,EAAUgP,EAEb,CAED,GACEhP,GACAjD,EACEiD,EAAQ/D,OACRiB,EACA/B,KAAK0T,UACL5R,SAASvC,EAAYgB,YAAYX,IAEnCiF,EAAQxD,KAAKzB,OACR,CACL,MAAMiF,EAAU,IAAIpE,EAAQ,CAAEG,QAAS,CAAChB,KACxCI,KAAK2E,SAAStD,KAAKwD,EACpB,CACH,qBD7EI,cAA+BkO,EAanCrS,YAAAC,GAOyB,IAPblB,IACVA,EAAGmB,QACHA,EAAU,GAAEoT,iBACZA,EAAmB,CAAE,EAAAC,UACrBA,EAAY,IAAIrD,EAAsBoD,GAAiBE,SACvDA,EAAW,IAAIzC,EAAiB0C,eAChCA,EAAiBb,GACM3S,EACvB4D,QACAvE,KAAKY,QAAU,IAAIA,GACnBZ,KAAK2E,SAAW,GAEhB3E,KAAKiU,UAAYA,EACjBjU,KAAKkU,SAAWA,EAEhBlU,KAAKmU,eAAiBA,EAElB1U,GACFO,KAAKE,OAAOT,EAEhB,CAEO2U,UAAUxU,EAAgByU,GAC3BrU,KAAKY,QAAQ0T,SAAS1U,KAI1BI,KAAKY,QAAQS,KAAKzB,GACbyU,GACHrU,KAAK0R,SAET,CAEO6C,WAAW3T,EAAmByT,GACnCzT,EAAQ+S,SAAS/T,IACfI,KAAKoU,UAAUxU,GAAQ,EAAK,IAGzByU,GACHrU,KAAK0R,QAET,CAEO8C,aAAa5U,EAAgByU,GAClC,MAAMxM,EAAQ7H,KAAKY,QAAQ1B,QAAQU,GAEnC,OAAe,IAAXiI,IAKJtI,EAAYW,OAAON,EAAQ,MAC3BI,KAAKY,QAAQ6T,OAAO5M,EAAO,GAEtBwM,GACHrU,KAAK0R,UAGA,EACT,CAEOgD,cAAc9T,EAAmByT,GACtC,IAAIM,GAAU,EAUd,OARA/T,EAAQ+S,SAAS/T,IACf+U,EAAU3U,KAAKwU,aAAa5U,GAAQ,IAAS+U,CAAO,IAGlDA,IAAYN,GACdrU,KAAK0R,SAGAiD,CACT,CAEOC,aAAaP,GAClBrU,KAAKY,QAAQvB,OAAS,EAEjBgV,GACHrU,KAAK0R,QAET,CAKOA,SACL,MAAMjS,EAAMO,KAAK6U,SACjB,GAAIpV,aAAeC,OAAOC,KAAKmV,KAAOrV,EAAIsV,gBAAiB,CACzDrV,OAAOC,KAAKqV,MAAMC,QAChBjV,KACAoT,wBAAsB8B,iBACtBlV,MAEF,MAAM2E,SAAEA,EAAQC,QAAEA,GAAY5E,KAAKiU,UAAUzP,UAAU,CACrD5D,QAASZ,KAAKY,QACdnB,MACAgC,oBAAqBzB,KAAK+U,kBAI5B,GAAInQ,GAAsBrD,MAAXqD,EAAsB,CAInC,MAAMuQ,EAAe,IAAIC,IACzB,IAAK,MAAMvQ,KAAWF,EACU,GAA1BE,EAAQjE,QAAQvB,QAClB8V,EAAavN,IAAI/C,EAAQjE,QAAQ,IAIrC,MAAMyU,EAAyB,GAE/B,IAAK,MAAMxQ,KAAW7E,KAAK2E,SACH,MAAlBE,EAAQjF,SAGkB,GAA1BiF,EAAQjE,QAAQvB,OACb8V,EAAaG,IAAIzQ,EAAQjF,SAI5BL,EAAYW,OAAO2E,EAAQjF,OAAQ,MAIrCyV,EAAahU,KAAKwD,EAAQjF,SAI9BI,KAAK2E,SAAWA,EAChB3E,KAAKuV,iBAGLC,uBAAsB,IACpBH,EAAa1B,SAAS/T,GAAWL,EAAYW,OAAON,EAAQ,SAE/D,CACDF,OAAOC,KAAKqV,MAAMC,QAChBjV,KACAoT,wBAAsBqC,eACtBzV,KAEH,CACH,CAEO0V,QACL1V,KAAK2V,aAAe3V,KAAK6U,SAASe,YAChC,OACA5V,KAAK0R,OAAOmE,KAAK7V,OAEnBA,KAAK0R,QACP,CAEOoE,WACLpW,OAAOC,KAAKqV,MAAMe,eAAe/V,KAAK2V,cACtC3V,KAAKgW,OACP,CAEUA,QACRhW,KAAKY,QAAQ+S,SAAS/T,GAAWL,EAAYW,OAAON,EAAQ,QAC5DI,KAAK2E,SAASgP,SAAS9O,GAAYA,EAAQvD,WAC3CtB,KAAK2E,SAAW,EAClB,CAEU4Q,iBAER,MAAM5D,EAAQ,IAAIP,EAAapR,KAAKY,QAASZ,KAAK2E,UAC5ClF,EAAMO,KAAK6U,SAEjB7U,KAAK2E,SAASgP,SAAS9O,IACU,IAA3BA,EAAQjE,QAAQvB,OAClBwF,EAAQjF,OAASiF,EAAQjE,QAAQ,IAGjCiE,EAAQjF,OAASI,KAAKkU,SAASxC,OAAO7M,EAAS8M,EAAOlS,GAEtDoF,EAAQjE,QAAQ+S,SAAS/T,GAAWL,EAAYW,OAAON,EAAQ,QAC3DI,KAAKmU,gBACPtP,EAAQjF,OAAOgW,YACb,SAECZ,IACCtV,OAAOC,KAAKqV,MAAMC,QAChBjV,KACAoT,wBAAsB6C,cACtBpR,GAEF7E,KAAKmU,eAAea,EAAOnQ,EAASpF,EAAI,KAKhDF,EAAYW,OAAO2E,EAAQjF,OAAQH,EAAI,GAE3C,mCExPI,cAA6BwE,EACjCvD,YAAY2D,GACVE,MADsB9F,EAAA4F,EAAZ,IAEZ,CACOG,UAAS7D,GAIC,IAJAC,QACfA,EAAOnB,IACPA,EAAGgC,oBACHA,GACed,EACf,MAAO,CACLgE,SAAU3E,KAAK6E,QAAQ,CAAEjE,UAASnB,MAAKgC,wBACvCmD,SAAS,EAEb,CAEUC,QAAQmM,GAChB,OAAOhR,KAAKmE,KAAK6M,EACnB,6DCCI,cAA6C5M,EAMjD1D,YAAY2D,GAAA,IAAAH,QACVA,EAAOsG,OACPA,EAAS,GAAElG,gBACXA,EAAkB,IAEUD,EADzB6G,EAAOzM,EAAA4F,EAJA,wCAMVE,MAAM,CAAEL,UAASI,oBAEjBtE,KAAK8Q,aAAe,IAAIC,iBACtB7M,QAASlE,KAAKkE,QACdsG,UACGU,IAGLlL,KAAK6Q,MAAQ,CAAEnE,MAAO,EAAGwJ,KAAM,CAAC,EAAG,EAAG,EAAG,GAC3C,CAEO1R,UAAUwM,GACf,MAAMH,EAAmC,CACvCnE,KAAMxJ,KAAKsM,MAAMwB,EAAMvR,IAAIiF,WAC3BwR,KAAM3T,EACJyO,EAAMvR,IAAIoC,YACVmP,EAAMvP,oBACNzB,KAAKsE,kBAIT,IAAIM,GAAWG,EAAM/E,KAAK6Q,MAAOA,GACjC,IAAK9L,EAAMiM,EAAMpQ,QAASZ,KAAKY,SAAU,CACvCgE,GAAU,EAEV5E,KAAKY,QAAU,IAAIoQ,EAAMpQ,SAEzB,MAAM6K,EAASzL,KAAKY,QAAQnB,KAAKG,IAC/B,MAAMO,EAAWZ,EAAYgB,YAAYX,GAEzC,MAAO,CACL2P,KAAM,UACN1D,SAAU,CACR0D,KAAM,QACNzD,YALgB,CAAC3L,EAASG,MAAOH,EAASE,QAO5CuO,WAAY,CAAEhP,UACf,IAEHI,KAAK8Q,aAAatF,KAAKC,EACxB,CAOD,OALI7G,IACF5E,KAAK2E,SAAW3E,KAAK6E,QAAQmM,GAC7BhR,KAAK6Q,MAAQA,GAGR,CAAElM,SAAU3E,KAAK2E,SAAUC,UACpC,CAEOC,QAAOlE,GAA6C,IAA5ClB,IAAEA,EAAGgC,oBAAEA,GAAqCd,EAEzD,MAAMkQ,EAAmC,CACvCnE,KAAMxJ,KAAKsM,MAAM/P,EAAIiF,WACrBwR,KAAM3T,EACJ9C,EAAIoC,YACJJ,EACAzB,KAAKsE,kBAIT,OAAOtE,KAAK8Q,aACTtE,YAAYqE,EAAMqF,KAAMrF,EAAMnE,MAC9BjN,KAAKwR,GACJjR,KAAKkR,iBAAiBD,IAE5B,CAEUC,iBAAgBnN,GAKW,IAJnC8H,UACEC,aAAcxL,EAAKD,IACpBuO,WACDA,GACmC7K,EACnC,GAAI6K,EAAW/J,QACb,OAAO,IAAIpE,EAAQ,CACjBG,QAASZ,KAAK8Q,aACXhD,UAAUc,EAAWC,WAAY5C,KACjCxM,KAAK0R,GAASA,EAAKvC,WAAWhP,SACjCO,SAAU,CAAEE,MAAKC,SAIrB,MAAMV,EAASgP,EAAWhP,OAE1B,OAAO,IAAIa,EAAQ,CACjBG,QAAS,CAAChB,GACVO,SAAUZ,EAAYgB,YAAYX,IAEtC"}